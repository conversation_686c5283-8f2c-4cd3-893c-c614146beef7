import React, { useRef, useEffect, useState, useMemo } from 'react';
import moment from 'moment';
// import { ICON_NAME } from '@common/Icon/enums';
// import Icon from '@common/Icon';
// import { getCookieValue } from 'utils';
// import { COOKIES } from 'utils/enum';
import styles from './index.scss';
// import ChartsContainer from './ChartsContainer';
import BlinkingCursor, { CURSOR_PLACEHOLDER } from './BlinkingCursor';
// import { quickFeedback, feedbackUpdate, getFeedbacks } from '../api';
// import { useToast } from '@common/Toast';

const buttonSample1 =
  'Based on your investment profile, I recommend these stocks for long-term growth:\n\n**Top Recommendations:**\n- Reliance Industries: Strong fundamentals\n- TCS: Consistent performer in IT sector\n- HDFC Bank: Leading private bank\n\n[[Buy Reliance Stocks]] [[Buy TCS Stocks]] [[Buy HDFC Bank Stocks]]\n\nWould you like detailed analysis of any specific stock?';

const chartSample =
  'Here\'s the current price chart for the stock:\n\n[[{"widget": "price-chart","display":"45.00","pml_id":**********}]]\n\nThe stock is currently trading at ₹45.00. [[Buy Now]] [[Set Alert]] [[View Analysis]]';

// Test samples for price-chart widget format (only price-chart will be matched)
const chartSample1 =
  '[[{"widget": "price-chart","display":"45.00","pml_id":**********}]]';
const chartSample2 =
  'Stock analysis: [[{"widget": "price-chart","display":"2847.50","pml_id":**********}]] - Strong performance this quarter!';
const chartSample3 =
  'Price chart: [[{"widget": "price-chart","display":"45.00","pml_id":**********}]] (other widgets like candlestick-chart will be ignored)';

const priceSample =
  'Here\'s the current live price for the stock:\n\n```display-price\n{\n\t"securityId": 22930,\n\t"segment": "E",\n\t"exchange": "NSE",\n\t"instrument_type": "ES"\n}\n```\n\nThe price is updated in real-time. [[Buy Now]] [[Set Price Alert]]';

// Test sample for price-broadcast widget format
const priceBroadcastSample =
  'Current stock price: [[{"widget": "price-broadcast","display":"45.00","pml_id":**********,"securityId": 22930, "segment": "E", "exchange": "NSE", "instrument_type": "ES"}]] - Live price updates!';

// Test sample for cta-prompt widget format
const ctaPromptSample =
  'Investment recommendation: [[{"widget": "cta-prompt","display":"Buy paytm stocks","pml_id":**********}]] - Click to proceed with the action.';

// Test sample for cta-deeplink widget format
const ctaDeeplinkSample =
  'Need more funds? [[{"widget": "cta-deeplink","display":" Add Funds ","deeplink":"https://paytmmoney.com/stocks/funds"}]] - Click to add funds to your account.';

// Utility to parse '2024-06-10 14:30' and return moment object
const parseDateTime = dateTimeStr => moment.utc(dateTimeStr).local();

const getDateLabel = dateMoment => {
  if (dateMoment.isSame(moment(), 'day')) return 'Today';
  if (dateMoment.isSame(moment().subtract(1, 'day'), 'day')) return 'Yesterday';
  return dateMoment.format('DD MMM YYYY');
};

// Example: messages from today and yesterday in the same chat

function flattenMessages(messages, condition) {
  return messages.reduce((acc, node) => {
    if (condition(node)) {
      acc.push(node);
    }

    if (node.steps?.length) {
      acc.push(...flattenMessages(node.steps, condition));
    }

    return acc;
  }, []);
}

// Dynamic ReactMarkdown Component
const DynamicReactMarkdown = ({ children, inlineParagraph = false }) => {
  const [ReactMarkdown, setReactMarkdown] = useState(null);
  const [remarkGfm, setRemarkGfm] = useState(null);
  const [loading, setLoading] = useState(true);
  const [visit, setVisit] = useState(null);

  // useEffect(() => {
  //   const loadMarkdown = async () => {
  //     try {
  //       const [visitModule] = await Promise.all([import('unist-util-visit')]);
  //       setVisit(() => visitModule.visit);
  //     } catch (err) {
  //       console.error('Failed to load markdown modules:', err);
  //     } finally {
  //       setLoading(false);
  //     }
  //   };

  //   if (typeof window !== 'undefined') {
  //     loadMarkdown();
  //   } else {
  //     setLoading(false);
  //   }
  // }, []);

  const cursorPlugin = () => tree => {
    if (!visit) return;
    visit(tree, 'text', (node, index, parent) => {
      const placeholderPattern = /\u200B/g;
      const matches = [...(node.value?.matchAll(placeholderPattern) || [])];

      if (matches.length > 0) {
        const newNodes = [];
        let lastIndex = 0;

        matches.forEach(match => {
          const [fullMatch] = match;
          const startIndex = match.index;
          const endIndex = startIndex + fullMatch.length;

          if (startIndex > lastIndex) {
            newNodes.push({
              type: 'text',
              value: node.value.slice(lastIndex, startIndex),
            });
          }

          newNodes.push({
            type: 'blinkingCursor',
            data: {
              hName: 'blinkingCursor',
              hProperties: { text: 'Blinking Cursor' },
            },
          });

          lastIndex = endIndex;
        });

        if (lastIndex < node.value.length) {
          newNodes.push({
            type: 'text',
            value: node.value.slice(lastIndex),
          });
        }

        parent.children.splice(index, 1, ...newNodes);
      }
    });
  };

  // useEffect(() => {
  //   const loadMarkdown = async () => {
  //     try {
  //       const [markdownModule, gfmModule] = await Promise.all([
  //         import('react-markdown'),
  //         import('remark-gfm'),
  //       ]);
  //       setReactMarkdown(() => markdownModule.default);
  //       setRemarkGfm(() => gfmModule.default);
  //     } catch (err) {
  //       console.error('Failed to load markdown modules:', err);
  //     } finally {
  //       setLoading(false);
  //     }
  //   };

  //   if (typeof window !== 'undefined') {
  //     loadMarkdown();
  //   } else {
  //     setLoading(false);
  //   }
  // }, []);

  if (loading) {
    return <div>{children}</div>;
  }

  if (!ReactMarkdown || !remarkGfm) {
    return <div>{children}</div>;
  }

  return (
    <ReactMarkdown
      remarkPlugins={[remarkGfm, cursorPlugin]}
      components={{
        blinkingCursor: () => <BlinkingCursor />,
        p: ({ node, ...props }) => (
          <p style={inlineParagraph ? { display: 'inline' } : {}} {...props} />
        ),
      }}
    >
      {children}
    </ReactMarkdown>
  );
};

// CTA Prompt Button Component - displays a styled button with pml_id context
const CtaPromptComponent = ({ display, pmlId, onClick, loading }) => {
  const handleClick = () => {
    console.log('CTA Prompt clicked:', { display, pmlId });
    if (onClick) {
      onClick(display, pmlId);
    }
  };

  return (
    <div className={styles.ctaPromptContainer}>
      <button
        disabled={loading}
        className={styles.ctaPromptButton}
        onClick={handleClick}
        data-pml-id={pmlId}
      >
        {display}
      </button>
    </div>
  );
};

// CTA Deeplink Button Component - displays a styled button that opens deeplink in new tab
const CtaDeeplinkComponent = ({ display, deeplink, loading }) => {
  const handleClick = () => {
    console.log('CTA Deeplink clicked:', { display, deeplink });
    if (deeplink) {
      // Open deeplink in new tab
      window.open(deeplink, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <div className={styles.ctaDeeplinkContainer}>
      <button
        disabled={loading}
        className={styles.ctaDeeplinkButton}
        onClick={handleClick}
        data-deeplink={deeplink}
      >
        {display}
        <span className={styles.ctaDeeplinkIcon}>↗</span>
      </button>
    </div>
  );
};

// Html Content Component - renders raw HTML from the 'data' key
const HtmlContentComponent = ({ data }) => {
  const containerRef = useRef(null);

  useEffect(() => {
    if (!containerRef.current) return;
    containerRef.current.innerHTML = '';

    // Parse the HTML string
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = data;

    // Separate scripts
    const scripts = Array.from(tempDiv.querySelectorAll('script'));
    const externalScripts = scripts.filter(s => s.src);
    const inlineScripts = scripts.filter(s => !s.src);

    // Append non-script nodes
    Array.from(tempDiv.childNodes).forEach(node => {
      if (node.nodeName !== 'SCRIPT') {
        containerRef.current.appendChild(node.cloneNode(true));
      }
    });

    // Helper to load external scripts sequentially
    const loadExternalScripts = (scriptsToLoad, cb) => {
      if (!scriptsToLoad.length) return cb();
      const [first, ...rest] = scriptsToLoad;
      const script = document.createElement('script');
      Array.from(first.attributes).forEach(attr =>
        script.setAttribute(attr.name, attr.value),
      );
      script.onload = () => loadExternalScripts(rest, cb);
      script.onerror = () => loadExternalScripts(rest, cb);
      containerRef.current.appendChild(script);
      // If script has inline content (rare for src scripts), add it
      if (first.text) script.text = first.text;
    };

    // After all external scripts are loaded, run inline scripts
    loadExternalScripts(externalScripts, () => {
      inlineScripts.forEach(oldScript => {
        const newScript = document.createElement('script');
        Array.from(oldScript.attributes).forEach(attr =>
          newScript.setAttribute(attr.name, attr.value),
        );
        newScript.text = oldScript.text;
        containerRef.current.appendChild(newScript);
      });
    });
  }, [data]);

  return <div ref={containerRef} style={{ width: '100%', height: '100%' }} />;
};

const STATIC_SUGGESTIONS = [
  'This was helpful',
  'Not relevant',
  'More details',
  'Too generic',
  'Show chart',
];

const ChatMessages = ({
  chainlitMessages,
  handleSend,
  loading,
  customMessages,
}) => {
  const chartContainerRef = useRef(null);
  const messagesEndRef = useRef(null);
  const [autoScroll, setAutoScroll] = useState(true);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const chatListRef = useRef(null);
  const prevMsgsRef = useRef();
  const [openSuggestionForMsgId, setOpenSuggestionForMsgId] = useState(null);
  const [feedbackSuggestions, setFeedbackSuggestions] = useState({
    positive_feedback: [],
    negative_feedback: [],
  });
  const [feedbackLoading, setFeedbackLoading] = useState(false);
  const [feedbackType, setFeedbackType] = useState(null);
  const [feedbackActive, setFeedbackActive] = useState({});
  // const { addToast } = useToast();

  const msgs = useMemo(() => {
    const messagesToProcess =
      customMessages && customMessages.length > 0
        ? customMessages
        : chainlitMessages;
    let lastDateTime = null;
    return (
      flattenMessages(
        messagesToProcess,
        m => m.type === 'user_message' || m.type === 'assistant_message',
      )?.map((msg, idx, arr) => {
        let dateTime;
        if (msg?.createdAt) {
          dateTime = new Date(msg.createdAt).toISOString();
        } else if (lastDateTime) {
          // Add 1 ms to the previous message's dateTime
          dateTime = new Date(
            new Date(lastDateTime).getTime() + 1,
          ).toISOString();
        } else {
          // Fallback to now if no previous dateTime
          dateTime = new Date().toISOString();
        }
        lastDateTime = dateTime;
        return {
          id: msg.id,
          sender: msg.name === 'user' ? 'user' : 'ai',
          message: msg?.output,
          dateTime,
          type: msg?.type,
          data: msg?.output,
        };
      }) || []
    );
  }, [chainlitMessages, customMessages]);

  // Track user scroll position to enable/disable auto-scroll
  useEffect(() => {
    const chatList = chatListRef.current;
    if (!chatList) return;
    const handleScroll = () => {
      // If user is within 100px of the bottom, enable auto-scroll
      const isAtBottom =
        chatList.scrollHeight - chatList.scrollTop - chatList.clientHeight <
        100;
      setAutoScroll(isAtBottom);
      setShowScrollToBottom(!isAtBottom);
    };
    chatList.addEventListener('scroll', handleScroll);
    return () => {
      chatList.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // // Fetch feedback suggestions on component mount
  // useEffect(() => {
  //   const fetchFeedbackSuggestions = async () => {
  //     try {
  //       setFeedbackLoading(true);
  //       const response = await getFeedbacks();
  //       setFeedbackSuggestions(response);
  //     } catch (error) {
  //       console.error('Error fetching feedback suggestions:', error);
  //     } finally {
  //       setFeedbackLoading(false);
  //     }
  //   };

  //   fetchFeedbackSuggestions();
  // }, []);

  // Scroll to bottom when messages change, but only if autoScroll is true
  useEffect(() => {
    const currentMsgs = msgs || [];
    const previousMsgs = prevMsgsRef.current || [];

    if (autoScroll) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    } else if (currentMsgs.length > previousMsgs.length) {
      // If auto-scroll is off, only scroll if the new message is from the user
      const lastMessage = currentMsgs[currentMsgs.length - 1];
      if (lastMessage.sender === 'user') {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }
    }
    prevMsgsRef.current = currentMsgs;
  }, [msgs, autoScroll]);

  const currentMsgs = msgs || [];
  const lastMessages = currentMsgs[currentMsgs.length - 1];

  // Group by date label using filtered messages
  const grouped = msgs.reduce((acc, msg) => {
    const m = parseDateTime(msg.dateTime);
    const label = getDateLabel(m);
    if (!acc[label]) acc[label] = [];
    acc[label].push({ ...msg, m });
    return acc;
  }, {});
  // Sort date labels by date descending (Today, Yesterday, ...)
  const dateLabels = Object.keys(grouped).sort((a, b) => {
    const ma = grouped[a][0].m;
    const mb = grouped[b][0].m;
    return mb.valueOf() - ma.valueOf();
  }); // Helper function to remove backticks from text content
  const removeBackticks = text => {
    if (typeof text !== 'string') return text;
    return text.replace(/`/g, '');
  };

  // Helper function to parse buttons in text
  const parseButtonsInText = text => {
    const buttonPattern = /\[\[([^\]]+)\]\]/g;
    const parts = [];
    let lastIndex = 0;
    let match;

    // eslint-disable-next-line no-cond-assign
    while ((match = buttonPattern.exec(text)) !== null) {
      // Add text before the button
      const textBefore = text.slice(lastIndex, match.index).trim();
      if (textBefore) {
        parts.push({
          type: 'text',
          content: removeBackticks(textBefore),
        });
      }

      // Add button
      parts.push({
        type: 'button',
        content: match[1], // Button text without brackets
      });

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text after last button
    const textAfter = text.slice(lastIndex).trim();
    if (textAfter) {
      parts.push({
        type: 'text',
        content: removeBackticks(textAfter),
      });
    }

    // If no buttons found, treat as regular text
    if (parts.length === 0 && text.trim()) {
      parts.push({
        type: 'text',
        content: removeBackticks(text),
      });
    }

    return parts;
  };

  const handleButtonClick = buttonText => {
    handleSend(buttonText);
  };

  // Clean up bullet points after component renders
  useEffect(() => {
    const cleanupBulletPoints = () => {
      // Find all list items that contain CTA containers
      const listItemsWithCTAs = document.querySelectorAll(
        'li:has(.ctaPromptContainer), li:has(.ctaDeeplinkContainer)',
      );
      listItemsWithCTAs.forEach(listItem => {
        const li = listItem;
        li.style.listStyle = 'none';
        li.style.listStyleType = 'none';
      });

      // Alternative approach for browsers that don't support :has()
      const ctaContainers = document.querySelectorAll(
        '.ctaPromptContainer, .ctaDeeplinkContainer',
      );
      ctaContainers.forEach(container => {
        const listItem = container.closest('li');
        if (listItem) {
          const li = listItem;
          li.style.listStyle = 'none';
          li.style.listStyleType = 'none';
        }
      });
    };

    // Run cleanup after a short delay to ensure DOM is fully rendered
    const timeoutId = setTimeout(cleanupBulletPoints, 100);

    return () => clearTimeout(timeoutId);
  }, [msgs]); // Re-run when messages change

  const handleScrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    setAutoScroll(true);
  };

  // Suggestion box component
  const SuggestionBox = ({ suggestions, onSelect, onClose }) => (
    <div className={styles.suggestionBoxOverlay}>
      <div className={styles.suggestionBox}>
        <div className={styles.suggestionBoxHeader}>
          <span>Why this feedback?</span>
          <button
            className={styles.suggestionBoxClose}
            onClick={onClose}
            aria-label="Close suggestions"
          >
            ×
          </button>
        </div>
        <div className={styles.suggestionTags}>
          {suggestions.map((suggestion, idx) => (
            <button
              key={idx}
              className={styles.suggestionTag}
              onClick={() => onSelect(suggestion)}
            >
              {suggestion}
            </button>
          ))}
        </div>
      </div>
    </div>
  );

  const renderMessage = (msg, isLastMessage = false) => {
    // Function to parse and render message with buttons, charts, and live prices
    const renderMessageWithButtons = message => {
      // Remove backticks from the message content first
      const cleanMessage = removeBackticks(message);
      // Regular expression to match ```display-price\n{...}\n``` pattern
      const pricePattern = /```display-price\s*\n([\s\S]*?)\n```/g;
      // Regular expression to match [[{...}]] pattern for chart format
      // This pattern specifically looks for "widget": "price-chart" as unique identifier
      const chartPattern = /\[\[(\{[^[\]]*"widget"\s*:\s*"price-chart"[^[\]]*\})\]\]/g;
      // Regular expression to match [[{...}]] pattern for price-broadcast format
      // This pattern specifically looks for "widget": "price-broadcast" as unique identifier
      const priceBroadcastPattern = /\[\[(\{[^[\]]*"widget"\s*:\s*"price-broadcast"[^[\]]*\})\]\]/g;
      // Regular expression to match [[{...}]] pattern for cta-prompt format
      // This pattern specifically looks for "widget": "cta-prompt" as unique identifier
      const ctaPromptPattern = /\[\[(\{[^[\]]*"widget"\s*:\s*"cta-prompt"[^[\]]*\})\]\]/g;
      // Regular expression to match [[{...}]] pattern for cta-deeplink format
      // This pattern specifically looks for "widget": "cta-deeplink" as unique identifier
      const ctaDeeplinkPattern = /\[\[(\{[^[\]]*"widget"\s*:\s*"cta-deeplink"[^[\]]*\})\]\]/g;
      // Regular expression to match [[{...}]] pattern for html-content widget
      // This pattern specifically looks for "widget": "html-content" as unique identifier
      const htmlContentPattern = /\[\[(\{[^\[\]]*"widget"\s*:\s*"html-content"[^\[\]]*\})\]\]/g;
      // Regular expression to match CTAs within backticks `[[{...}]]`
      // More flexible pattern to catch any widget type within backticks
      const ctaInBackticksPattern = /`\[\[(\{[^}]*"widget"[^}]*\})\]\]`/g;
      // Regular expression to match [[{...}]] pattern for compare-chart widget
      // This pattern specifically looks for "widget": "compare-chart" as unique identifier
      const compareChartsPattern = /\[\[(\{[^[\]]*"widget"\s*:\s*"compare-chart"[^[\]]*\})\]\]/g;

      const parts = [];
      let processedMessage = cleanMessage;

      // Process all patterns in order: cta-in-backticks first, then charts, price-broadcast, cta-prompt, cta-deeplink, html-content, prices, then buttons
      const allPatterns = [
        { pattern: ctaInBackticksPattern, type: 'cta-in-backticks' },
        { pattern: chartPattern, type: 'chart' },
        { pattern: priceBroadcastPattern, type: 'price-broadcast' },
        { pattern: ctaPromptPattern, type: 'cta-prompt' },
        { pattern: ctaDeeplinkPattern, type: 'cta-deeplink' },
        { pattern: htmlContentPattern, type: 'html-content' },
        { pattern: pricePattern, type: 'price' },
        { pattern: compareChartsPattern, type: 'compare-chart' },
      ];

      allPatterns.forEach(({ pattern, type }) => {
        const matches = [];
        let match;

        // Reset pattern index
        const newPattern = new RegExp(pattern.source, pattern.flags);

        // eslint-disable-next-line no-cond-assign
        while ((match = newPattern.exec(processedMessage)) !== null) {
          console.log(`Found ${type} match:`, match[0], 'Content:', match[1]);
          matches.push({
            start: match.index,
            end: match.index + match[0].length,
            content: match[1].trim(),
            fullMatch: match[0],
            type,
          });
        }

        // Process matches in reverse order to maintain indices
        matches.reverse().forEach(matchInfo => {
          try {
            const parsedData = JSON.parse(matchInfo.content);

            // Validate required fields for each type
            if (
              matchInfo.type === 'chart' &&
              (parsedData.widget !== 'price-chart' || !parsedData.pml_id)
            ) {
              return; // Skip invalid chart data
            }
            if (
              matchInfo.type === 'price-broadcast' &&
              (parsedData.widget !== 'price-broadcast' ||
                !parsedData.securityId ||
                !parsedData.exchange)
            ) {
              return; // Skip invalid price-broadcast data
            }
            if (
              matchInfo.type === 'cta-prompt' &&
              (parsedData.widget !== 'cta-prompt' || !parsedData.display)
            ) {
              return; // Skip invalid cta-prompt data
            }
            if (
              matchInfo.type === 'cta-deeplink' &&
              (parsedData.widget !== 'cta-deeplink' ||
                !parsedData.display ||
                !parsedData.deeplink)
            ) {
              return; // Skip invalid cta-deeplink data
            }
            if (
              matchInfo.type === 'html-content' &&
              (parsedData.widget !== 'html-content' || !parsedData.data)
            ) {
              return; // Skip invalid html-content data
            }
            if (
              matchInfo.type === 'cta-in-backticks' &&
              (!parsedData.widget || !parsedData.display)
            ) {
              return; // Skip invalid cta-in-backticks data
            }
            if (
              matchInfo.type === 'compare-chart' &&
              parsedData.widget !== 'compare-chart'
            ) {
              return; // Skip invalid html-content data
            }

            // Replace the pattern with a placeholder
            const placeholder = `__${type.toUpperCase()}_${Date.now()}_${Math.random()}__`;
            processedMessage =
              processedMessage.slice(0, matchInfo.start) +
              placeholder +
              processedMessage.slice(matchInfo.end);

            // Store the parsed data for later use
            parts.push({
              type: matchInfo.type,
              content: parsedData,
              placeholder,
            });
          } catch (error) {
            console.error(`Error parsing ${type} JSON:`, error);
          }
        });
      });

      // Now parse the remaining message for buttons and text
      const finalParts = parseButtonsInText(processedMessage);

      // Replace placeholders with actual components
      const result = [];
      finalParts.forEach(part => {
        if (part.type === 'text' && part.content) {
          // Check if this text contains any placeholders
          const placeholderMatches = parts.filter(p =>
            part.content.includes(p.placeholder),
          );

          if (placeholderMatches.length > 0) {
            let remainingText = part.content;

            // Sort placeholders by their position in the text to process them in order
            placeholderMatches.sort(
              (a, b) =>
                remainingText.indexOf(a.placeholder) -
                remainingText.indexOf(b.placeholder),
            );

            placeholderMatches.forEach(placeholderPart => {
              const placeholderIndex = remainingText.indexOf(
                placeholderPart.placeholder,
              );
              if (placeholderIndex === -1) return; // Skip if placeholder not found

              const beforePlaceholder = remainingText.substring(
                0,
                placeholderIndex,
              );
              const afterPlaceholder = remainingText.substring(
                placeholderIndex + placeholderPart.placeholder.length,
              );

              // Clean up markdown list markers before CTA placeholders
              let cleanedBefore = beforePlaceholder;
              // Remove list item markers that appear right before CTAs
              cleanedBefore = cleanedBefore
                .replace(/\*\s*$/, '')
                .replace(/-\s*$/, '')
                .replace(/\+\s*$/, '');
              // Remove numbered list markers
              cleanedBefore = cleanedBefore.replace(/\d+\.\s*$/, '');

              // Add text before placeholder
              if (cleanedBefore.trim()) {
                result.push({
                  type: 'text',
                  content: cleanedBefore,
                });
              }

              // Add the component
              result.push({
                type: placeholderPart.type,
                content: placeholderPart.content,
              });

              remainingText = afterPlaceholder;
            });

            // Add remaining text after last placeholder
            if (remainingText.trim()) {
              result.push({
                type: 'text',
                content: remainingText,
              });
            }
          } else {
            result.push(part);
          }
        } else {
          result.push(part);
        }
      });

      return result.length > 0
        ? result
        : [{ type: 'text', content: cleanMessage }];
    };

    const messageParts = renderMessageWithButtons(msg.message);
    console.log('ChatMessages - messageParts:', messageParts);
    // If all messageParts are empty or whitespace, do not render the message bubble
    const allPartsEmpty = messageParts.every(
      part =>
        !part.content ||
        (typeof part.content === 'string' && part.content.trim() === ''),
    );
    if (allPartsEmpty) return null;

    // Feedback buttons only for AI messages
    // const showFeedback = msg.sender === 'ai' && !loading;

    // const handleFeedbackClick = async (type) => {
    //   const current = feedbackActive[msg.id];
    //   // Toggle if same, else set new
    //   const newType = current === type ? null : type;
    //   setFeedbackActive((prev) => ({ ...prev, [msg.id]: newType }));
    //   setFeedbackType(newType);

    //   if (newType) {
    //     setOpenSuggestionForMsgId(msg.id);
    //     // Call quick feedback API
    //     const requestId = msg.id || `msg_${Date.now()}`;
    //     const userId = getCookieValue(COOKIES.PML_CB_USER_ID);
    //     try {
    //       await quickFeedback({
    //         request_id: requestId,
    //         reaction: newType === 'up' ? 'like' : 'dislike',
    //         user_id: userId,
    //       });
    //     } catch (error) {
    //       console.error('Error sending quick feedback:', error);
    //     }
    //   } else {
    //     setOpenSuggestionForMsgId(null);
    //     setFeedbackType(null);
    //   }
    // };

    // const handleSuggestionSelect = async (suggestion) => {
    //   try {
    //     // Call feedback update API with extended feedback
    //     const requestId = msg.id || `msg_${Date.now()}`;
    //     const userId = getCookieValue(COOKIES.PML_CB_USER_ID);
    //     await feedbackUpdate({
    //       user_id: userId,
    //       request_id: requestId,
    //       reaction: feedbackType === 'up' ? 'like' : 'dislike',
    //       extended_feedback: [suggestion],
    //     });
    //     addToast('Thank you for the Feedback.');
    //     setOpenSuggestionForMsgId(null);
    //     setFeedbackType(null);
    //     setFeedbackActive({});
    //   } catch (error) {
    //     console.error('Error sending feedback update:', error);
    //     addToast('Thank you for the Feedback.');
    //     setOpenSuggestionForMsgId(null);
    //     setFeedbackType(null);
    //   }
    // };

    // const handleSuggestionClose = () => {
    //   setOpenSuggestionForMsgId(null);
    //   setFeedbackType(null);
    //   setFeedbackActive({});
    // };

    return (
      <div className={styles.messageText}>
        {messageParts.map((part, index, arr) => {
          if (part.type === 'button') {
            return (
              <button
                disabled={loading}
                key={index}
                className={styles.messageButton}
                onClick={() => handleButtonClick(part.content)}
              >
                {part.content}
              </button>
            );
            // } if (part.type === 'chart') {
            //   return (
            //     <div
            //       key={index}
            //       className={styles.chartMessage}
            //       ref={chartContainerRef}
            //     >
            //       {/* <ChartsContainer
            //         pmlId={part.content.pml_id}
            //         stock={{
            //           change: 100,
            //           change_percent: 10,
            //           name: 'Stock Chart',
            //           ltp: parseFloat(part.content.display) || 0,
            //         }}
            //         stockRanges={[
            //           {
            //             code: '1d',
            //             name: '1 Day',
            //           },
            //         ]}
            //         widgetType={part.content.widget || 'price-chart'}
            //         index={0}
            //         timeRanges={[
            //           {
            //             code: '1d',
            //             name: '1 Day',
            //           },
            //         ]}
            //         setStockRanges={[
            //           {
            //             code: '1d',
            //             name: '1 Day',
            //           },
            //         ]}
            //         ranges={[
            //           {
            //             title: '1 Day',
            //             range: '1d',
            //             default: true,
            //           },
            //         ]}
            //         chartContainerRef={chartContainerRef}
            //       /> */}
            //     </div>
            //   );
            // } if (part.type === 'price-broadcast') {
            //   // Handle price-broadcast format [[{"widget": "price-broadcast","display":"45.00","pml_id":**********,"securityId": 22930, "segment": "E", "exchange": "NSE", "instrument_type": "ES"}]]
            //   return (
            //     <PriceBroadcastComponent
            //       key={index}
            //       securityId={part.content.securityId}
            //       segment={part.content.segment}
            //       exchange={part.content.exchange}
            //       instrumentType={part.content.instrument_type}
            //     />
            //   );
          }
          if (part.type === 'cta-prompt') {
            // Handle cta-prompt format [[{"widget": "cta-prompt","display":"Buy paytm stocks","pml_id":**********}]]
            return (
              <CtaPromptComponent
                key={index}
                display={part.content.display}
                pmlId={part.content.pml_id}
                onClick={handleButtonClick}
                loading={loading}
              />
            );
          }
          if (part.type === 'cta-deeplink') {
            // Handle cta-deeplink format [[{"widget": "cta-deeplink","display":" Add Funds ","deeplink":"https://paytmmoney.com/stocks/funds"}]]
            return (
              <CtaDeeplinkComponent
                key={index}
                display={part.content.display}
                deeplink={part.content.deeplink}
                loading={loading}
              />
            );
          }
          if (part.type === 'html-content') {
            // Handle html-content format [[{"widget": "html-content", "data": "<b>Some HTML</b>"}]]
            return (
              <HtmlContentComponent key={index} data={part.content.data} />
            );
          }
          if (part.type === 'cta-in-backticks') {
            // Handle cta-in-backticks format `[[{"widget": "cta-prompt","display":"..."}]]`
            if (part.content.widget === 'cta-prompt') {
              return (
                <CtaPromptComponent
                  key={index}
                  display={part.content.display}
                  pmlId={part.content.pml_id}
                  onClick={handleButtonClick}
                />
              );
            }
            if (part.content.widget === 'cta-deeplink') {
              return (
                <CtaDeeplinkComponent
                  key={index}
                  display={part.content.display}
                  deeplink={part.content.deeplink}
                />
              );
            }
            return null;
          }
          let { content } = part;
          let inlineParagraph = false;
          // Only a single text part between two buttons should be inline
          if (
            part.type === 'text' &&
            index > 0 &&
            index < arr.length - 1 &&
            arr[index - 1].type === 'button' &&
            arr[index + 1].type === 'button'
          ) {
            // Check if there are any other text parts between these two buttons
            const between = arr.slice(index - 1, index + 2);
            const textCount = between.filter(p => p.type === 'text').length;
            if (textCount === 1) {
              inlineParagraph = true;
            }
          }
          if (
            loading &&
            msg.sender === 'ai' &&
            isLastMessage &&
            index === messageParts.length - 1
          ) {
            content += CURSOR_PLACEHOLDER;
          }
          return (
            <DynamicReactMarkdown key={index} inlineParagraph={inlineParagraph}>
              {content}
            </DynamicReactMarkdown>
          );
        })}
        {/* Feedback buttons after AI message */}
      </div>
    );
  };

  return (
    <div className={styles.chatMessagesList} ref={chatListRef}>
      {dateLabels.map((label, labelIdx) => (
        <React.Fragment key={label}>
          <div className={styles.dateTag}>{label}</div>
          {grouped[label]
            .sort((a, b) => a.m.valueOf() - b.m.valueOf())
            .map((msg, index) => {
              // If this is the last message of the last date group and loading is true, show loader after it
              const isLastMessage =
                labelIdx === dateLabels.length - 1 &&
                index === grouped[label].length - 1;
              const { m } = msg;
              const rendered = renderMessage(msg, isLastMessage);
              if (!rendered) return null;
              return (
                <React.Fragment key={msg.id}>
                  <div
                    className={
                      msg.sender === 'user'
                        ? styles.userMessageBubble
                        : styles.aiMessageBubble
                    }
                  >
                    {rendered}
                    {(msg.sender === 'user' || !isLastMessage || !loading) && (
                      <div className={styles.messageTime}>
                        {m.format('h:mm A')}
                      </div>
                    )}
                  </div>
                </React.Fragment>
              );
            })
            .filter(Boolean)}
        </React.Fragment>
      ))}
      {/* Show blinking cursor if loading and no AI messages are present */}
      {loading && (!lastMessages || lastMessages.sender === 'user') && (
        <div className={styles.aiMessageBubble}>
          <BlinkingCursor />
        </div>
      )}
      <div ref={messagesEndRef} />
      {showScrollToBottom && (
        <button
          type="button"
          className={styles.scrollToBottomButton}
          onClick={handleScrollToBottom}
          aria-label="Scroll to bottom"
        >
          ↓
        </button>
      )}
    </div>
  );
};

export { CtaPromptComponent, CtaDeeplinkComponent };
export default ChatMessages;

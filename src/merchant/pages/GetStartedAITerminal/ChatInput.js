import React, { useState, useRef } from 'react';
import styles from './index.scss';

const ChatInput = ({
  onSend,
  onAttach,
  loading,
  stopTask,
}) => {
  const [value, setValue] = useState('');
  const fileInputRef = useRef();

  const handleSend = () => {
    if (value.trim() && !loading) {
      onSend(value);
      setValue('');
    }
  };

  const handleAttach = (e) => {
    if (e.target.files && e.target.files[0]) {
      onAttach(e.target.files[0]);
      e.target.value = '';
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleSend();
    }
  };

  return (
    <div className={styles.chatInputBar}>
      {/* <button
        className={styles.attachBtn}
        onClick={() => fileInputRef.current.click()}
        aria-label="Attach file"
        type="button"
      >
        <span className={styles.attachIcon}>📎</span>
      </button> */}
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        onChange={handleAttach}
      />
      <input
        className={styles.chatInput}
        type="text"
        placeholder="Ask about stocks, mutual funds, SIPs... ₹"
        value={value}
        onChange={(e) => setValue(e.target.value)}
        onKeyDown={handleKeyDown}
        disabled={loading}
      />
      {loading ? (
        <button
          className={styles.sendBtn}
          onClick={stopTask}
          aria-label="Stop"
          type="button"
        >
          stop
        </button>
      ) : (
        <button
          className={styles.sendBtn}
          onClick={handleSend}
          aria-label="Send message"
          type="button"
          disabled={loading}
        >
          <span className={styles.sendIcon}>➤</span>
        </button>
      )}
    </div>
  );
};

export default ChatInput;

// @import '@/styles/main';

:root {
  --background-color: #000000;
  --chat-history-bg: #1c1c1e;
  --border-color: #333333;
  --text-color-primary: #ffffff;
  --text-color-secondary: #a0a0a0;
  --chat-bubble-bg-ai: #282828;
  --chat-bubble-bg-user: #1c1c1e;
  --chat-bubble-text-user: #ffffff;
  --chat-input-bg: #1c1c1e;
  --chat-input-text: #ffffff;
  --hover-color: #3a3a3a;
  --selected-chat-border: #007bff;
  --selected-chat-bg: #3a3a3a;
  --shadow-color: rgba(0,0,0,0.06);
}

.light-theme {
  --background-color: #f5f5f5;
  --chat-history-bg: #ffffff;
  --border-color: #e0e0e0;
  --text-color-primary: #333333;
  --text-color-secondary: #666666;
  --chat-bubble-bg-ai: #e0e0e0;
  --chat-bubble-bg-user: #fff;
  --chat-bubble-text-user: #333;
  --chat-input-bg: #ffffff;
  --chat-input-text: #333333;
  --hover-color: #f0f0f0;
  --selected-chat-border: #007bff;
  --selected-chat-bg: #e6f2ff;
  --shadow-color: rgba(0,0,0,0.1);
}

.container {
  display: flex;
  padding: 0;
  max-width: 100%;
  margin: 0;
  height: calc(100vh); // Adjust based on your header height
  border-top: 1px solid var(--border-color);
}

.chatHistorySection {
  width: 100%;
  background: var(--chat-history-bg);
  display: flex;
  flex-direction: column;
  flex: 2; /* Allow it to grow and take 2 parts of available space */
  min-height: 0; /* Crucial for flex items to shrink properly when content overflows */
}

.watcherPanel {
  width: 100%; /* Match the width of chatHistorySection */
  background: var(--chat-history-bg);
  display: flex;
  flex-direction: column;
  flex: 1; /* Take remaining vertical space (1 part) */
  overflow-y: auto;
  border-top: 1px solid var(--border-color); /* Separator from chat history */
}

.leftPanel {
  width: 20%;
  min-width: 20%;
  max-width: 20%;
  display: flex;
  flex-direction: column;
  height: 100%;
  transition: width 0.3s, min-width 0.3s, max-width 0.3s, transform 0.3s;
  &.closed {
    width: 56px !important;
    min-width: 56px !important;
    max-width: 56px !important;
    overflow: hidden;
    transform: translateX(0);
    .chatHistorySection,
    .watcherPanel {
      display: none !important;
    }
  }
  &.open {
    width: 20%;
    min-width: 20%;
    max-width: 20%;
    transform: translateX(0);
  }
}

.leftPanelContentWrapper {
  flex: 1; /* Take up remaining vertical space */
  display: flex;
  flex-direction: column;
  min-height: 0; /* Allow content to scroll within */
}

.chatSection {
//   width: 74rem;
//   min-width: 74rem;
//   max-width: 74rem;
  flex: 1;
  background: var(--background-color);
  display: flex;
  flex-direction: column;
  position: relative;
}

.leftPanel.closed ~ .chatSection {
  width: 97rem;
  min-width: 97rem;
  max-width: 97rem;
}

.leftPanel.open ~ .chatSection {
  width: 74rem;
  min-width: 74rem;
  max-width: 74rem;
}

.watchlistSection {
  width: 40rem;
  min-width: 40rem;
  max-width: 40rem;
  background: var(--chat-history-bg);
  display: flex;
  flex-direction: column;

  border-right: 1px solid var(--border-color);
}

.watchlist{
  padding:2rem;
  overflow: scroll;
  height: 90%;

} 

.aiTerminalWatchlistTab{
  // overflow: scroll;
  // height: 46%;
}

.aiTerminalWatchlist{
  height: calc(100vh - 14rem);
}

.sectionHeader {
  padding: 2rem 2rem 1rem 2rem;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-color-secondary);
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--border-color);
  background: var(--chat-history-bg);
  display: flex;
  justify-content: space-between;
  overflow-x: scroll;
  &::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for Firefox */
  scrollbar-width: none;
  /* Hide scrollbar for IE, Edge */
  -ms-overflow-style: none;
}

.sectionContent {
  flex: 1;
  padding: 2rem;
  overflow-y: auto; /* This is where the scroll should happen */
  color: var(--text-color-primary);
  min-height: 0; /* Crucial for flex items to shrink properly when content overflows */
  height: 100%; /* Explicitly set height to ensure it fills parent and enables scrolling */
}

.activeTab{
  text-decoration: underline;
  text-decoration-color: var(--selected-chat-border);
  text-underline-offset: 10px;
}

.mainContent {
  flex: 1;
  padding-right: 20px;
  overflow-y: auto;

  h1 {
    font-size: 24px;
    margin-bottom: 20px;
    color: var(--text-color-primary);
  }

  .content {
    background: var(--chat-history-bg);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px var(--shadow-color);
  }
}

.watchlistContainer {
  width: 27%;
  min-width: 27%;
  max-width: 27%;
  overflow-y: auto;

  @media only screen and (min-width: 1366px) {
    width: 36rem;
    min-width: 36rem;
    max-width: 36rem;
  }
}

.chatHistoryList {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
  background-color: #121212;
  min-height: 0;
}

.chatHistoryBox {
  background: none;
  border-radius: 0.7rem;
  padding: 1.2rem 1.5rem;
  margin-bottom: 0.2rem;
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
  box-shadow: none;
  border: 1.5px solid transparent;
}

.chatHistoryBox:hover {
  background: var(--hover-color);
}

.selectedChatBox {
  border: 1.5px solid var(--selected-chat-border);
  background: var(--selected-chat-bg);
  box-shadow: 0 2px 8px var(--shadow-color);
}

.chatHistoryTitle {
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--text-color-primary);
  margin-bottom: 0.2rem;
}

.chatHistoryDate {
  font-size: 1rem;
  color: var(--text-color-secondary);
  margin-bottom: 0.3rem;
}

.chatHistoryPreview {
  font-size: 1.2rem;
  color: var(--text-color-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chatMessagesList {
  display: flex;
  flex-direction: column;
  height: 80%;
  overflow-y: auto;
  padding-bottom: 5.5rem;

  /* Hide scrollbar for Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }
}

.userMessageBubble {
  align-self: flex-end;
  // background: linear-gradient(90deg, #000000 0%, #333333 100%);
  background: var(--chat-bubble-bg-user);
  color: var(--chat-bubble-text-user);
  padding: 1rem 1.4rem;
  border-radius: 1.2rem 1.2rem 0.2rem 1.2rem;
  max-width: 80%;
  box-shadow: 0 2px 8px var(--shadow-color);
  font-size: 1.05rem;
  position: relative;
}

.aiMessageBubble {
  // align-self: flex-start;
  // background: var(--chat-bubble-bg-ai);
  color: var(--text-color-primary);
  padding: 1rem 1.4rem;
  border-radius: 1.2rem 1.2rem 1.2rem 0.2rem;
  max-width: 100%;
  box-shadow: 0 2px 8px var(--shadow-color);
  font-size: 1.05rem;
  position: relative;
}

.messageText {
  margin-bottom: 0.4rem;
  font-size: 1.5rem;

  // Markdown table styling
  table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
    background: var(--chat-history-bg);
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 8px var(--shadow-color);
  }

  th, td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
  }

  th {
    // background: var(--chat-bubble-bg-ai);
    font-weight: 600;
    color: var(--text-color-primary);
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  td {
    color: var(--text-color-primary);
    font-size: 1.2rem;
  }

  tr:hover {
    background: var(--hover-color);
  }

  tr:last-child td {
    border-bottom: none;
  }

  // Markdown styling for other elements
  h1, h2, h3, h4, h5, h6 {
    color: var(--text-color-primary);
    margin: 1rem 0 0.5rem 0;
  }

  p {
    margin: 0.5rem 0;
    line-height: 1.6;
  }

  ul, ol {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
  }

  li {
    margin: 0.25rem 0;
    line-height: 1.5;
  }

  strong {
    font-weight: 600;
    color: var(--text-color-primary);
  }

  em {
    font-style: italic;
    color: var(--text-color-secondary);
  }

  code {
    // background: var(--chat-bubble-bg-ai);
    padding: 0.2rem 0.4rem;
    border-radius: 0.3rem;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
  }

  pre {
    // background: var(--chat-bubble-bg-ai);
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1rem 0;
  }

  blockquote {
    border-left: 3px solid var(--selected-chat-border);
    padding-left: 1rem;
    margin: 1rem 0;
    color: var(--text-color-secondary);
    font-style: italic;
  }
}

/* Feedback buttons for AI messages */
.feedbackButtons {
  display: flex;
  gap: 1.2rem;
  margin-top: 0.5rem;
  margin-bottom: 0.2rem;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
}

.feedbackButton {
  background: transparent;
  border: none;
  font-size: 1.6rem;
  cursor: pointer;
  color: var(--text-color-secondary);
  transition: color 0.2s, transform 0.1s;
  padding: 0.3rem 0.7rem;
  border-radius: 50%;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  min-width: 40px;
  min-height: 40px;

  &:hover {
    background: var(--hover-color);
    transform: scale(1.15);
  }

  &:active {
    background: var(--selected-chat-bg);
    transform: scale(0.98);
  }

  &.active {
    background: var(--selected-chat-bg);
    transform: scale(0.98);
  }
}

/* Suggestion box overlay and popup */
.suggestionBoxOverlay {
  margin-top: 10px;
  background: rgba(0,0,0,0.18);
}
.suggestionBox {
  background: #121212;
  border-radius: 1.2rem;
  box-shadow: 0 8px 32px var(--shadow-color);
  padding: 2rem 2.5rem 1.5rem 2.5rem;
  min-width: 320px;
  max-width: 90vw;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
}
.suggestionBoxHeader {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color-primary);
  margin-bottom: 1.2rem;
}
.suggestionBoxClose {
  background: none;
  border: none;
  color: var(--text-color-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.2rem 0.7rem;
  border-radius: 50%;
  transition: background 0.2s, color 0.2s;
  &:hover {
    background: var(--hover-color);
    color: #ff4d4f;
  }
}
.suggestionTags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.7rem;
  width: 100%;
}
.suggestionTag {
  background: var(--chat-bubble-bg-user);
  color: var(--chat-bubble-text-user);
  border: none;
  padding: 0.6rem 1.2rem;
  border-radius: 1.2rem;
  font-size: 0.98rem;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
  box-shadow: 0 2px 8px var(--shadow-color);
  // &:hover {
  //   background: #007bff;
  //   color: #fff;
  //   transform: translateY(-2px) scale(1.07);
  //   box-shadow: 0 4px 16px var(--shadow-color);
  // }
  // &:active {
  //   background: #0056b3;
  //   color: #fff;
  //   transform: scale(0.98);
  // }
}

.messageButton {
  background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
  color: #ffffff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 1.2rem;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  margin: 0.5rem 0.5rem 0.5rem 0;
  display: inline-block;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
  letter-spacing: 0.02em;

  &:hover {
    background: linear-gradient(90deg, #0056b3 0%, #007bff 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 123, 255, 0.2);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.3);
  }
}

.chartContainer {
  width: 100%;
  height: 400px;
  background: var(--chat-history-bg);
  border: 1px solid var(--border-color);
  border-radius: 0.8rem;
  margin: 1rem 0;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color-secondary);
  font-size: 1rem;
  box-shadow: 0 2px 8px var(--shadow-color);
  position: relative;

  &::before {
    content: '📈';
    font-size: 2rem;
    margin-right: 0.5rem;
  }

  // You can add more specific styling when actual chart is implemented
  &[data-pml-id] {
    min-height: 350px;
  }
}

.livePriceContainer {
  // background: var(--chat-history-bg);
  border: 1px solid var(--border-color);
  border-radius: 0.8rem;
  padding: 1.5rem;
  margin: 1rem 0;
  box-shadow: 0 2px 8px var(--shadow-color);

  .priceHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    .securityLabel {
      font-size: 0.9rem;
      color: var(--text-color-secondary);
      font-weight: 500;
    }

    .exchangeLabel {
      background: var(--selected-chat-border);
      color: white;
      padding: 0.2rem 0.6rem;
      border-radius: 0.4rem;
      font-size: 0.8rem;
      font-weight: 600;
    }
  }

  .priceDisplay {
    display: flex;
    align-items: baseline;
    gap: 1rem;

    .currentPrice {
      font-size: 2rem;
      font-weight: 700;
      color: var(--text-color-primary);
      font-family: 'Courier New', monospace;
    }

    .priceChange {
      font-size: 1.1rem;
      font-weight: 600;
      padding: 0.3rem 0.8rem;
      border-radius: 0.5rem;

      &.positive {
        background: rgba(34, 197, 94, 0.1);
        color: #22c55e;
      }

      &.negative {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
      }
    }
  }
}

// Inline CTA Button Styling for React Markdown
.messageText {
  // Hide bullet points that appear near CTA containers - comprehensive approach
  .ctaPromptContainer,
  .ctaDeeplinkContainer {
    position: relative;

    &::before,
    &::after {
      display: none !important;
    }
  }

  // Target all list items and hide bullets for those containing CTAs
  ul, ol {
    li {
      // Hide bullets for list items containing CTA containers
      &:has(.ctaPromptContainer),
      &:has(.ctaDeeplinkContainer) {
        list-style: none !important;
        list-style-type: none !important;

        &::before,
        &::after,
        &::marker {
          display: none !important;
        }
      }

      // Also target direct children
      .ctaPromptContainer,
      .ctaDeeplinkContainer {
        &::before {
          content: '';
          position: absolute;
          left: -2rem;
          width: 2rem;
          height: 100%;
          background: var(--background-color);
          z-index: 10;
        }
      }
    }
  }

  // More aggressive approach - hide all list styling when CTAs are present
  ul:has(.ctaPromptContainer) li,
  ul:has(.ctaDeeplinkContainer) li,
  ol:has(.ctaPromptContainer) li,
  ol:has(.ctaDeeplinkContainer) li {
    list-style: none !important;
    list-style-type: none !important;

    &::marker {
      display: none !important;
    }
  }

  // More aggressive approach to hide bullet points near CTAs
  ul, ol {
    li {
      // If a list item contains a CTA container, hide its bullet
      &:has(.ctaPromptContainer),
      &:has(.ctaDeeplinkContainer) {
        list-style: none !important;
        position: relative;

        &::before,
        &::marker {
          display: none !important;
        }
      }
    }
  }

  // Alternative approach - hide bullets for any list item that has CTA content
  li:has(.ctaPromptContainer)::marker,
  li:has(.ctaDeeplinkContainer)::marker {
    display: none !important;
  }

  // Fallback - hide bullets on list items containing CTAs
  li {
    &:has(.ctaPromptContainer),
    &:has(.ctaDeeplinkContainer) {
      list-style-type: none !important;

      &::before {
        display: none !important;
      }
    }
  }

  button.cta-prompt-button,
  button.cta-deeplink-button {
    display: inline-block;
    margin: 0.25rem 0.25rem 0.25rem 0;
    padding: 0.5rem 1rem;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;

    &:hover {
      background: #0056b3;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
    }

    &:active {
      transform: translateY(0);
    }
  }

  button.cta-deeplink-button {
    background: #28a745;

    &:hover {
      background: #1e7e34;
      box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    }
  }
}

.messageTime {
  font-size: 0.85rem;
  color: var(--text-color-secondary);
  text-align: right;
}

.chartMessage{
  // max-height: 350px;
  margin-bottom: 2rem;
}

.inputBarWrapper {
  width: 90%;
  padding: 0 1rem 1rem 1rem;
  background: transparent;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 2;
}

.chatInputBar {
  display: flex;
  align-items: center;
  background: var(--chat-input-bg);
  border-radius: 2.2rem;
  padding: 0.6rem 1.2rem;
  box-shadow: 0 2px 8px var(--shadow-color);
  gap: 1rem;
}

.attachBtn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.5rem;
  color: var(--text-color-secondary);
  padding: 0 0.5rem;
  border-radius: 50%;
  transition: background 0.2s;
}
.attachBtn:hover {
  background: var(--hover-color);
}
.attachIcon {
  font-size: 1.4rem;
}

.chatInput {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: var(--chat-input-text);
  font-size: 1.1rem;
  padding: 0.7rem 0.5rem;
}

.sendBtn {
  background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
  border: none;
  color: #fff;
  font-size: 1.3rem;
  border-radius: 50%;
  width: 2.7rem;
  height: 2.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0,123,255,0.18);
  transition: background 0.2s, box-shadow 0.2s;
}
.sendBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.sendBtn:hover {
  background: linear-gradient(90deg, #0056b3 0%, #007bff 100%);
}
.sendIcon {
  font-size: 1.3rem;
}

.aiBadge {
  margin-left: 0.7rem;
  background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
  color: #fff;
  font-size: 0.95rem;
  font-weight: 600;
  border-radius: 0.7rem;
  padding: 0.18rem 0.9rem;
  letter-spacing: 0.04em;
  vertical-align: middle;
  display: flex;
  justify-content: center;
  align-items: center;
}

.gptBadge {
  background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
  color: #fff;
  font-size: 0.95rem;
  font-weight: 600;
  border-radius: 0.7rem;
  padding: 0.18rem 0.9rem;
  letter-spacing: 0.04em;
  vertical-align: middle;
  width: 47%;
  margin-top: 5px;
}

.rupeeIcon {
  margin-left: 1.2rem;
  color: #007bff;
  font-size: 1.3rem;
  font-weight: 700;
  vertical-align: middle;
}

.dateTag {
  display: flex;
  align-items: center;
  justify-content: center;
  // background: var(--chat-bubble-bg-ai);
  color: var(--text-color-secondary);
  font-size: 0.98rem;
  font-weight: 600;
  border-radius: 1.2rem;
  padding: 0.3rem 1.2rem;
  margin: 1.2rem auto 0.7rem auto;
  width: fit-content;
  box-shadow: 0 2px 8px var(--shadow-color);
  letter-spacing: 0.04em;
}

.chatMessage {
  height: 300px;
  width: 100%;
  max-width: 800px;
  background: transparent;
  margin: 0 auto;
}

.livePrice {
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--text-color-primary);
  padding: 8px;
  margin-bottom: 8px;
  background: var(--background-color);
  border-radius: 4px;
  text-align: center;
}

.suggestionsContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  padding: 1rem;
  // background: var(--chat-bubble-bg-ai);
  border-radius: 1.2rem;
  margin-bottom: 0.4rem;
  box-shadow: 0 2px 8px var(--shadow-color);
}

.suggestionTagWrapper {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  background: var(--chat-bubble-bg-user);
  border-radius: 1.2rem;
  padding: 0.2rem;
  box-shadow: 0 2px 4px var(--shadow-color);
}

.suggestionTag {
  background: var(--chat-bubble-bg-user);
  color: var(--chat-bubble-text-user);
  border: none;
  padding: 0.6rem 1.2rem;
  border-radius: 1.2rem;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px var(--shadow-color);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px var(--shadow-color);
  }
}

.removeSuggestion {
  background: none;
  border: none;
  color: var(--text-color-secondary);
  font-size: 1.2rem;
  width: 1.8rem;
  height: 1.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
  margin-right: 0.2rem;

  &:hover {
    background: var(--hover-color);
    color: var(--text-color-primary);
  }
}

.dotLoaderContainer {
  display: inline-flex;
  align-items: center;
  margin-left: 4px;
  margin-right: 2px;
  vertical-align: middle;
  position: relative;
  top: -1px;
  line-height: 1;
}
.dotLoader {
  width: 14px;
  height: 14px;
  background-color: var(--text-color-primary);
  border-radius: 50%;
  display: inline-block;
  animation: blink 1s ease-in-out infinite;
  flex-shrink: 0;
}
@keyframes blink {
  0% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}

// Price Broadcast Container - similar to livePriceContainer but with additional styling
.priceBroadcastContainer {
  // background: var(--chat-history-bg);
  // border: 1px solid var(--border-color);
  // border-radius: 0.8rem;
  // padding: 1.5rem;
  // margin: 1rem 0;
  box-shadow: 0 2px 8px var(--shadow-color);
  //border-left: 4px solid #007bff; // Blue accent to distinguish from regular price display

  .priceHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;

    .securityLabel {
      font-size: 0.9rem;
      color: var(--text-color-secondary);
      font-weight: 500;
    }

    .exchangeLabel {
      background: var(--selected-chat-border);
      color: white;
      padding: 0.2rem 0.6rem;
      border-radius: 0.4rem;
      font-size: 0.8rem;
      font-weight: 600;
    }

    .pmlIdLabel {
      background: #007bff;
      color: white;
      padding: 0.2rem 0.6rem;
      border-radius: 0.4rem;
      font-size: 0.8rem;
      font-weight: 600;
    }
  }

  .priceDisplay {
    display: flex;
    align-items: baseline;
    gap: .5rem;

    .currentPrice {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--text-color-primary);
      font-family: 'Courier New', monospace;
    }

    .priceChange {
      font-size: 1.1rem;
      font-weight: 600;
      padding: 0.3rem 0.8rem;
      border-radius: 0.5rem;

      &.positive {
        background: rgba(34, 197, 94, 0.1);
        color: #22c55e;
      }

      &.negative {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
      }
    }
  }
}

// CTA Prompt Button Container
.ctaPromptContainer {
  margin: 1rem 0;
  display: flex;
  justify-content: flex-start;

  // Hide any bullet points that might appear near CTA buttons
  &::before {
    display: none !important;
  }
}

.ctaPromptButton {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border: none;
  border-radius: 0.8rem;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 0.8rem;
  min-height: 3rem;
  position: relative;
  overflow: hidden;

  &:hover {
    background: linear-gradient(135deg, #0056b3 0%, #007bff 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }

  .ctaPmlId {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.2rem 0.6rem;
    border-radius: 0.4rem;
    font-size: 0.8rem;
    font-weight: 500;
    margin-left: auto;
  }
}

// CTA Deeplink Button Container
.ctaDeeplinkContainer {
  margin: 1rem 0;
  display: flex;
  justify-content: flex-start;

  // Hide any bullet points that might appear near CTA buttons
  &::before {
    display: none !important;
  }
}

.ctaDeeplinkButton {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 0.8rem;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
  display: flex;
  align-items: center;
  gap: 0.8rem;
  min-height: 3rem;
  position: relative;
  overflow: hidden;

  &:hover {
    background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }

  .ctaDeeplinkIcon {
    font-size: 1.2rem;
    font-weight: bold;
    margin-left: auto;
    opacity: 0.8;
    transition: opacity 0.3s ease;
  }

  &:hover .ctaDeeplinkIcon {
    opacity: 1;
  }
}


.loaderCircle {
  width: 27px;
  height: 27px;
  background: var(--background-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loaderSquare {
  width: 8px;
  height: 8px;
  background: var(--text-color-primary);
}



.scrollToBottomButton {
  position: absolute;
  bottom: 70px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--chat-input-bg);
  border: 1px solid var(--chat-input-bg);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  font-size: 14px;
  line-height: 30px;
  text-align: center;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 10;
  color: var(--chat-input-text);;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chatHistoryWrapper {
  display: flex;
  flex-direction: column;
}

.chatHistoryHeading {
  padding: 12px 16px;
  font-size: 13px;
  font-weight: 600;
  color: #a0a0a0;
  text-transform: uppercase;
  border-bottom: 1px solid #2a2a2a;
}

.chatHistoryBox {
  padding: 10px 16px;
  cursor: pointer;
  color: #e0e0e0;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.chatHistoryBox:hover {
  background-color: #2a2a2a;
}

.selectedChatBox {
  background-color: #353535;
  color: #fff;
}

.chatHistoryPreview {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

}

.newChatButton {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  margin: 10px;
  width: calc(100% - 20px); /* Adjust width to account for margin */
  box-sizing: border-box; /* Include padding and border in the element's total width and height */

  &:hover {
    background-color: #0056b3;
  }
}

.transparentButton {
  background-color: transparent;
  color: var(--text-color-primary);
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px; /* Space between icon and text */
  font-size: 1.6rem;
  font-weight: 600;
  padding: 2rem 2rem 2rem 1.5rem; /* Match section header padding */
  width: 100%;
  justify-content: flex-start;
}

.chatHistoryHeader{
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.sideNav{
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.toolbars{
  margin-top: 10px;
  gap: 25px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.sideCollapse {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 56px;
  width: 100%;
  padding-left: 0.5rem;
}

.sidebarToggleButton {
  // position: absolute;
  top: 16px;
  left: 0;
  z-index: 100;
  background: transparent;
  border: none;
  color: var(--text-color-primary);
  font-size: 2rem;
  padding: 0.5rem 1rem;
  border-radius: 0 8px 8px 0;
  cursor: pointer;
  box-shadow: 0 2px 8px var(--shadow-color);
  transition: background 0.2s;
}

.icon {
  width: 20px !important;
  height: 20px !important;
  min-width: 20px;
  min-height: 20px;
  max-width: 20px;
  max-height: 20px;
  display: block;
}

.leftPanel.closed .transparentButton {
  width: 100%;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.leftPanel.closed .icon {
  margin: 0 auto;
}

.leftPanel.closed .sideCollapse {
  background: var(--chat-history-bg);
  border-right: 1px solid var(--border-color);
  height: 100%;
  width: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.iconStack {
  position: relative;
  padding-top: 8px;
  display: inline-block;
  width: 32px;
  height: 32px;
}

.baseIcon {
  position: absolute;
  top: 0;
  left: 0;
  width: 32px;
  height: 32px;
  z-index: 1;
  opacity: 1;
  transition: opacity 0.2s;
}

.expandIcon {
  position: absolute;
  top: 0;
  left: 0;
  width: 32px;
  height: 32px;
  z-index: 2;
  opacity: 0;
  transform: scale(0.5);
  transition: opacity 0.2s, transform 0.2s;
}

.group:hover .expandIcon {
  opacity: 1;
  transform: scale(1);
}

.group:hover .baseIcon {
  opacity: 0.2;
}


.nextBtn {
    padding: 0 15px;
    margin-top: 20px;
    .buttonStyle {
      border-radius: 4px;
    }
  }
  
  .headerContainer {
    padding: 52.5px 15px 0px;
    display: flex;
    flex-direction: column;
    align-items: center;
  
    .headerImgContainer {
      width: 127px;
  
      img {
        width: 100%;
        height: 100%;
      }
    }
  
    .name {
      @include typography(title3B, map-get($colors, DGrey));
      text-align: center;
      margin-top: 114px;
    }
    .passcodeTextContainer {
      text-align: center;
      margin: 0 42px;
      .enterPasscodeText {
        @include typography(text1R, map-get($colors, DGrey));
        margin-top: 25px;
      }
    }
  }
  
  .forgotPasscode {
    @include typography(text1R, map-get($colors, DBlue));
    text-align: center;
    margin-top: 25px;
  }
  
  .contentContainer {
    height: 91vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .banerContainer {
    width: 100%;
    height: 187px;
  
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .tncAcceptContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40px;
    padding: 0 15px;
  
    .tncAccept {
      @include typography(text1R, map-get($colors, Grey7));
      margin-left: 8px;
    }
  }
  
  .resendContainer {
    text-align: center;
    margin-top: 25px;
  }
  
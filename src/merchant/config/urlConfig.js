import { PF_AUTH_HOST, PF_HOST } from '../../config/urlConfig';

export const MERCHANT_AUTHORIZATION = {
  GET_STARTED: '/merchant-authorization',
  STRATEGY_LANDING: '/strategy-landing-dashboard',
};

export const AI_TERMINAL_AUTHORIZATION = {
  GET_STARTED: '/ai-terminal',
};

export const MERCHANT_API_URL = {
  MERCHANT_AUTH_TOKEN: (
    deviceId,
    deviceManufacturer,
    deviceName,
    client,
    version,
    osVersion,
  ) =>
    `${PF_AUTH_HOST}api/auth/generate-merchant-jwt?deviceIdentifier=${deviceId}&deviceManufacturer=${deviceManufacturer}&deviceName=${deviceName}&client=${client}&version=${version}&osVersion=${osVersion}`,
  VALIDATE_PASSCODE: requestToken =>
    `${PF_HOST}merchantonboarding/v1/token/request/${requestToken}/passcode/valid`,
  AUTHORIZE_REQUEST_TOKEN: () =>
    `${PF_HOST}merchant-auth/authorisation/v1/user/merchant/permissions/authorise`,
  GET_MERCHANT_PERMISSIONS: requestToken =>
    `${PF_HOST}merchant-auth/authorisation/v1/user/permissions?requestToken=${requestToken}`,
  CHECK_LOGIN_TYPE: userId => `${PF_HOST}2fa/totp/user/${userId}`,
  SEND_OTP: userId => `${PF_HOST}2fa/totp/user/${userId}/otp/send`,
  VERIFY_OTP: requestToken =>
    `${PF_HOST}merchantonboarding/v1/token/request/${requestToken}/otp/valid`,
  VERIFY_TOTP: requestToken =>
    `${PF_HOST}merchantonboarding/v1/token/request/${requestToken}/totp/valid`,
  GET_TRADERON_COHORT: () => `${PF_AUTH_HOST}api/agg/getAccess`,
};

export const MERCHANT_API_MASKED_URL = {
  VALIDATE_PASSCODE: `${PF_HOST}merchantonboarding/v1/token/request/######/passcode/valid`,
  GET_MERCHANT_PERMISSIONS: `${PF_HOST}merchant-auth/authorisation/v1/user/permissions?requestToken=######`,
  VERIFY_OTP: `${PF_HOST}merchantonboarding/v1/token/request/######/otp/valid`,
  VERIFY_TOTP: `${PF_HOST}merchantonboarding/v1/token/request/######/totp/valid`,
};

export const AI_TERMINAL = {
  MERCHANT_ID: '6043',
  MERCHANT_NAME: 'AI Terminal Preprod',
  API_KEY: '375d640b70c24840b9ddd59be896a1d5',
  API_SECRET: '401b6767679a43c6a7eb51c20665f967',
  BASE_URL: 'https://developer.paytmmoney.com',
  RECOMMENDATION_NUDGER: 'https://nudger.paytmmoney.com',
  PML_AGENT_ORCHESTRATOR_BASE_URL: 'https://pml-agent-orchestrator.internal.production.gm.paytmmoney.com/',
  PML_AGENT_ORCHESTRATOR_API_KEY: '375d640b70c24840b9ddd59be896a1d5',
};
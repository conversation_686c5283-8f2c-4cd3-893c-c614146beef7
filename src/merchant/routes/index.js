import {
  MERCHANT_AUTHORIZATION,
  AI_TERMINAL_AUTHORIZATION,
} from '../config/urlConfig';

const merchantRoutes = [
  {
    path: MERCHANT_AUTHORIZATION.GET_STARTED,
    load: () =>
      import(
        /* webpackChunkname: 'MerchantAuthorization' */ './GetStartedLanding'
      ),
  },
  {
    path: MERCHANT_AUTHORIZATION.STRATEGY_LANDING,
    load: () =>
      import(
        /* webpackChunkname: 'MerchantAuthorization' */ './StrategyLandingPage'
      ),
  },
  {
    path: AI_TERMINAL_AUTHORIZATION.GET_STARTED,
    load: () =>
      import(
        /* webpackChunkname: 'AITerminalAuthorization' */ './GetStartedLandingAITerminal/index'
      ),
  },
];

export default merchantRoutes;

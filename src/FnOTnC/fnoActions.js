import axios from 'axios';
import {
  getGenericAppHeaders,
  makeApiGetCall,
  makeApiPostCall,
} from '../utils/apiUtil';
import { AxiosErrorHandler } from '../utils/errorUtils';
import { FNO_TNC } from './enums';
import { TNC_URL } from './urlConfig';
import { setLoaderView } from '../actions/genericActions';

export const postTncData = async axiosSource => {
  const headers = getGenericAppHeaders();

  try {
    setLoaderView(true);
    const response = await makeApiPostCall({
      url: TNC_URL,
      headers,
      body: { tncType: FNO_TNC.RISK_DISCLOSURE_FNO },
      axiosSource,
    });
    setLoaderView(false);
    return response?.data;
  } catch (error) {
    setLoaderView(false);
    if (!axios.isCancel(error)) {
      AxiosErrorHandler(error, false, false, true);
    }
  }
};

export const getFNOJSON = async (url, axiosSource) => {
  try {
    const response = await makeApiGetCall({
      url,
      headers: {},
      axiosSource,
    });
    return response?.data;
  } catch (error) {
    if (!axios.isCancel(error)) {
      AxiosErrorHandler(error, false, false, true);
    }
  }
};

import { exitApp, notifyNativeApp } from '../utils/bridgeUtils';
import { isIBL, isPaytmMoney } from '../utils/commonUtils';
import { FNO_TNC } from './enums';

export const proceedOnResponseTrue = () => {
  if (isPaytmMoney() || isIBL()) {
    notifyNativeApp({
      flowType: FNO_TNC.FLOW_TYPE,
      TncStatus: true,
    });
  } else {
    exitApp();
  }
};

export const proceedOnResponseFalse = () => {
  if (isPaytmMoney() || isIBL()) {
    notifyNativeApp({
      flowType: FNO_TNC.FLOW_TYPE,
      TncStatus: false,
    });
  } else {
    exitApp();
  }
};

import React from 'react';
import cx from 'classnames';

import styles from './shimmerLoading.scss';

const ShimmerLoading = () => (
  <div className={styles.container}>
    <div className={styles.header}>
      <div className={cx(styles.image, styles.shimmerAnimation)} />
      <div className={cx(styles.heading, styles.shimmerAnimation)} />
      <div className={cx(styles.sebi, styles.shimmerAnimation)} />
    </div>
    <div className={styles.body}>
      <ul className={styles.lists}>
        {[...Array(4)].map((_, index) => (
          <li key={index} className={styles.shimmerAnimation} />
        ))}
      </ul>
      <hr className={styles.line} />
      <p className={cx(styles.source, styles.shimmerAnimation)} />
    </div>
    <div className={styles.footer} />
  </div>
);

export default ShimmerLoading;

.container {
    margin: 100px 0;

    .header {
        .image {
            margin: 0 auto;
            width: 120px;
            height: 48px;
        }

        .heading, .sebi {
            margin: 0 16px 0 18px;
        }

        .heading {
            margin-top: 23px;
            height: 64px;
        }

        .sebi {
            margin-top: 4px;
            height: 20px;
        }
    }

    .body {
        border-radius: 10px;
        border: solid 1px map-get($colors, ShadowColor6);
        margin: 16px 16px 40px;
        padding: 16px 0;

        .lists {
            padding: 0;
            list-style: none;

            li {
                height: 60px;
                margin: 0 16px 12px 30px;
            }
        }

        .line {
            border: 0;
            margin: 12px 16px;
            height: 1.1px;
            background-color: map-get($colors, ShadowColor6);
        }

        .source {
            margin: 0 16px;
            height: 108px;
        }
    }

    .footer {
        position: fixed;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 72px;
        box-shadow: 0 -3px 20px 0 rgba(0, 0, 0, 0.15);
        border: solid 0.8px rgba(16, 16, 16, 0.13);
    }
}
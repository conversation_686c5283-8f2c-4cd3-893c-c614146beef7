import React from 'react';
import Button from '../../../../../../components/Button/Button';

import { postTncData } from '../../../../../fnoActions';
import {
  proceedOnResponseFalse,
  proceedOnResponseTrue,
} from '../../../../../fnoUtils';
import { log } from '../../../../../../utils/commonUtils';

import styles from './footer.scss';

const Footer = ({ buttonText, axiosSource }) => {
  const handleProceed = async () => {
    try {
      const res = await postTncData(axiosSource);
      if (res) proceedOnResponseTrue();
    } catch (error) {
      log(error);
      proceedOnResponseFalse();
    }
  };
  return (
    <footer className={styles.footer}>
      <Button
        buttonText={buttonText}
        className={styles.button}
        onClickHandler={handleProceed}
      />
    </footer>
  );
};

export default Footer;

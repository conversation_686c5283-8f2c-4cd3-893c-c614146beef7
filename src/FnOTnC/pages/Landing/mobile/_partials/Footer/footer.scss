div .footer {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;

  padding: 12px 16px;
  background-color: map-get($colors, backgroundFNOColor);
  box-shadow: 0 -6px 32px 0 map-get($colors, boxShadowFNOColor);
  border-top: solid 0.5px map-get($colors, borderFNOColor);

  .button {
    padding: 14px 24px;
    border-radius: 6px;
    border: 1px solid map-get($colors, buttonFNOColor);
    background-color: map-get($colors, buttonFNOColor);

    span {
        @include typography(heading1B11, map-get($colors, buttonFNOTextColor), false, true);
    }
  }
}

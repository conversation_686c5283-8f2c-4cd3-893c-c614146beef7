div .header {
    display: flex;
    flex-direction: column;

    img {
        width: 120px;
    }

    h1 {
        padding-bottom: 10px;

        @include typography(heading1B, map-get($colors, primaryTextFNOColor), false, true);
    }

    p {
        @include typography(text1R1, map-get($colors, primaryTextFNOColor), false, true);
    }

    h2 {
        @include typography(text1R1B1, map-get($colors, primaryTextFNOColor), false, true);
    }

    .line {
        border: 0;
        margin: 16px 0;
        height: 1px;
        background-color: map-get($colors, borderFNOColor);
    }
}

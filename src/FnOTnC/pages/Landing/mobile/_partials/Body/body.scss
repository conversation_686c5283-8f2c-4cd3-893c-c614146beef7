div .body {
    border-radius: 10px;
    border: solid 1px map-get($colors, borderFNOColor);
    padding: 16px 0;
    margin-top: 12px;

    ul {
        list-style: disc;
        list-style-position: inside;
        padding: 0 16px 0 32px;

        li {
            &:not(:last-child) {
                padding-bottom: 12px;
            }

            text-indent: -12px;

            @include typography(text1R1, map-get($colors, primaryTextFNOColor), false, true);

            &::marker {
                content: '\2022  ';
                font-size: 14px;
            }
        }
    }

    .line {
        border: 0;
        margin: 12px 16px;
        height: 1.1px;
        background-color: map-get($colors, borderFNOColor);
    }

    p {
        padding: 0 16px 6px;
        @include typography(text1R1, map-get($colors, primaryTextFNOColor), false, true);
    }

    .sebi {
        padding: 0 16px;
        display: flex;
        align-items: center;

        span {
            padding-right: 4px;

            @include typography(text1R1B2, map-get($colors, buttonFNOColor), false, true);
        }

        img {
            width: 6px;
            height: 10px;
        }
    }
}

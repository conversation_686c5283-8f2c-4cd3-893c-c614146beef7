import React from 'react';

import {
  openNewPage,
  openInBrowser,
} from '../../../../../../utils/bridgeUtils';
import {
  isIosBuild,
  isIBL,
  isPaytmMoney,
} from '../../../../../../utils/commonUtils';

import Card from '../../../../../../components/Card/Card';
import BlueRight from '../../../../../../assets/icons/blue_right.png';

import styles from './body.scss';

const Body = ({ body }) => {
  const { listItem, sebi } = body;
  const { source, description, link } = sebi;

  const openSebi = () => {
    if ((isPaytmMoney() || isIBL()) && isIosBuild()) {
      openInBrowser(link?.url);
    } else {
      openNewPage(link?.url);
    }
  };

  return (
    <Card customClass={styles.body}>
      {listItem.length > 0 && (
        <>
          <ul>
            {listItem.map((item, index) => (
              <li key={index + 1}>{item}</li>
            ))}
          </ul>
          <hr className={styles.line} />
        </>
      )}
      <p>
        {source && <span>{source}&nbsp;</span>}
        {description && <span>{description}</span>}
      </p>
      {link && (
        <div className={styles.sebi} onClick={openSebi}>
          <span>{link.text}</span>
          <img src={BlueRight} alt="blue-right-icon" />
        </div>
      )}
    </Card>
  );
};

export default Body;

import React, { useEffect, useState } from 'react';

import { JSON_URL } from '../../../urlConfig';
import { getFNOJSON } from '../../../fnoActions';
import { useBackPress } from '../../../../utils/react';
import { proceedOnResponseFalse } from '../../../fnoUtils';
import { log } from '../../../../utils/commonUtils';

// COMPONENTS
import ShimmerLoading from './_partials/ShimmerLoading';
import Header from './_partials/Header';
import Body from './_partials/Body';
import Footer from './_partials/Footer';

import styles from './mobile.scss';

const HomePage = props => {
  const { pushStack, clearStack } = useBackPress();
  const [data, setData] = useState({});

  // FETCH JSON
  const fetchTNCJSON = async () => {
    try {
      const res = await getFNOJSON(JSON_URL, props.axiosSource);
      if (res) setData(res);
    } catch (error) {
      log(error);
      proceedOnResponseFalse();
    }
  };

  // DISABLE NATIVE BACK PRESS
  const disableNativeBackPress = () => {
    pushStack(disableNativeBackPress);
  };

  // USE EFFECT
  useEffect(() => {
    pushStack(disableNativeBackPress);
    fetchTNCJSON();
    return () => {
      clearStack();
    };
  }, []);

  return (
    <div className={styles.fnoContainer}>
      {!data && <ShimmerLoading />}
      {Object.keys(data).length > 0 && (
        <>
          {data.header && <Header header={data.header} />}
          {data.body && <Body body={data.body} />}
          {data.buttonText && (
            <Footer
              buttonText={data.buttonText}
              axiosSource={props.axiosSource}
            />
          )}
        </>
      )}
    </div>
  );
};

export default HomePage;

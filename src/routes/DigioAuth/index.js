import React, { useEffect } from 'react';
import URL from '../config/urlConfig';
import { getDeeplinkDataOrQueryParam } from '../../utils/commonUtils';

function DigioAuth() {
  const crd = getDeeplinkDataOrQueryParam('crd');
  const cid = getDeeplinkDataOrQueryParam('cid');
  const gwd = getDeeplinkDataOrQueryParam('gwd');
  console.log('url params: ', { crd, cid, gwd });

  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://ext-gateway.digio.in/sdk/v12/digio.js';
    script.async = true;
    document.body.appendChild(script);

    script.onload = () => {
      if (window.Digio) {
        const options = {
          environment: 'sandbox',
          is_redirection_approach: true,
          mode: 'redirection',
          redirect_url: 'https://www.paytmmoney.com/',
          callback(response) {
            console.log('$$$$$ Response is: ', response);
          },
          theme: {
            primaryColor: '#1F002A',
            secondaryColor: '#444444',
            fontFormat: 'truetype',
            fontFamily: 'NaviBody',
          },
        };

        const digio = new window.Digio(options);
        digio.init();
        digio.submit(crd, cid, gwd);
      }
    };

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return <></>;
}

export default {
  path: URL.DIGIO_AUTH,
  action() {
    return {
      title: 'Digio Authentication',
      component: <DigioAuth />,
    };
  },
};

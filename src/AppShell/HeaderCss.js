export default function(origin, darkMode) {
  return `
  .headerMainDiv {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 19;
    width: stretch
  }

  .headerMainDiv ${
    origin === 'PAYTMMONEY'
      ? `.headerDiv{box-shadow:0 3px 10px 0 rgba(0, 0, 0, 0.1);${
          darkMode === 'true' ? 'color:#efeff4' : 'color:#141b2f'
        };align-items:center;display:flex;padding:2px 10px 2px 10px;height:51px;${
          darkMode === 'true' ? 'background:#1c1c1e' : 'background:#ffffff'
        }}`
      : '.headerDiv{align-items:center;display:flex;padding:2px 10px 2px 10px;height:51px}'
  }

  .headerDiv .centerBox {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center
  }

  .headerDiv .rightBox {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center
  }

  .headerDiv .leftBox {
    display: flex;
    flex: 1;
    align-items: center;
    font-size: 30px
  }

  img {
    width: 100%
  }

  .title {
    font-size: 18px;
    font-weight: 500;
    ${darkMode === 'true' ? 'color: #efeff4' : 'color: #141b2f'}
  }`;
}

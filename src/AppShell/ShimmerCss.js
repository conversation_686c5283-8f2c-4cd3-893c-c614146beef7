export default `
.shimmerContainer {
	display: flex;
	flex-direction: column
}

.shimmerItem {
	animation: shimmerBackground 2s infinite;
	background: linear-gradient(to right, var(--card-border, #eff1f5) 4%, #e2e2e2 25%, var(--card-border, #eff1f5) 36%);
	background-size: 1000px 100%;
	height: 11px
}

.shimmerRow1 {
	width: 42%
}

.shimmerRowWrapper {
	display: flex;
	margin-top: 10px;
	margin-left: 15px;
	margin-right: 15px
}

.shimmerCol1 {
	display: flex;
	flex-direction: column;
	flex: 1
}

.shimmerCol2 {
	display: flex;
	flex-direction: column;
	flex: 1
}

.shimmerColItem1 {
	width: 55%;
	height: 10px;
	margin-bottom: 9px
}

.shimmerColItem2 {
	width: 70%;
	height: 17px;
	margin-bottom: 10px
}

.shimmerTabItem2 {
	width: 25%;
	height: 55px;
	margin-bottom: 10px
}

.shimmerTabItem2:not(:last-child) {
	margin-right: 30px
}

.goldIcon {
	width: 80px;
	height: 35px
}

@keyframes shimmerBackground {
	0% {
		background-position: -1000px 0
	}
	100% {
		background-position: 1000px 0
	}
}
`;

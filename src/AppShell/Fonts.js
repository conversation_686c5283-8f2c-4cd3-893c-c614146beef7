export default `
@font-face {
	font-family: Roboto;
	font-style: normal;
	font-weight: 300;
	src: local("Roboto Light"), local("Roboto-Light"), url(https://fonts.gstatic.com/s/roboto/v18/KFOlCnqEu92Fr1MmSU5fBBc4AMP6lQ.woff2) format("woff2");
	unicode-range: U0-0FF, U131, U152-153, U2BB-2BC, U2C 6, U2DA, U2DC, U2000-206F, U2074, U20AC, U2122, U2191, U2193, U2212, U2215, UFEFF, UFFFD
}

@font-face {
	font-family: Roboto;
	font-style: normal;
	font-weight: 400;
	src: local("Roboto"), local("Roboto-Regular"), url(https://fonts.gstatic.com/s/roboto/v18/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2) format("woff2");
	unicode-range: U0-0FF, U131, U152-153, U2BB-2BC, U2C 6, U2DA, U2DC, U2000-206F, U2074, U20AC, U2122, U2191, U2193, U2212, U2215, UFEFF, UFFFD
}

@font-face {
	font-family: Roboto;
	font-style: normal;
	font-weight: 500;
	src: local("Roboto Medium"), local("Roboto-Medium"), url(https://fonts.gstatic.com/s/roboto/v18/KFOlCnqEu92Fr1MmEU9fBBc4AMP6lQ.woff2) format("woff2");
	unicode-range: U0-0FF, U131, U152-153, U2BB-2BC, U2C 6, U2DA, U2DC, U2000-206F, U2074, U20AC, U2122, U2191, U2193, U2212, U2215, UFEFF, UFFFD
}

@font-face {
	font-family: Roboto;
	font-style: normal;
	font-weight: 700;
	src: local("Roboto Bold"), local("Roboto-Bold"), url(https://fonts.gstatic.com/s/roboto/v18/KFOlCnqEu92Fr1MmWUlfBBc4AMP6lQ.woff2) format("woff2");
	unicode-range: U0-0FF, U131, U152-153, U2BB-2BC, U2C 6, U2DA, U2DC, U2000-206F, U2074, U20AC, U2122, U2191, U2193, U2212, U2215, UFEFF, UFFFD
}

@font-face {
	font-family: Roboto;
	font-style: normal;
	font-weight: 900;
	src: local("Roboto Black"), local("Roboto-Black"), url(https://fonts.gstatic.com/s/roboto/v18/KFOlCnqEu92Fr1MmYUtfBBc4AMP6lQ.woff2) format("woff2");
	unicode-range: U0-0FF, U131, U152-153, U2BB-2BC, U2C 6, U2DA, U2DC, U2000-206F, U2074, U20AC, U2122, U2191, U2193, U2212, U2215, UFEFF, UFFFD
}

@font-face {
	font-family: Lato;
	font-style: normal;
	font-weight: 300;
	src: local('Lato Light'), local('Lato-Light'), url(https://fonts.gstatic.com/s/lato/v16/S6u9w4BMUTPHh7USSwiPGQ3q5d0.woff2) format('woff2');
	unicode-range: U30-39
}

@font-face {
	font-family: Lato;
	font-style: normal;
	font-weight: 400;
	src: local('Lato Regular'), local('Lato-Regular'), url(https://fonts.gstatic.com/s/lato/v16/S6uyw4BMUTPHjx4wXiWtFCc.woff2) format('woff2');
	unicode-range: U30-39
}

@font-face {
	font-family: Lato;
	font-style: normal;
	font-weight: 700;
	src: local('Lato Bold'), local('Lato-Bold'), url(https://fonts.gstatic.com/s/lato/v16/S6u9w4BMUTPHh6UVSwiPGQ3q5d0.woff2) format('woff2');
	unicode-range: U30-39
}

@font-face {
	font-family: Lato;
	font-style: normal;
	font-weight: 900;
	src: local('Lato Black'), local('Lato-Black'), url(https://fonts.gstatic.com/s/lato/v16/S6u9w4BMUTPHh50XSwiPGQ3q5d0.woff2) format('woff2');
	unicode-range: U30-39
}

@font-face {
	font-family: Muli;
	font-style: normal;
	font-weight: 300;
	src: url(https://fonts.gstatic.com/s/muli/v21/7Auwp_0qiz-afTLGLQjUwkQ.woff2) format('woff2');
	unicode-range: U0-0FF, U131, U152-153, U2BB-2BC, U2C 6, U2DA, U2DC, U2000-206F, U2074, U20AC, U2122, U2191, U2193, U2212, U2215, UFEFF, UFFFD
}

@font-face {
	font-family: Muli;
	font-style: normal;
	font-weight: 400;
	src: url(https://fonts.gstatic.com/s/muli/v21/7Auwp_0qiz-afTLGLQjUwkQ.woff2) format('woff2');
	unicode-range: U0-0FF, U131, U152-153, U2BB-2BC, U2C 6, U2DA, U2DC, U2000-206F, U2074, U20AC, U2122, U2191, U2193, U2212, U2215, UFEFF, UFFFD
}

@font-face {
	font-family: Muli;
	font-style: normal;
	font-weight: 500;
	src: url(https://fonts.gstatic.com/s/muli/v21/7Auwp_0qiz-afTLGLQjUwkQ.woff2) format('woff2');
	unicode-range: U0-0FF, U131, U152-153, U2BB-2BC, U2C 6, U2DA, U2DC, U2000-206F, U2074, U20AC, U2122, U2191, U2193, U2212, U2215, UFEFF, UFFFD
}

@font-face {
	font-family: Muli;
	font-style: normal;
	font-weight: 600;
	src: url(https://fonts.gstatic.com/s/muli/v21/7Auwp_0qiz-afTLGLQjUwkQ.woff2) format('woff2');
	unicode-range: U0-0FF, U131, U152-153, U2BB-2BC, U2C 6, U2DA, U2DC, U2000-206F, U2074, U20AC, U2122, U2191, U2193, U2212, U2215, UFEFF, UFFFD
}

@font-face {
	font-family: Muli;
	font-style: normal;
	font-weight: 700;
	src: url(https://fonts.gstatic.com/s/muli/v21/7Auwp_0qiz-afTLGLQjUwkQ.woff2) format('woff2');
	unicode-range: U0-0FF, U131, U152-153, U2BB-2BC, U2C 6, U2DA, U2DC, U2000-206F, U2074, U20AC, U2122, U2191, U2193, U2212, U2215, UFEFF, UFFFD
}

@font-face {
	font-family: Muli;
	font-style: normal;
	font-weight: 900;
	src: url(https://fonts.gstatic.com/s/muli/v21/7Aulp_0qiz-aVz7u3PJLcUMYOFlnl0k30e6fwniDtzM.woff) format('woff');
	unicode-range: U0-0FF, U131, U152-153, U2BB-2BC, U2C 6, U2DA, U2DC, U2000-206F, U2074, U20AC, U2122, U2191, U2193, U2212, U2215, UFEFF, UFFFD
}
`;

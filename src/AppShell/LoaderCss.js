export default `
.loaderBox {
	display: none;
	position: fixed;
	width: 100vw;
	height: 100vh;
	background: transparent;
	z-index: 21;
	top: 0;
	left: 0
}

.loaderBox .loader_inner {
	margin: auto;
	display: flex
}

@-webkit-keyframes scale {
	0% {
		-webkit-transform: scale(1);
		transform: scale(1);
		opacity: 1
	}
	45% {
		-webkit-transform: scale(.35);
		transform: scale(.35);
		opacity: .7
	}
	80% {
		-webkit-transform: scale(1);
		transform: scale(1);
		opacity: 1
	}
}

@keyframes scale {
	0% {
		-webkit-transform: scale(1);
		transform: scale(1);
		opacity: 1
	}
	45% {
		-webkit-transform: scale(.35);
		transform: scale(.35);
		opacity: .7
	}
	80% {
		-webkit-transform: scale(1);
		transform: scale(1);
		opacity: 1
	}
}

.ball_pulse>div:nth-child(1) {
	background-color: #012b72;
	-webkit-animation: scale .75s -.24s infinite cubic-bezier(.2, .68, .18, 1.08);
	animation: scale .75s -.24s infinite cubic-bezier(.2, .68, .18, 1.08);
	margin: 2px
}

.ball_pulse>div:nth-child(2) {
	background-color: #012b72;
	-webkit-animation: scale .75s -.12s infinite cubic-bezier(.2, .68, .18, 1.08);
	animation: scale .75s -.12s infinite cubic-bezier(.2, .68, .18, 1.08);
	margin: 2px
}

.ball_pulse>div:nth-child(3) {
	background-color: #012b72;
	-webkit-animation: scale .75s 0s infinite cubic-bezier(.2, .68, .18, 1.08);
	animation: scale .75s 0s infinite cubic-bezier(.2, .68, .18, 1.08);
	margin: 2px
}

.ball_pulse>div:nth-child(4) {
	background-color: #00b9f5;
	-webkit-animation: scale .75s 0s infinite cubic-bezier(.2, .68, .18, 1.08);
	animation: scale .75s 0s infinite cubic-bezier(.2, .68, .18, 1.08);
	margin: 2px
}

.ball_pulse>div:nth-child(5) {
	background-color: #00b9f5;
	-webkit-animation: scale .75s 0s infinite cubic-bezier(.2, .68, .18, 1.08);
	animation: scale .75s 0s infinite cubic-bezier(.2, .68, .18, 1.08);
	margin: 2px
}

.ball_pulse>div:nth-child(6) {
	background-color: #00b9f5;
	-webkit-animation: scale .75s 0s infinite cubic-bezier(.2, .68, .18, 1.08);
	animation: scale .75s 0s infinite cubic-bezier(.2, .68, .18, 1.08);
	margin: 2px
}

.ball_pulse>div {
	width: 12px;
	height: 12px;
	border-radius: 100%
}
`;

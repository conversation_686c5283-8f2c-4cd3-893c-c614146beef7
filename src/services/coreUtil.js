import get from 'lodash/get';
import round from 'lodash/round';
import DeviceInfoProvider from '../utils/Providers/DeviceInfoProvider';
import { isBridge, getBridge, exitApp } from '../utils/bridgeUtils';
import { PULSE_EVENTS } from '../utils/Constants';
import { log, errorLog, isIosBuild, isPaytmMoney } from '../utils/commonUtils';
import URL from '../routes/config/urlConfig';

class AppNavigatorException {
  constructor(message) {
    this.msg = `${message}: object is missing in the arguments. Please pass \`${message}\` to avoid this exception\nMake sure \`withRouter in used in the component\``;
    this.name = 'AppNavigatorError';
  }
}

export function addParamToUrl(url) {
  if (isBridge()) {
    const currentBundleVersion = DeviceInfoProvider.getInfo('ver');
    if (url.indexOf('?') > -1) {
      return `${url}&os=${DeviceInfoProvider.getInfo(
        'device_type',
      )}&ver=${currentBundleVersion}`;
    }
    return `${url}?os=${DeviceInfoProvider.getInfo('device_type')}`;
  }
  return url;
}

export const isH5Container = () => {
  const ua = window.navigator.userAgent;
  return /AppContainer/i.test(ua);
};

export function goToPaytmHomepage() {
  try {
    window.location.href = addParamToUrl('/');
    // eslint-disable-next-line no-empty
  } catch (e) {}
}

export function getSsoTokenKey() {
  if (isBridge()) {
    return 'sso_token';
  }
  // eslint-disable-next-line no-undef
  if (__ENV__ === 'production') {
    return 'sso_token_enc';
  }
  return 'sso_token';
}

export function getExternalDeepLinkData() {
  if (isBridge()) {
    return DeviceInfoProvider.getInfo('deeplinkData');
  }
  return '';
}

export function setExternalDeepLinkData(deepLinkUrl) {
  if (isBridge()) {
    return DeviceInfoProvider.setInfo('deeplinkData', deepLinkUrl);
  }
  return '';
}

export function setInitialQueryParams(value) {
  DeviceInfoProvider.setInfo('initialQueryParams', value);
}

export function getInitialQueryParams() {
  return DeviceInfoProvider.getInfo('initialQueryParams');
}

export function getH5NativeDeepLinkData() {
  log('## getH5NativeDeepLinkData: ');
  if (isBridge()) {
    return DeviceInfoProvider.getInfo('H5NativeDeeplinkData');
  }
  return '';
}

export function setH5NativeDeepLink(deepLinkUrl) {
  log('## setH5NativeDeepLink: ');
  if (isBridge()) {
    return DeviceInfoProvider.setInfo('H5NativeDeeplinkData', deepLinkUrl);
  }
  return '';
}

export function resetH5NativeDeepLinkData() {
  log('## resetH5NativeDeepLinkData: ');
  if (isBridge()) {
    return DeviceInfoProvider.setInfo('H5NativeDeeplinkData', '');
  }
  return '';
}

export function resetExternalDeepLinkData() {
  if (isBridge()) {
    return DeviceInfoProvider.setInfo('deeplinkData', '');
  }
  return '';
}

export function setJsBundleVersion(version) {
  DeviceInfoProvider.setInfo('ver', version ? parseInt(version, 10) : 1);
}

export function getJsBundleVersion() {
  return DeviceInfoProvider.getInfo('ver');
}

export function shouldShowNativeSip() {
  return getJsBundleVersion() === 1 && isBridge();
}

export function getEncSSOToken() {
  return DeviceInfoProvider.getInfo('sso_token_enc');
}

export function getNormalSSOToken() {
  return DeviceInfoProvider.getInfo('sso_token');
}

export function getX2FAToken() {
  return DeviceInfoProvider.getInfo('x-2fa-token');
}

export function getX2FATokenExpiry() {
  return DeviceInfoProvider.getInfo('x-2fa-token-expiry');
}

export function getSSOToken() {
  if (isBridge()) {
    return getNormalSSOToken();
  }
  // eslint-disable-next-line no-undef
  if (__ENV__ === 'production') {
    return getEncSSOToken();
  }
  return getNormalSSOToken();
}

export function getUserId() {
  return DeviceInfoProvider.getInfo('userId');
}

export function getOsVersion() {
  return DeviceInfoProvider.getInfo('osVersion');
}

export function getDeviceName() {
  return DeviceInfoProvider.getInfo('deviceName');
}

export function getDeviceManufacturer() {
  return DeviceInfoProvider.getInfo('deviceManufacturer');
}

export function getAppVersion() {
  return DeviceInfoProvider.getInfo('appVersionName');
}

export function getAppClient() {
  return DeviceInfoProvider.getInfo('client');
}

export function getDeviceType() {
  return DeviceInfoProvider.getInfo('device_type');
}

export function getDeviceID() {
  return DeviceInfoProvider.getInfo('deviceId');
}

export const parseDeviceToken = () => {
  const deviceId = getDeviceID() || '';
  if (isIosBuild()) return deviceId;
  const splitArray = deviceId.split('-');
  return splitArray[splitArray.length - 1];
};

export function getCookie(name) {
  try {
    const re = new RegExp(`${name}=([^;]+)`);
    const value = re.exec(document.cookie);
    return value != null ? unescape(value[1]) : null;
  } catch (e) {
    return null;
  }
}

// export const setTimeZone = (date, format) => {
//   let timeZoneUtc = date
//     ? moment(date).utcOffset("+05:30")
//     : moment().utcOffset("+05:30");
//   if (format) timeZoneUtc = timeZoneUtc.format(format);
//   return timeZoneUtc;
// };

/**
 * @description: to throw location change as well as change physical path though Navigate/redirect action
 */
export function navigate(path, method = 'push', scrollToTop) {
  return async (dispatch, getState, { history }) => {
    history[method](path, { scrollToTop, isReact: true });
  };
}

export const navigateTo = (history, path, data, method = 'push') => {
  try {
    if (!history) {
      throw new AppNavigatorException('history');
    }
    let routePath = path;

    // for desktop
    const { pathname } = window.location;
    if (pathname.includes(URL.CORPORATE_ACTIONS)) {
      routePath = `${URL.CORPORATE_ACTIONS}${path}`;
    }

    switch (method) {
      case 'push':
        history.push(routePath, data);
        break;
      case 'replace':
        history.replace(routePath, data);
        break;
      default:
        history.push(routePath, data);
    }
  } catch (e) {
    errorLog(e);
  }
};

export const navigateBack = history => {
  history.goBack();
};

export const goBack = history => {
  if (history.length === 1) {
    exitApp();
  }
  navigateBack(history);
};

export function urlAugmentor(url = '') {
  if (url.startsWith('http')) return url;
  if (url.startsWith('//')) return `https:${url}`;
  if (!url.startsWith('http') && !url.startsWith('/')) return `https://${url}`;
  return url;
}

/* Returns query string with object's key values pairs as?key1=value1&key2=value2 and so-on */
export const stringifyQueryParams = obj => {
  let queryString = '';
  // To-Do  Please remove filter and map chaining , It can be done with single iteration .
  Object.keys(obj)
    .filter(key => !!obj[key] !== false)
    .map(key => {
      queryString += `${key}=${obj[key]}&`;
      return queryString;
    });
  return queryString.length
    ? `?${queryString.substr(0, queryString.length - 1)}`
    : '';
};

export function kConverter(val) {
  const value = parseInt(val, 10);
  return value > 999 ? `${round(value / 1000, 1)}K` : value;
}

export function getYoutubeVideoId(url = '') {
  return get(
    url.match(
      /(?:https?:\/{2})?(?:w{3}\.)?youtu(?:be)?\.(?:com|be)(?:\/watch\?v=|\/)([^\s&]+)/,
    ),
    '1',
    '',
  );
}

export function pluralize(str, count) {
  return count > 1 ? `${str}s` : str;
}

export function getReferrer() {
  let referrer = process.env.BROWSER
    ? window.location.pathname + window.location.search
    : '/';
  referrer = encodeURIComponent(referrer);
  return referrer;
}

export function deg2rad(degrees) {
  const pi = Math.PI;
  return degrees * (pi / 180);
}

export function rad2deg(radians) {
  const pi = Math.PI;
  return radians * (180 / pi);
}

export function getDistanceBtwCoords(c1 = {}, c2 = {}) {
  const lat1 = c1.latitude;
  const lat2 = c2.latitude;
  const lon1 = c1.longitude;
  const lon2 = c2.longitude;

  const theta = lon1 - lon2;
  let dist =
    Math.sin(deg2rad(lat1)) * Math.sin(deg2rad(lat2)) +
    Math.cos(deg2rad(lat1)) *
      Math.cos(deg2rad(lat2)) *
      Math.cos(deg2rad(theta));
  dist = rad2deg(Math.acos(dist));
  dist = dist * 60 * 1.1515 * 1.609344;
  return round(dist, 2);
}

export function formatNumber(value, digits = 2) {
  return round(Number(value), digits);
}

export function isLogedIn() {
  return DeviceInfoProvider.getInfo('isLogin');
}

export function isSupportedVersion(curr, b) {
  const base = b.split('.').map(ele => parseInt(ele, 10));
  const current = curr.split('.').map(ele => parseInt(ele, 10));

  for (let i = 0; i < base.length; i += 1) {
    if (current[i] > base[i]) {
      return true;
    } else if (current[i] < base[i]) {
      return false;
    }
  }
  return true;
}

export const sendAnalyticsScreenEvent = screenName => {
  const bridgeName = getBridge();
  if (typeof bridgeName !== 'undefined') {
    if (isPaytmMoney()) {
      bridgeName.call(
        'paytmAnalyticsTracking',
        {
          eventName: 'openScreen',
          screenName,
          data: {
            vertical_name: PULSE_EVENTS.verticalName,
          },
        },
        () => {
          // Logger(`Analytics:: Analytics Bridge result - ${result}`);
        },
      );
    }
  } else if (window.ga) {
    if (isPaytmMoney()) {
      window.ga('send', 'pageview', screenName);
    }
  }
};

export function getClientIdEnv(clientId) {
  // eslint-disable-next-line no-undef
  if (__ENV__ === 'staging') {
    // eslint-disable-next-line no-undef
    return `${clientId}-${__ENV__}`;
  }
  return clientId;
}

export function setDarkModeValue(isDarkMode) {
  DeviceInfoProvider.setInfo('darkmode', isDarkMode);
}

export const isPhoenixContainer = () => {
  const ua = window.navigator.userAgent;
  return /PhoenixContainer/i.test(ua);
};

export const getMobileOperatingSystem = () => {
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  // Windows Phone must come first because its UA also contains “Android”
  if (/windows phone/i.test(userAgent)) {
    return 'windows';
  }
  if (/android/i.test(userAgent)) {
    return 'android';
  }
  // iOS detection from: http://stackoverflow.com/a/9039885/177710
  if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
    return 'ios';
  }
  return 'android';
};

export const setIsConsentOverlayShown = value => {
  localStorage.setItem('isConsentOverlayShown', value);
};
export const getIsConsentOverlayShown = () =>
  localStorage.getItem('isConsentOverlayShown');

export const sendEventToBridge = AnalyticsDataMap => {
  const bridgeName = getBridge();
  if (typeof bridgeName !== 'undefined') {
    if (isPaytmMoney()) {
      bridgeName.call('paytmAnalyticsTracking', AnalyticsDataMap, () => {
        // Logger(`Analytics:: Analytics Bridge result - ${result}`);
      });
    }
  }
};

export function sendAnalyticsEvent(
  screenName,
  data,
  event_label,
  event_label5,
) {
  const deviceType = `H5_${getDeviceType()}`;
  const userId = getUserId();
  log({
    screenName,
    event_label: event_label || deviceType,
    event_label5: event_label5 || userId,
    ...data,
  });

  if (isBridge()) {
    const map = {
      eventName: 'custom_event',
      screenName,
      data: {
        event_label: deviceType,
        event_label5: userId,
        ...data,
      },
    };

    sendEventToBridge(map);
  }
}

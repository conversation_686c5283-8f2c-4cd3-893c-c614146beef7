import { get } from 'lodash';
import queryString from 'query-string';

import { isPaytmMoney, isIBL, isDarkMode } from '../../utils/commonUtils';
import { isH5, notifyNativeApp, exitApp } from '../../utils/bridgeUtils';
import {
  getH5NativeDeepLinkData,
  sendAnalyticsEvent,
} from '../../services/coreUtil';

import { PULSE_STATICS } from '../config/common';
import { PULSE_ACTIONS } from '../pages/MTFPledge/enums';

export const getFilterScrips = (scrips, searchText) =>
  scrips.filter(el =>
    el.scrip_name.toLowerCase().includes(searchText.toLowerCase()),
  );

export const getScripCSVRows = scrips =>
  scrips.map(el => [
    el.scrip_name,
    `${100 - Number(el.margin_perc)}%`, // Paytmmoney funding
    `${el.margin_perc}%`, // Your funding
    `${parseFloat((100 / Number(el.margin_perc)).toFixed(1))}`, // Leverage
  ]);

export const getPageSource = () => {
  if (isPaytmMoney()) {
    const deepLinkUrl = getH5NativeDeepLinkData();
    const source = get(queryString.parse(deepLinkUrl), 'source', '');
    return {
      source,
    };
  } else if ('URLSearchParams' in window) {
    const searchParams = new URLSearchParams(window.location.search);
    return {
      source: searchParams.get('source'),
    };
  }
  return {
    source: null,
  };
};

export const sendMTFEvents = action => {
  // eslint-disable-next-line no-underscore-dangle
  const { webUtilities: { sendWebPulseEvent } = {} } = window._context;
  const { SCREEN_NAME, CATEGORY, LABEL } = PULSE_STATICS;
  const verticalName = isIBL()
    ? 'ibl_stocks'
    : isPaytmMoney()
    ? 'stocks'
    : 'paytm_stocks';
  const data = {
    vertical_name: verticalName,
    event_category: CATEGORY,
    event_action: action,
    event_label: LABEL,
  };
  if (isH5()) {
    sendAnalyticsEvent(SCREEN_NAME, data);
  } else if (sendWebPulseEvent) {
    sendWebPulseEvent({
      event_category: CATEGORY,
      event_action: action,
      event_label: LABEL,
      vertical_name: verticalName,
      screen_name: SCREEN_NAME,
    });
  }
};

export const sendMTFPledgePageOpenEvent = sendWebPulseEvent => {
  const { SCREEN_NAME, CATEGORY, LABEL } = PULSE_STATICS;
  const verticalName = isIBL()
    ? 'ibl_stocks'
    : isPaytmMoney()
    ? 'stocks'
    : 'paytm_stocks';
  if (sendWebPulseEvent) {
    sendWebPulseEvent({
      event_category: CATEGORY,
      event_action: PULSE_ACTIONS.ONLOAD,
      event_label: LABEL,
      vertical_name: verticalName,
      screen_name: SCREEN_NAME,
    });
  }
};

export const sendDataToParentApp = data => {
  // eslint-disable-next-line no-underscore-dangle
  const { webUtilities: { notifyWebAppForMTF } = {} } = window._context;

  if (isH5()) {
    const mtfData = {
      flowType: 'mtf_flow',
      ...data,
    };
    notifyNativeApp(mtfData);
  } else if (notifyWebAppForMTF) {
    notifyWebAppForMTF(data);
  }
};

export const exitMTFContainer = () => {
  const { webUtilities: { webModalClose } = {} } = window._context;
  if (isH5()) {
    exitApp();
  } else {
    webModalClose();
  }
};

export const darkModeHandling = () => {
  const isDesktop = '__BUILD_PATH__' === 'desktop';

  if (isDesktop) {
    return localStorage.getItem('darkmode') === 'dark';
  }

  return isDarkMode();
};

import { useEffect, useState } from 'react';
import { activateMTF, fetchMTFPlans } from '../../actions/mtfAction';
import {
  openDeepLink,
  openDeepLinkPaytmMoney,
  openNewPage,
} from '../../utils/bridgeUtils';
import { isPaytmMoney } from '../../utils/commonUtils';
import { MTF_IR_STATUS } from '../config/common';
import { ONBOARDING_PULSE_STATICS } from '../pages/MTFOnboarding/enums';
import { KYC_DEEP_LINK, MTF_ERROR_CODES } from '../pages/MTFScrips/enums';
import { sendDataToParentApp, sendMTFEvents } from './mtfUtils';

const isDesktop = '__BUILD_PATH__' === 'desktop';

export function useMTF(axiosSource) {
  const [planDetails, setPlanDetails] = useState(null);

  const getMTFPlans = async () => {
    const response = await fetchMTFPlans(axiosSource);
    if (response?.data) {
      setPlanDetails(response.data.find(item => item.isDefaultPlan === true));
    }
  };

  const sendActivateSuccessEvents = status => {
    if (status === MTF_IR_STATUS.ACTIVE) {
      sendMTFEvents(ONBOARDING_PULSE_STATICS.ACTIVATE_SUCCESS_SCREEN);
    } else if (status === MTF_IR_STATUS.REVOKED) {
      sendMTFEvents(ONBOARDING_PULSE_STATICS.ACTIVATE_BLOCKED_SCREEN);
    } else if (status === MTF_IR_STATUS.UNDER_REGISTRATION) {
      sendMTFEvents(ONBOARDING_PULSE_STATICS.ACTIVATE_PENDING_SCREEN);
    }
  };

  const onActivateCtaClick = async onActivateMTF => {
    sendMTFEvents(
      isDesktop
        ? ONBOARDING_PULSE_STATICS.ACTIVATE_CLICK
        : ONBOARDING_PULSE_STATICS.ACTIVATE_CLICK_V2,
    );
    try {
      const response = await activateMTF(axiosSource, planDetails.planId);
      if (response?.data) {
        const status = response.data.irStatus;
        const { cardDisplayMessage } = response.data;

        sendActivateSuccessEvents(status);

        sendDataToParentApp({
          irStatus: status,
          pollingExhausted: false,
          displayMessage: cardDisplayMessage,
        });
        onActivateMTF(status);
      }
    } catch (error) {
      if (error.message === MTF_ERROR_CODES.IR_STATUS_NOT_FOUND) {
        if (isPaytmMoney()) {
          openDeepLinkPaytmMoney(KYC_DEEP_LINK);
        } else {
          openDeepLink(KYC_DEEP_LINK);
        }
      }
    }
  };

  const onContactCTA = configData => {
    openNewPage(configData?.['cta-link']);
  };

  useEffect(() => {
    getMTFPlans();
  }, []);

  return { onActivateCtaClick, planDetails, onContactCTA };
}

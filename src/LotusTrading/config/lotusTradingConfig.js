/* eslint-disable import/no-unresolved */
/* eslint-disable global-require */

import POLYGON_LEFT from '../../assets/icons/tradingIdeas/polygon-left.svg';
import POLYGON_RIGHT from '../../assets/icons/tradingIdeas/polygon-right.png';
import CHECKED from '../../assets/icons/tradingIdeas/checked.png';
import DOWNLOAD_PDF from '../../assets/icons/downloadPdf.png';
import DOWNLOAD from '../../assets/icons/tradingIdeas/Download.png';
import {
  sendBuySellClickEvents,
  sendQuickFilterClickEvent,
  sendTabChangeEvent,
} from '../analyticsEvents/tradingIdeasEventUtils';
import { INSTRUMENT_TYPE, SEGMENT_TYPES } from '../../utils/enum';
import {
  generateQueryParamsString,
  isDarkMode,
  isIosBuild,
  isPaytmMoney,
} from '../../utils/commonUtils';
import {
  isH5,
  openDeepLink,
  openDeepLinkPaytmMoney,
  paytmResetStatusBarBackgroundColor,
} from '../../utils/bridgeUtils';
import {
  PAYTM_ORDER_URL,
  PML_ORDER_URL_DERIVATIVES,
  PML_ORDER_URL_EQUITY,
} from './urlConfig';
import {
  EVENT_CATEGORY,
  SCREEN_NAME_RA_LANDING,
} from '../analyticsEvents/tradingIdeasEventEnum';
import { CALL_TYPE } from './expertPage';
import {
  PML_ORDER_URL_DERIVATIVES_WEB,
  PML_ORDER_URL_EQUITY_WEB,
} from '../components/Table/enums';

let FILTER;
let DOWN_ARROW;
let HAMBURGER_MENU;
let UNCHECKED;
let SELECTED;
let UNSELECTED;
let BACK;
let RESEARCH_ANALYST;
let DERIVATIVES_LOGO;
let EQUITY_LOGO;
let UP_ARROW;
let EXPERTSBG;

if (isDarkMode()) {
  FILTER = require('../../assets/icons/tradingIdeas/filter-dark.png');
  DOWN_ARROW = require('../../assets/icons/tradingIdeas/downArrowDark.png');
  HAMBURGER_MENU = require('../../assets/icons/tradingIdeas/Hamburger-menu-dark.png');
  UNCHECKED = require('../../assets/icons/tradingIdeas/unchecked-dark.png');
  SELECTED = require('../../assets/icons/tradingIdeas/radioSelected.png');
  UNSELECTED = require('../../assets/icons/tradingIdeas/radioUnselectedDark.png');
  BACK = require('../../assets/icons/back_white.png');
  RESEARCH_ANALYST = require('../../assets/icons/tradingIdeas/research-analyst-dark.png');
  DERIVATIVES_LOGO = require('../../assets/icons/tradingIdeas/derivative-dark.png');
  EQUITY_LOGO = require('../../assets/icons/tradingIdeas/equity-dark.png');
  UP_ARROW = require('../../assets/icons/tradingIdeas/upArrowDark.png');
  EXPERTSBG = require('../../assets/icons/tradingIdeas/expertsBGDark.png');
} else {
  FILTER = require('../../assets/icons/tradingIdeas/filter.png');
  DOWN_ARROW = require('../../assets/icons/tradingIdeas/downArrow.png');
  HAMBURGER_MENU = require('../../assets/icons/tradingIdeas/menu.png');
  UNCHECKED = require('../../assets/icons/tradingIdeas/unchecked.png');
  SELECTED = require('../../assets/icons/tradingIdeas/radioSelected.png');
  UNSELECTED = require('../../assets/icons/tradingIdeas/radioUnselected.png');
  BACK = require('../../assets/icons/backIcon.png');
  RESEARCH_ANALYST = require('../../assets/icons/tradingIdeas/research-analyst.png');
  DERIVATIVES_LOGO = require('../../assets/icons/tradingIdeas/derivatives.png');
  EQUITY_LOGO = require('../../assets/icons/tradingIdeas/equity.png');
  UP_ARROW = require('../../assets/icons/tradingIdeas/upArrow.png');
  EXPERTSBG = require('../../assets/icons/tradingIdeas/expertsBG.png');
}

export const HAMBERGER_MENU_LOGO = HAMBURGER_MENU;
export const FILTER_ICON = FILTER;

export const DOWN_ARROW_ICON = DOWN_ARROW;
export const CHECKED_ICON = CHECKED;
export const SELECTED_ICON = SELECTED;
export const UNSELECTED_ICON = UNSELECTED;
export const UNCHECKED_ICON = UNCHECKED;
export const RESEARCH_ANALYST_ICON = RESEARCH_ANALYST;
export const DERIVATIVES_ICON = DERIVATIVES_LOGO;
export const EQUITY_ICON = EQUITY_LOGO;
export const UP_ARROW_ICON = UP_ARROW;
export const BACK_ICON = BACK;
export const EXPERTSBGLOGO = EXPERTSBG;
export const POLYGON_LEFT_ICON = POLYGON_LEFT;
export const POLYGON_RIGHT_ICON = POLYGON_RIGHT;
export const DOWNLOAD_ICON = DOWNLOAD;

export const ALL = 'All';
export const SORT_BY = 'Sort by';
export const APPLY = 'Apply';
export const CALL_EXPIRY = 'Idea Expiry';
export const DEFAULT_TEXT = 'Set as Default';
export const SAVED_FILTERS_KEY = 'filters';
export const POWERED_BY = 'Powered by';
export const EXIT_AT = 'Exit at';
export const LIVE_PRICE = 'Live price';
export const BUY = 'Buy';
export const SELL = 'Sell';
export const BUY_OVERNIGHT = 'Buy';
export const SELL_OVERNIGHT = 'Sell';
export const NOTE_CONTENT =
  'The Trading Ideas are expressed by SEBI registered RAs/RIAs. You agree that Paytm Money Ltd. (PML) is not responsible nor liable for any financial liability on account of such advice/research. PML is only acting as a distribution agent. PML makes no representations, warranty, express or implied, provided to the fairness, accuracy, correctness, completeness or reliability of the information, opinions or conclusions expressed. For any claims/disputes arising out of advice/research provided by the RAs/RIAs or with respect to the distribution activity, you would not have access to the Exchange investor redressal forum or Arbitration mechanism. All data, content, views, expressed or implied, are only for information purposes for investors/users. It cannot be construed as either an offer or a solicitation to purchase or sell securities.';
export const FEATURES_MORE = 'Features & more';
export const HAMBERGER_MENU = [
  {
    text: 'Brought to you by',
  },
  { text: '© Paytm Money Ltd. All right reserved.' },
  {
    text:
      'SEBI Reg No. Broking - INZ000240532; Depository Participant - IN - DP - 416 - 2019 , Depository Participant Number: CDSL - 12088800, Trading and clearing member of NSE (90165,M52073) and BSE(6707).',
  },
];
export const NOTE = 'Note';

const LOTUS_TRADING_LISTING_PAGE_TABS = ['derivatives', 'equity'];

const PERSONALIZED_TRADING_IDEAS_TABS = [
  'watchlist',
  'holdings',
  'positions',
  'search',
];

export const SOURCE = {
  PERSONALIZED_TRADING_IDEAS: 'personalized-listing',
  LOTUS_TRADING_IDEAS: 'lotus-listing',
};

const BADGE_FLAG_MAPPING = {
  0: {
    flagName: 'Call type',
    label: {
      0: '',
      1: 'Buy',
      2: 'Sell',
    },
  },
  1: {
    flagName: 'Event',
    label: {
      0: '',
      1: 'Event',
    },
  },
  2: {
    flagName: 'News',
    label: {
      0: '',
      1: 'News',
    },
  },
  3: {
    flagName: 'Breakout type',
    label: {
      0: '',
      1: 'Bearish',
      2: 'Bullish',
    },
  },
};

const HAMBURGER_DERIVATIVES = [
  { param: 'Future', displayValue: 'Futures' },
  { param: 'Option', displayValue: 'Options' },
];

const HAMBURGER_EQUITY = [
  { param: 'LongTerm', displayValue: 'Long Term' },
  { param: 'ShortTerm', displayValue: 'Short Term' },
  { param: 'Intraday', displayValue: 'Intraday' },
];
const DEFAULT_LOTUS_TRADING_LISTING_PAGE_TAB =
  LOTUS_TRADING_LISTING_PAGE_TABS[0];

const QUICK_FILTERS_DERIVATIVES = [
  { param: 'All', displayValue: 'All' },
  ...HAMBURGER_DERIVATIVES,
];

const DEFAULT_QUICK_FILTERS = QUICK_FILTERS_DERIVATIVES[0].param;

const QUICK_FILTERS_EQUITY = [
  { param: 'All', displayValue: 'All' },
  { param: 'Intraday', displayValue: 'Intraday' },
  { param: 'ShortTerm', displayValue: 'Short Term (Upto 30 Days)' },
  { param: 'LongTerm', displayValue: 'Long Term' },
];

const FiltersCategories = {
  Status: ['Open', 'Closed'],
  FutureOption: ['Future', 'Option'],
  Tag1: ['Intraday', 'ShortTerm', 'LongTerm'],
};

const LOTUS_TRADING_THIRD_PARTY_TAG = 'Powered by LotusFunds';
export const PARAMS = ['openAndClose', 'duration', 'advisor', 'instrument'];

const LOTUS_FILTER_DERIVATIVES_OPTIONS = [
  {
    key: 1,
    param: PARAMS[0],
    displayValue: 'Open/Close',
    isHardCoded: true,
    isChip: true,
    children: [
      { param: 'Open', displayValue: 'Open' },
      { param: 'Closed', displayValue: 'Closed' },
    ],
  },
  {
    key: 2,
    param: PARAMS[1],
    displayValue: 'Duration',
    isHardCoded: true,
    isChip: true,
    children: [
      { param: 'Intraday', displayValue: 'Intraday' },
      { param: 'ShortTerm', displayValue: 'Short Term' },
    ],
  },
  {
    key: 3,
    param: PARAMS[2],
    displayValue: 'Analyst/Advisor',
    isChip: false,
    isHardCoded: false,
  },
  {
    key: 4,
    param: PARAMS[3],
    displayValue: 'Instrument Type',
    isHardCoded: true,
    isChip: false,
    children: [
      { param: 'optidx', displayValue: 'Option Index' },
      { param: 'optstk', displayValue: 'Option Stock' },
      { param: 'futstk', displayValue: 'Future Stock' },
      { param: 'futidx', displayValue: 'Future Index' },
    ],
  },
];

const LOTUS_FILTER_EQUITY_OPTIONS = [
  {
    param: PARAMS[0],
    displayValue: 'Open/Close',
    isHardCoded: true,
    isChip: true,
    children: [
      { param: 'Open', displayValue: 'Open' },
      { param: 'Closed', displayValue: 'Closed' },
    ],
  },
  {
    param: PARAMS[1],
    displayValue: 'Duration',
    isHardCoded: true,
    isChip: true,
    children: [
      { param: 'Intraday', displayValue: 'Intraday' },
      { param: 'ShortTerm', displayValue: 'Short Term' },
      { param: 'LongTerm', displayValue: 'Long Term' },
    ],
  },
  {
    param: PARAMS[2],
    displayValue: 'Analyst/Advisor',
    isHardCoded: false,
    isChip: false,
  },
];

const LOTUS_SORT_DERIVATIVES_OPTIONS = [
  { param: 'lastUpdatedTime', displayValue: 'Last Updated Time', id: 1 },
  { param: 'createdTime', displayValue: 'Created Time', id: 2 },
  { param: 'priceWhenCreated', displayValue: 'Entry Price', id: 3 },
  { param: 'margin', displayValue: 'Margin Required (Low to High)', id: 4 },
];
const LOTUS_SORT_EQUITY_OPTIONS = [
  { param: 'lastUpdatedTime', displayValue: 'Last Updated Time', id: 1 },
  { param: 'createdTime', displayValue: 'Created Time', id: 2 },
  { param: 'priceWhenCreated', displayValue: 'Entry Price', id: 3 },
];
const FILTER_SORT_TITLES = {
  FILTER: 'Filter',
  SORT: 'Sort by',
};
const FILTER_CTA = {
  CLEAR_ALL: 'Clear All',
  APPLY: 'Apply',
  CLOSE: 'Close',
  CANCEL: 'Cancel',
};
const FILTER_DRAWER_TYPES = {
  FILTER: 'Filter',
  SORT: 'Sort',
};
const DRAWER_TITLES = {
  FILTER: 'Filters',
  SORT: 'Sort By',
};
const CARD_FIELDS = {
  STATUS: 'Status',
  TARGET_PRICE: 'Target Price',
  ENTRY_PRICE: 'Entry Price',
  STOP_LOSS: 'Stop Loss',
  CURRENT_PRICE: 'Current Price',
  LAST_UPDATE_TIME: 'Last Update Time',
  MARGIN_REQUIRED: 'Margin Required',
  SHORT_TERM: 'Short Term',
  EXIT_AT: 'Exit at',
  DOWNLOAD_REPORT: 'Download Reports',
};

const DEFAULT_DERIVATIVES_FILTERS = {
  Open: false,
  Closed: false,
  Option: false,
  Future: false,
  Intraday: false,
  ShortTerm: false,
};
const DEFAULT_EQUITY_FILTERS = {
  Open: false,
  Closed: false,
  Intraday: false,
  ShortTerm: false,
  LongTerm: false,
};

const DEFAULT_DERIVATIVES_SORT = LOTUS_SORT_DERIVATIVES_OPTIONS[1].param;
const DEFAULT_EQUITY_SORT = LOTUS_SORT_EQUITY_OPTIONS[1].param;

const REQUIRED_FIELDS = [
  'pml_id',
  'script_id',
  'script_name',
  'date_time',
  'status',
  'calltype',
  'rationale',
  'tag1',
  'price_when_created',
  'stop',
];

export const DOWNLOAD_PDF_LOGO = DOWNLOAD_PDF;

export const getIntradayBtnText = isBuy =>
  isBuy ? 'Buy Intraday' : 'Sell Intraday';

export const getBracketBtnText = isBuy =>
  isBuy ? 'Buy with Bracket Order' : 'Sell with Bracket Order';

export const getBuySellCTA = activeTab => {
  if (activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]) {
    return { BUY: BUY_OVERNIGHT, SELL: SELL_OVERNIGHT };
  }

  return { BUY, SELL };
};

// export const processExpiryDate = callexpiry =>
//   callexpiry?.split(' ')?.[0]?.replace(/-/g, ' ');

export const processExpiryDate = callexpiry => {
  const expiry = new Date(callexpiry);
  const date = expiry.getDate();
  const month = expiry.toLocaleString('default', { month: 'long' }).slice(0, 3);
  const year = expiry.getFullYear();

  return ` ${date} ${month} ${year}`;
};

export const FNO_ONBOARDING = 'paytmmp://paytmmoney/stocks/kyc/fno-onboarding';

const DUMMY_DATE = '1970/09/28';

export const TRADING_IDEAS_PARAMS = advisor => {
  let params = '?clientId=*********&reportname=standard_open';
  if (advisor) {
    params += `&advisor=${advisor}`;
  }

  return params;
};

export const handleTabChange = (tab, setActiveTab, activeQuickFilter) => {
  // PULSE EVENTS
  sendTabChangeEvent(EVENT_CATEGORY.TRADECALLS_HOME, tab);
  sendQuickFilterClickEvent(
    EVENT_CATEGORY.TRADECALLS_HOME,
    tab,
    activeQuickFilter,
  );
  setActiveTab(tab);
};

export const processTradingIdeasResponse = res => {
  const {
    script_name: name,
    entryprice1: entry,
    stoplossprice1: stopLoss,
    category: tag1,
    script_id: securityId,
    remarks: tag2,
    targetprice1: targetPrice,
    subcategory: rationale,
    callsenttime: date_time,
    callstatus: status,
    pml_id: pmlId,
    exit_condition: exitCondition,
    future_option: futureOption,
    last_UpdateTime: lastUpdateTime,
  } = res;

  return {
    name,
    entry,
    stopLoss,
    tag1,
    securityId,
    tag2,
    targetPrice,
    rationale,
    date_time,
    status,
    pmlId,
    exitCondition,
    futureOption,
    lastUpdateTime,
    ...res,
  };
};

function appendShouldOpenOnCurrentScreen(url) {
  return `${url +
    (url.includes('?') ? '&' : '?')}shouldOpenOnCurrentScreen=true`;
}

export const navigateToOrderScreen = async ({ ...data }) => {
  const { Tag1 } = FiltersCategories;

  const {
    name,
    securityId,
    isin,
    pmlId,
    entry,
    orderType,
    activeTab,
    futureOption,
    tag1,
    instrument,
    channel,
    isUserFNOReady,
    calltype,
    scripMaster,
  } = processTradingIdeasResponse(data);

  const isBuy = calltype?.toLowerCase() === CALL_TYPE.BUY;

  sendBuySellClickEvents(
    EVENT_CATEGORY.TRADECALLS_HOME,
    activeTab,
    futureOption,
    tag1,
    orderType,
    isBuy,
    SCREEN_NAME_RA_LANDING,
  );
  const DELIVERY_PRODUCT_TYPE = 'C';
  const BRACKET_PRODUCT_TYPE = 'B';
  const MARKET_ORDER_TYPE = 'MKT';
  const INTRADAY_PRODUCT_TYPE = 'I';
  const LIMIT = 'LMT';
  const M = 'M';
  const isDerivative = activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0];
  const { DERIVATIVES, CASH } = SEGMENT_TYPES;
  const segment =
    activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0] ? DERIVATIVES : CASH;

  const commonParams = {
    productType:
      orderType === 'bracket'
        ? BRACKET_PRODUCT_TYPE
        : tag1 === Tag1[0]
        ? INTRADAY_PRODUCT_TYPE
        : activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]
        ? M
        : DELIVERY_PRODUCT_TYPE,
    orderType:
      instrument?.toUpperCase() === INSTRUMENT_TYPE.FUTSTK ||
      instrument?.toUpperCase() === INSTRUMENT_TYPE.OPTSTK
        ? LIMIT
        : MARKET_ORDER_TYPE,
  };

  const params = {
    action: 'place-order',
    txn_type: isBuy ? 'B' : 'S',
    price:
      instrument?.toUpperCase() === INSTRUMENT_TYPE.FUTSTK ||
      instrument?.toUpperCase() === INSTRUMENT_TYPE.OPTSTK
        ? entry
        : 0.0,
    product: commonParams.productType,
    order_type: commonParams.orderType,
    channel,
  };

  if (!isH5()) {
    let deepLinkUrl = '';
    const queryString = generateQueryParamsString(params);

    if (activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]) {
      deepLinkUrl = `${PML_ORDER_URL_DERIVATIVES_WEB}/${instrument.toLowerCase()}/${pmlId}${queryString}`;
    } else deepLinkUrl = `${PML_ORDER_URL_EQUITY_WEB}/${pmlId}${queryString}`;
    deepLinkUrl = appendShouldOpenOnCurrentScreen(deepLinkUrl);
    window.open(deepLinkUrl);
    return;
  }

  if (isIosBuild()) {
    paytmResetStatusBarBackgroundColor();
  }

  if (isPaytmMoney()) {
    const queryString = generateQueryParamsString(params);
    let deepLinkUrl = '';
    if (activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]) {
      deepLinkUrl = `${PML_ORDER_URL_DERIVATIVES}/${
        instrument?.toLowerCase() === 'eq' ? 'es' : instrument?.toLowerCase()
      }/${pmlId}${queryString}`;
    } else deepLinkUrl = `${PML_ORDER_URL_EQUITY}/${pmlId}${queryString}`;
    deepLinkUrl = appendShouldOpenOnCurrentScreen(deepLinkUrl);
    openDeepLinkPaytmMoney(deepLinkUrl);
    return;
  }

  if (isDerivative && !isUserFNOReady) {
    openDeepLink(appendShouldOpenOnCurrentScreen(FNO_ONBOARDING));
    return;
  }

  const tickSize = scripMaster.tick_size;
  const lotSize = scripMaster.lot_Size;
  let paytmParams = {
    productType:
      tag1 === Tag1[0]
        ? INTRADAY_PRODUCT_TYPE
        : activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]
        ? M
        : DELIVERY_PRODUCT_TYPE,
    orderType: commonParams.orderType,
    id: pmlId,
    securityId,
    exchange: 'NSE',
    name,
    segment,
    tickSize,
    isin,
    instrumentType:
      instrument.toLowerCase() === 'eq' ? 'ES' : instrument?.toUpperCase(),
    lotSize,
    channel,
  };
  if (orderType === 'bracket') {
    paytmParams = {
      ...paytmParams,
      activeOrderType: BRACKET_PRODUCT_TYPE,
    };
  }
  const queryString = generateQueryParamsString(paytmParams);
  let deepLinkUrl = '';
  if (activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]) {
    deepLinkUrl = `${PAYTM_ORDER_URL(isBuy)}${queryString}`;
  } else deepLinkUrl = `${PAYTM_ORDER_URL(isBuy, pmlId)}${queryString}`;
  // Append the param here
  deepLinkUrl = appendShouldOpenOnCurrentScreen(deepLinkUrl);
  openDeepLink(deepLinkUrl);
};

export {
  LOTUS_TRADING_LISTING_PAGE_TABS,
  PERSONALIZED_TRADING_IDEAS_TABS,
  QUICK_FILTERS_DERIVATIVES,
  QUICK_FILTERS_EQUITY,
  DEFAULT_QUICK_FILTERS,
  LOTUS_FILTER_DERIVATIVES_OPTIONS,
  LOTUS_FILTER_EQUITY_OPTIONS,
  FILTER_SORT_TITLES,
  DEFAULT_DERIVATIVES_FILTERS,
  DEFAULT_EQUITY_FILTERS,
  FILTER_CTA,
  FILTER_DRAWER_TYPES,
  DRAWER_TITLES,
  REQUIRED_FIELDS,
  DUMMY_DATE,
  LOTUS_TRADING_THIRD_PARTY_TAG,
  FiltersCategories,
  CARD_FIELDS,
  LOTUS_SORT_DERIVATIVES_OPTIONS,
  LOTUS_SORT_EQUITY_OPTIONS,
  DEFAULT_DERIVATIVES_SORT,
  DEFAULT_EQUITY_SORT,
  HAMBURGER_DERIVATIVES,
  HAMBURGER_EQUITY,
  DEFAULT_LOTUS_TRADING_LISTING_PAGE_TAB,
  BADGE_FLAG_MAPPING,
};

import { ddMMyyyy, getTime } from '../../utils/dateUtl';
import {
  FiltersCategories,
  LOTUS_TRADING_LISTING_PAGE_TABS,
  SAVED_FILTERS_KEY,
} from './lotusTradingConfig';

export const getIRStatusResponse = data => {
  const fnoStatus = data?.EQUITY?.find(item => item.subProduct === 'FO');
  const irStatus = data?.EQUITY?.find(item => item.subProduct === 'CASH');

  return {
    fnoStatus: fnoStatus?.irStatus?.toUpperCase() === 'ACTIVE',
    irStatus: irStatus?.irStatus?.toUpperCase() === 'ACTIVE',
  };
};

export const isBuyBracketButtonVisible = (activeTab, tag1, status) => {
  const { Tag1, Status } = FiltersCategories;

  return (
    activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[1] &&
    tag1 === Tag1[0] &&
    status === Status[0]
  );
};

export const renderHeaderDate = date_time =>
  `${ddMMyyyy(date_time)} • ${getTime(date_time)}`;

export const getBracketBtnText = isBuy =>
  isBuy ? 'Buy with bracket order' : 'Sell with bracket order';

export const isSavedFilters = () => {
  const filters = localStorage.getItem(SAVED_FILTERS_KEY);
  return filters && Object.keys(JSON.parse(filters)).length;
};

export const getMonthsDiffBetweenDates = (dateInitial, dateFinal) =>
  Math.max(
    (dateFinal.getFullYear() - dateInitial.getFullYear()) * 12 +
      dateFinal.getMonth() -
      dateInitial.getMonth(),
    0,
  );

export const calculateArrowPosition = (
  priceChange,
  stopLoss,
  targetPrice,
  isBuy,
) => {
  if (isBuy) {
    return priceChange < stopLoss
      ? 0
      : priceChange > targetPrice
      ? 100
      : ((priceChange - stopLoss) / (targetPrice - stopLoss)) * 100;
  }

  return priceChange > stopLoss
    ? 0
    : priceChange < targetPrice
    ? 100
    : ((priceChange - stopLoss) / (targetPrice - stopLoss)) * 100;
};

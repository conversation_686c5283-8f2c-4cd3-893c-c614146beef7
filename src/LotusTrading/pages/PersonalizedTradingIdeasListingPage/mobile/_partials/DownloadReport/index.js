import React from 'react';

import {
  CARD_FIELDS,
  DOWNLOAD_ICON,
} from '../../../../../config/lotusTradingConfig';

import { openInBrowser } from '../../../../../../utils/bridgeUtils';

import styles from './index.scss';

function index({ internalremarks, onDownloadClick }) {
  const onDownlod = () => {
    if (typeof onDownloadClick === 'function') {
      onDownloadClick();
    }
    if (!internalremarks[0]?.message?.length) return;

    const link = internalremarks[0]?.message?.split(/<p>(.*?)<\/p>/g);

    openInBrowser(link?.[1]);
  };
  return internalremarks?.length &&
    internalremarks[0]?.message.includes('http') ? (
    <div className={styles.downloadReport} onClick={onDownlod}>
      <span>{CARD_FIELDS.DOWNLOAD_REPORT}</span>
      <img src={DOWNLOAD_ICON} alt="" />
    </div>
  ) : null;
}

export default index;

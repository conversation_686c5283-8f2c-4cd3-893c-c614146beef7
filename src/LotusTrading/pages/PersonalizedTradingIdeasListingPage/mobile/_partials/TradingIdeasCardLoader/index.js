import React from 'react';
import cx from 'classnames';

import styles from './TradingIdeasCardLoader.scss';

const TradingIdeasCardLoader = () => (
  <div className={styles.container}>
    <div className={styles.header}>
      <div className={styles.wrapper}>
        <div className={cx(styles.icon, styles.shimmerAnimation)} />
        <div className={cx(styles.content, styles.shimmerAnimation)} />
      </div>
      <div className={styles.chipWrapper}>
        {[...Array(3)].map((_, index) => (
          <div
            key={index}
            className={cx(styles.chip, styles.shimmerAnimation)}
          />
        ))}
      </div>
    </div>
    <div className={styles.body}>
      {[...Array(6)].map((_, index) => (
        <div
          key={index}
          className={cx(styles.title, styles.shimmerAnimation)}
        />
      ))}
    </div>
    <div className={styles.livePriceWrapper}>
      <div className={cx(styles.livePrice, styles.shimmerAnimation)} />
    </div>
    <div className={styles.footer}>
      <div className={styles.buttonWrapper}>
        <div className={cx(styles.button, styles.shimmerAnimation)} />
      </div>
    </div>
  </div>
);

export default TradingIdeasCardLoader;

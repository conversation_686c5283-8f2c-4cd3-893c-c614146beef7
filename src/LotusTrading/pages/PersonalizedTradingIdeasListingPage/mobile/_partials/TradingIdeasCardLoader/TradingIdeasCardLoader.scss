.container {
    margin: 18px 15px 15px;
    padding: 16px;
    height: 170px;
    max-height: 170px;
    border: solid 1px map-get($colors, Gray1);
    background-color: map-get($colors, SecondaryColor);
    border-radius: 10px;

    .header {
        width: 300px;
        border-bottom: 1px dashed map-get($colors, Gray1);

        .wrapper {
            display: flex;

            .icon {
                width: 30px;
                height: 20px;
                margin-right: 5px;
            }

            .content {
                width: 70%;
                height: 20px;
            }


        }

        .chipWrapper {
            display: flex;
            padding: 10px 0;

            .chip {
                margin-right: 6.2px;
                width: 66.8px;
                height: 8px;
                border-radius: 115.7px;
                border: none;
                padding: 0 12px;
            }
        }
    }

    .body {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-gap: 15px;
        padding: 10px 0;
        border-bottom: 1px dashed map-get($colors, Gray1);

        .title {
            width: 56px;
            height: 13px;
        }

    }

    .livePriceWrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 10px;

      .livePrice {
        width: 150px;
        height: 20px;
      }
    }

    .footer {
        display: flex;
        justify-content: flex-end;
        padding-top: 10px;


        .buttonWrapper {
            display: flex;

            .button {
                width: 31.781px;
                height: 20px;
                border-radius: 6px;
                border: none;
                padding: 4px 12px;
            }
        }
    }
}

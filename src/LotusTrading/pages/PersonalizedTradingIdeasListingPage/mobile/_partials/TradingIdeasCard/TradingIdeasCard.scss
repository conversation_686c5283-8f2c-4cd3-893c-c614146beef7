.cardList {
  z-index: 1;
  flex-grow: 0;
  margin-top: 18px;
  position: relative;
  border: solid 1px map-get($colors, TertiaryColor);
  background-color: map-get($colors, SecondaryColor);
  border-radius: 10px;
}

.equityBottom {
  z-index: -1;
  display: flex;
  margin-top: -8px;
  align-items: center;
  justify-content: space-between;
  padding: 20px 10px 12px 10px;
  border-radius: 0px 0px 10px 10px;
  background-color: map-get($colors, BluePrimary);

  @include typography(text2R, map-get($colors, GreyPrimary), false, true);
  line-height: 16px;
}

.footer {
  button {
    height: 30px;
  }
}

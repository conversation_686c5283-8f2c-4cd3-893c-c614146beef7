import React, { Suspense, lazy } from 'react';

import DownloadReport from '../DownloadReport';

import <PERSON>Footer from '../../../../../components/CardFooter';
import CardHeader from '../../../../../components/CardHeader';
import Shimmer from '../../../../../../components/Shimmer/Shimmer';

import {
  CARD_FIELDS,
  FiltersCategories,
  LOTUS_TRADING_LISTING_PAGE_TABS,
  processTradingIdeasResponse,
} from '../../../../../config/lotusTradingConfig';
import { CALL_TYPE } from '../../../../../config/expertPage';

import PriceRange from '../../../../../components/PriceRange';
import { SEGMENT_TYPES } from '../../../../../../utils/enum';
import { useStockFeed } from '../../../../../../hooks/equities';
import {
  formatNumberWithoutHtml,
  roundValue,
} from '../../../../../../utils/commonUtils';
import { getMonthsDiffBetweenDates } from '../../../../../config/tradingIdeasUtils';

// eslint-disable-next-line css-modules/no-unused-class
import styles from './index.scss';

const LivePrice = lazy(() => import('../LivePrice'));

function ExpandedCard({ advisorDefination, ...props }) {
  const {
    name,
    margin,
    callexpiry,
    entry,
    stopLoss,
    tag1,
    securityId,
    targetPrice,
    rationale,
    date_time,
    status,
    calltype,
    pmlId,
    exitCondition,
    sebi_details,
    provider_name,
    advisor,
    advisorid,
    internalremarks,
    isUserReadyLoading,
    activeTab,
    onMarginOpen,
    lastUpdateTime,
  } = processTradingIdeasResponse(props);

  const { SHORT_TERM } = CARD_FIELDS;
  const { Tag1, Status } = FiltersCategories;
  const { DERIVATIVES, CASH } = SEGMENT_TYPES;

  const segment =
    activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0] ? DERIVATIVES : CASH;

  const isBuy = calltype?.toLowerCase() === CALL_TYPE.BUY;

  const isExit = exitCondition && status !== Status[0];

  const { ltp, pClose, percentageChange } = useStockFeed({
    exchange: 'NSE',
    securityId,
    segment,
  });

  const priceChange = Number(roundValue(ltp));

  const renderTags = () => {
    const renderTag1 = tag1 && tag1 === Tag1[1] ? SHORT_TERM : tag1;

    const renderTag2 = () => {
      const diff = getMonthsDiffBetweenDates(
        new Date(),
        new Date(callexpiry?.replace(/\s/, 'T')),
      );

      if (diff === 0) return null;

      return diff > 1 ? `${diff} Months` : `${diff} Month`;
    };

    const renderTag3 =
      rationale !== undefined && rationale.length !== 0 && rationale;
    return [
      {
        id: 1,
        tag: renderTag1,
        icon: false,
      },
      {
        id: 2,
        tag: renderTag2(),
        icon: false,
      },
      {
        id: 3,
        tag: renderTag3,
        onClick: onMarginOpen,
        icon: !!advisorDefination,
      },
    ].filter(
      val => val.tag !== undefined && val.tag !== false && val.tag !== null,
    );
  };

  const headerProps = {
    pmlId,
    name,
    status,
    Status,
    sebi_details,
    provider_name,
    isBuy,
    date_time,
    onMarginOpen,
    advisor,
    advisorid,
    advisorDefination,
    isExpandedCard: true,
    categories: renderTags(),
  };

  const bottomList = [
    {
      text: 'Updated Time',
      value: lastUpdateTime || '-- -- --',
    },
    {
      text:
        activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]
          ? 'Margin Required'
          : '',
      value:
        activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0] ? (
          formatNumberWithoutHtml(margin) || '-- -- --'
        ) : (
          <DownloadReport internalremarks={internalremarks} />
        ),
    },
  ];

  const footerProps = {
    activeTab,
    status,
    Status,
    tag1,
    isUserReadyLoading,
    exit: {
      isExit: exitCondition && status !== Status[0],
      value: exitCondition,
    },
    isBuy,
    customClass: styles.footer,
    ...processTradingIdeasResponse(props),
  };

  return (
    <div className={styles.expandedCardContainer}>
      <CardHeader {...headerProps} />

      <div className={styles.priceRangeContainer}>
        <PriceRange
          isBuy={isBuy}
          priceChange={priceChange}
          stopLoss={stopLoss}
          entry={entry}
          targetPrice={targetPrice}
        />
      </div>

      {!isExit ? (
        <Suspense fallback={<Shimmer height="15px" width="70px" />}>
          <LivePrice
            ltp={ltp}
            pClose={pClose}
            percentageChange={percentageChange}
          />
        </Suspense>
      ) : null}

      <div className={styles.cardBottom}>
        {bottomList.map(item => (
          <div className={styles.updatedTime} key={item.text}>
            {item.text ? <div className={styles.text}>{item.text}</div> : null}
            <div className={styles.value}>{item.value}</div>
          </div>
        ))}
      </div>

      <CardFooter {...footerProps} />
    </div>
  );
}

export default ExpandedCard;

import React, { Suspense, lazy } from 'react';

import Drawer from '../../../../../components/Drawer';
import Loader from '../../../../../../components/Loader/Loader';

// eslint-disable-next-line css-modules/no-unused-class
import styles from './index.scss';

const ExpandedCard = lazy(() => import('.'));

function ExpandedCardDrawer({
  isExpandedCardOpen,
  onExpandedCardClose,
  expandedCardData,
  onMarginOpen,
  activeTab,
  ...props
}) {
  const renderFallback = () => (
    <div className={styles.fallbackContainer}>
      <Loader customClass={styles.loader} />
    </div>
  );
  return (
    <Drawer
      isOpen={isExpandedCardOpen}
      onClose={onExpandedCardClose}
      showCross={false}
      className={styles.expandedCardDrawer}
    >
      <Suspense fallback={renderFallback()}>
        <ExpandedCard
          {...expandedCardData}
          onMarginOpen={onMarginOpen}
          activeTab={activeTab}
          {...props}
        />
      </Suspense>
    </Drawer>
  );
}

export default ExpandedCardDrawer;

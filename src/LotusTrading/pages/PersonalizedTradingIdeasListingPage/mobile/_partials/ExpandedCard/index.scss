.expandedCardContainer {
  padding-bottom: 16px;

  .footer {

    button {
      padding: 14px 20px;
      @include typography(heading1B2, null, false, true);
      line-height: 22px;
      letter-spacing: 0.01px;
    }
  }
}

.priceRangeContainer {
  margin: 0 16px;
}

.cardBottom {
  margin: 10px 16px 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .text {
    @include typography(text2, map-get($colors, GreySecondary), false, true);
    font-weight: 400;
    line-height: 10px;
    letter-spacing: 0.01px;
  }

  .value {
    margin-top: 4px;
    @include typography(text2B1, map-get($colors, PrimaryColor), false, true);
    line-height: 16px;
  }

  &:last-child {
    .value {
      text-align: end;
    }
  }

}


.fallbackContainer {
  height: 40vh;
  display: flex;
  position: unset;
  align-items: center;
  flex-direction: column;
  .loader {
    width: 100vw;
    height: 100%;
   }
}

.expandedCardDrawer {
  z-index: 1;
  background-color: map-get($colors, bgPopup);
}

import React, { Suspense, lazy } from 'react';

import { useStockFeed } from '../../../../../../hooks/equities';

import PriceRange from '../../../../../components/PriceRange';
import Shimmer from '../../../../../../components/Shimmer/Shimmer';

import { roundValue } from '../../../../../../utils/commonUtils';

import styles from './cardBody.scss';

const LivePrice = lazy(() => import('../LivePrice'));

const CardBody = ({
  priceRange,
  securityId,
  segment,
  isExit,
  stopLoss,
  entry,
  targetPrice,
  isBuy,
}) => {
  const { ltp, pClose, percentageChange } = useStockFeed({
    exchange: 'NSE',
    securityId,
    segment,
  });

  const priceChange = Number(roundValue(ltp));

  return (
    <div className={styles.container}>
      <PriceRange
        priceRange={priceRange}
        priceChange={priceChange}
        stopLoss={stopLoss}
        entry={entry}
        targetPrice={targetPrice}
        isBuy={isBuy}
      />

      {!isExit ? (
        <Suspense fallback={<Shimmer height="15px" width="70px" />}>
          <LivePrice
            ltp={ltp}
            pClose={pClose}
            percentageChange={percentageChange}
          />
        </Suspense>
      ) : null}
    </div>
  );
};

export default CardBody;

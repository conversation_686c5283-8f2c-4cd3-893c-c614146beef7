import React from 'react';
import classNames from 'classnames';

import { roundValue } from '../../../../../../utils/commonUtils';
import Shimmer from '../../../../../../components/Shimmer/Shimmer';

import styles from './index.scss';

const EtfPriceBroadcast = ({ ltp, pClose, percentageChange }) => {
  const renderGetChange = () => {
    const percentChange = roundValue(Math.abs(percentageChange));
    const priceChange = roundValue(ltp - pClose);
    const isProfit = priceChange > 0;
    const priceChangeStyle = isProfit ? styles.profit : styles.loss;

    return (
      <span className={classNames(priceChangeStyle, styles.change)}>{`${
        isProfit ? '+' : ' '
      }${priceChange}(${percentChange})`}</span>
    );
  };

  return (
    <>
      {ltp === undefined ? (
        <Shimmer height="15px" width="70px" />
      ) : (
        <>
          <span className={styles.ltp}>{roundValue(ltp)}</span>
          {renderGetChange()}
        </>
      )}
    </>
  );
};

export default EtfPriceBroadcast;

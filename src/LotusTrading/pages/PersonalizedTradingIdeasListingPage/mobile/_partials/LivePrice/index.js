import React from 'react';

import EtfPriceBroadcast from '../EtfPriceBroadcast';

import { LIVE_PRICE } from '../../../../../config/lotusTradingConfig';

import styles from './index.scss';

function LivePrice({ ltp, pClose, percentageChange }) {
  return (
    <div className={styles.livePrice}>
      <span className={styles.text}>{LIVE_PRICE}</span>{' '}
      <EtfPriceBroadcast
        ltp={ltp}
        pClose={pClose}
        percentageChange={percentageChange}
      />
    </div>
  );
}

export default LivePrice;

import React from 'react';
import classNames from 'classnames';
import Tab from '../../../../../components/Tab/Tab';
import { BACK_ICON } from '../../../../../config/lotusTradingConfig';
import crossIcon from '../../../../../../assets/icons/cross_icon.svg';
import RefreshIcon from '../../../../../../assets/icons/refresh.png';

import styles from './HeaderWithTabs.scss';

function HeaderWithTabs({
  customClass,
  tabContainerCustomClass,
  title,
  tabs,
  source,
  activeTab,
  isCloseIcon,
  onCloseIconClick,
  isBackIcon,
  onBackIconClick,
  isRefreshIcon,
  onRefreshIconClick,
  handleTabChange,
}) {
  return (
    <div className={classNames(styles.container, customClass)}>
      <div className={styles.header}>
        {isBackIcon && (
          <img
            onClick={onBackIconClick}
            alt="backIcon"
            src={BACK_ICON}
            className={styles.backIcon}
          />
        )}
        {title ? <div className={styles.heading}>{title}</div> : null}
        <div className={styles.icons}>
          {isCloseIcon && (
            <img
              onClick={onCloseIconClick}
              alt="crossIcon"
              src={crossIcon}
              className={styles.closeIcon}
            />
          )}
          {isRefreshIcon && (
            <img
              onClick={onRefreshIconClick}
              alt="refreshIcon"
              src={RefreshIcon}
              className={styles.refreshIcon}
            />
          )}
        </div>
      </div>

      <div className={styles.tabsContainer}>
        <Tab
          tabContainerCustomClass={tabContainerCustomClass}
          tabs={tabs}
          source={source}
          activeTab={activeTab}
          tabOnClick={handleTabChange}
        />
      </div>
    </div>
  );
}

export default HeaderWithTabs;

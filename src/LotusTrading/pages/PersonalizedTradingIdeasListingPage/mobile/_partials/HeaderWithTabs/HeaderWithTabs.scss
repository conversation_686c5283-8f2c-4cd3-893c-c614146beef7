.container {
  top: 0;
  z-index: 3;
  width: 100%;
  position: sticky;
  background: map-get($colors, GreyTertiary);

  .header {
    position: relative;
    padding: 16px 15px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .heading {
      @include typography(title3B, map-get($colors, PrimaryColor), false, true);
      line-height: 16px;
    }
  }
  .refreshIcon {
    width: 28px;
    height: 28px;
    margin-top: 2px;
  }
  .backIcon {
    width: 24px;
    height: 24px;
  }

  .icons {
    display: flex;
    align-items: center;
    gap: 7px;
  }

  .closeIcon {
    width: 14px;
    height: 14px;
  }
}

.filtersContainer {
  display: flex;
  justify-content: space-between;
}

.tabsContainer {
  display:flex;
  padding: 0 16px;
  gap: 10px;
  margin-top: 11px;
  align-items: center;
  border-bottom: 0.8px solid map-get($colors, TertiaryColor);
}

.tabContainerCustomClass {
  flex: 1 !important;
}

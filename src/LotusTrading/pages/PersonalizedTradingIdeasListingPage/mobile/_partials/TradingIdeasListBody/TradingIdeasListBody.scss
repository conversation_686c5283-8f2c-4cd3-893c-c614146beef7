.drawer {
  z-index: 10000 !important;
  background-color: map-get($colors, bgPopup);
  p {
    padding: 15px;
  }

  div:first-child {
    align-items: flex-start;
  }

  h2 {
    padding-bottom: 20px;
    font-weight: 500 !important;
  }
}

.marginDrawerPriority {
  z-index: 10000;
}

.cardLists {
  padding: 0 15px !important;
  list-style: none !important;
}

.noData {
  padding: 16px;
  height: calc(100vh - 190px);
  @include typography(heading2B1, map-get($colors, GreyPrimary), false, true);
  font-weight: 400;
}

.tiBottomMessage {
  width: calc(100% - 32px);
  padding: 0 16px;
  padding-bottom: 16px;
}

.tiBottomMessageText {
  padding: 16px;
  @include typography(text3B2, map-get($colors, GreyPrimary), false, true);
  font-size: 8px;
  font-weight: 400;
  line-height: 12px;
  letter-spacing: 0.02px;
  background: var(--important-info);
  border-radius: 10px;
  box-sizing: border-box;
}

import React, { useEffect, useState } from 'react';

import HeaderWithTabs from './_partials/HeaderWithTabs/index';

import TradingIdeasListBody from './_partials/TradingIdeasListBody/TradingIdeasListBody';

import { usePersonalizedTradingIdeasContext } from '../../../context/PersonalizedTradingIdeasContext';

import {
  exitApp,
  paytmChangeBottomBarColorBridge,
  paytmChangeStatusBarColorBridge,
} from '../../../../utils/bridgeUtils';
import { useBackPress } from '../../../../utils/react';
import {
  getDeeplinkDataOrQueryParam,
  isIosBuild,
} from '../../../../utils/commonUtils';

import styles from './index.scss';

function TradingIdeasListWrapperPage({
  isRefreshIcon,
  handleTabChange,
  getTradingIdeas,
  isBackIcon,
  onBackIconClick,
  isHamburgerIcon,
  title,
  tabs,
  source,
  tiBadgeData,
  ...props
}) {
  const { activeTab, setActiveTab } = usePersonalizedTradingIdeasContext(props);
  const { pushStack, clearStack } = useBackPress();
  const [ideaToSelect, setIdeaToSelect] = useState(null);

  const getURLData = () => {
    const tabToSelect = getDeeplinkDataOrQueryParam('tab');
    const callId = getDeeplinkDataOrQueryParam('callId');
    if (callId) {
      if (isIosBuild()) {
        const selectedIdea = callId?.split('?')?.[0];
        setIdeaToSelect(selectedIdea);
      } else {
        setIdeaToSelect(callId);
      }
    }

    if (tabToSelect) {
      if (isIosBuild()) {
        const selectedTab = tabToSelect?.split('?')?.[0];
        setActiveTab(selectedTab);
      } else {
        setActiveTab(tabToSelect);
      }
    }
  };

  const handleBackPress = () => {
    exitApp();
  };

  useEffect(() => {
    const style = getComputedStyle(document.body);
    // TODO: REMOVE WHEN NATIVE ISSUE IS FIXED
    paytmChangeBottomBarColorBridge(
      style.getPropertyValue('--pml-eq-primary-color'),
    );
    setTimeout(() => {
      paytmChangeStatusBarColorBridge(
        style.getPropertyValue('--pml-eq-grey-tertiary'),
      );
    }, 100);
    getURLData();
    pushStack(handleBackPress);
    return () => {
      clearStack();
    };
  }, []);
  return (
    <div className={styles.container}>
      <HeaderWithTabs
        isRefreshIcon={isRefreshIcon}
        onRefreshIconClick={getTradingIdeas}
        isBackIcon={isBackIcon}
        onBackIconClick={onBackIconClick}
        isHamburgerIcon={isHamburgerIcon}
        title={title}
        tabs={tabs}
        source={source}
        activeTab={activeTab}
        handleTabChange={handleTabChange}
        customClass={styles.headerCustomClass}
        tabContainerCustomClass={styles.tabContainerCustomClass}
      />
      <TradingIdeasListBody
        activeTab={activeTab}
        ideaToSelect={ideaToSelect}
        tiBadgeData={tiBadgeData}
      />
    </div>
  );
}

export default TradingIdeasListWrapperPage;

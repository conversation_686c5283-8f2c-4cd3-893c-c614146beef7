import React, { useState } from 'react';

import Header from '../Header';
import SubHeader from '../SubHeader';

import Table from '../../../../../components/Table';

import { useTradingIdeasContext } from '../../../../../context/TradingIdeasContext';

import {
  LOTUS_TRADING_LISTING_PAGE_TABS,
  TRADING_IDEAS_PARAMS,
  handleTabChange,
} from '../../../../../config/lotusTradingConfig';

import styles from './index.scss';

function TradingIdeasListBody({
  tabs = LOTUS_TRADING_LISTING_PAGE_TABS,
  onMenu,
  ...props
}) {
  const {
    activeQuickFilter,
    setActiveQuickFilter,
    setSelectedSortOption,
    defaultSortOption,
    activeTab,
    isMarginLoading,
    isTradingCardLoading,
    advisorDefination,
    fetchTradingIdeas,
    setActiveTab,
    tradingIdeas,
  } = useTradingIdeasContext(props);

  const quickFilterProps = {
    activeTab,
    activeQuickFilter,
    setActiveQuickFilter,
    setSelectedSortOption,
    defaultSortOption,
  };

  const [currentTableIndex, setCurrentTableIndex] = useState(null);

  const getTradingIdeas = () => {
    fetchTradingIdeas(TRADING_IDEAS_PARAMS());
  };

  return (
    <>
      <div className={styles.headerWrapper}>
        <Header
          tabs={tabs}
          onMenu={onMenu}
          activeTab={activeTab}
          onRefreshClick={getTradingIdeas}
          handleTabChange={tab =>
            handleTabChange(tab, setActiveTab, activeQuickFilter)
          }
        />
        <SubHeader quickFilterProps={quickFilterProps} />
      </div>
      <div className={styles.container}>
        {!isTradingCardLoading ? (
          <Table
            tradingIdeas={tradingIdeas}
            activeTab={activeTab}
            currentTableIndex={currentTableIndex}
            setCurrentTableIndex={setCurrentTableIndex}
            isMarginLoading={isMarginLoading}
            isTradingCardLoading={isTradingCardLoading}
            advisorDefination={advisorDefination}
            {...props}
          />
        ) : null}
      </div>
    </>
  );
}

export default TradingIdeasListBody;

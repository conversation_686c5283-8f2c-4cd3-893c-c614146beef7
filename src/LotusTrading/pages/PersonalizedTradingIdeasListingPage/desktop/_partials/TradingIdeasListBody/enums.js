import ARROW_UP from '../../../../../../assets/icons/arrow_up.png';
import ARROW_DOWN from '../../../../../../assets/icons/arrow_down.png';
import FILTER from '../../../../../../assets/icons/tradingIdeas/filter.png';
import ACTIVE_FILTER from '../../../../../../assets/icons/tradingIdeas/activeFilter.png';
import ACTIVE_SORT from '../../../../../../assets/icons/tradingIdeas/activeSort.png';
import SORT from '../../../../../../assets/icons/tradingIdeas/sort.png';
import CLOSE from '../../../../../../assets/icons/tradingIdeas/close.png';

export const FILTER_SORT = [
  {
    icon: isActive => (isActive ? ACTIVE_FILTER : FILTER),
    text: 'Filter',
    type: 'drawer',
  },
  {
    icon: isActive => (isActive ? ACTIVE_SORT : SORT),
    text: 'Sort by',
    type: 'tooltip',
  },
];

export const HEADER = [
  {
    text: 'Name',
    className: 'name',
  },
  {
    text: 'Live Price',
    className: 'livePrice',
  },
  {
    text: 'Stop Loss',
    className: 'stopLoss',
  },
  {
    text: 'Entry Price',
    className: 'entryPrice',
  },
  {
    text: 'Target Price',
    className: 'targetPrice',
  },
  {
    text: 'Date/Time',
    className: 'lastUpdateTime',
  },
  {
    text: 'Action',
    className: 'action',
  },
];

export const CLOSED_CALLS_TABLE_HEADER = [
  {
    text: 'Name',
    className: 'name',
  },
  {
    text: 'Target Price',
    className: 'targetPrice',
  },
  {
    text: 'Entry Price',
    className: 'entryPrice',
  },
  {
    text: 'Exit Price',
    className: 'exitPrice',
  },
  {
    text: 'Returns',
    className: 'returns',
  },
];

export const UP_LOGO = ARROW_UP;
export const DOWN_LOGO = ARROW_DOWN;
export const CLOSE_LOGO = CLOSE;

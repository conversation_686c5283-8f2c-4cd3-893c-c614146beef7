.container {
  top: 0;
  z-index: 12;
  width: calc(100vw - 115px);
  max-width: calc(100% - 20px);
  display: flex;
  position: sticky;
  padding-top: 16px;
  padding-bottom: 16px;
  padding-left: 60px;
  align-items: center;
  background-color: map-get($colors, OffsetPrimary);
}

.wrapper {
  display: flex;
  margin: 0 auto;
  align-items: center;
  padding-right: 55px;
  width: $desktop-max-width;
  max-width: $desktop-max-width;
  justify-content: space-between;
}

.titleContainer {
  flex: 0.4;
  display: flex;
  align-items: center;
  .logo {
    width: 24px;
    height: 24px;
    margin-right: 20px;
  }

  .title {
    @include typography(text15B, map-get($colors, PrimaryColor), false, true);
  }
}


.tabContainer {
  flex: 0.6;
  display: flex;
  position: sticky;
  align-items: center;

  justify-content: space-between;

  .refreshButton {
    gap: 4px;
    display: flex;
    cursor: pointer;
    padding: 8px 12px;
    width: fit-content;
    border-radius: 6px;
    align-items: center;

    background: map-get($colors, BrandPrimary);

    @include typography(text1B, map-get($colors, PureWhite), false, true);

    img {
      width: 16px;
      height: 16px;
    }
  }
}

.tabContainerCustomClass {
  width: 260px;
  border-bottom: none !important;

  > div {
    padding: 0 !important;
  }

  span {
    @include typography(text15D, map-get($colors, PrimaryColor), false, true);
  }
}

.activeTab {

  span {
    color: map-get($colors, TextLink) !important;
  }
}

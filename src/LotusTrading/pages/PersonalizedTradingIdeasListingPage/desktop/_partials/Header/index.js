import React from 'react';

import Tab from '../../../../../components/Tab/Tab';

import { REFRESH, REFRESH_ICON, TITLE, TRADING_IDEAS_LOGO } from './enums';

import styles from './index.scss';

function Header({ tabs, activeTab, handleTabChange, onRefreshClick }) {
  return (
    <div className={styles.container}>
      <div className={styles.wrapper}>
        <div className={styles.titleContainer}>
          <img className={styles.logo} src={TRADING_IDEAS_LOGO} alt="" />
          <div className={styles.title}>{TITLE}</div>
        </div>
        <div className={styles.tabContainer}>
          {tabs && (
            <Tab
              tabs={tabs}
              activeTab={activeTab}
              showUnderline={false}
              tabOnClick={handleTabChange}
              customActiveTab={styles.activeTab}
              tabContainerCustomClass={styles.tabContainerCustomClass}
            />
          )}
          <div className={styles.refreshButton} onClick={onRefreshClick}>
            {REFRESH}
            <img src={REFRESH_ICON} alt="" />
          </div>
        </div>
      </div>
    </div>
  );
}

export default Header;

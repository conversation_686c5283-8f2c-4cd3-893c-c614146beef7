.container {
  margin: 20px auto 60px;
  display: flex;
  justify-content: flex-end;
  width: $desktop-max-width;
  max-width: 1082px;
  .content {
    width: 70%;
    @include typography(text1R, map-get($colors, Grey7));
  }

  .btnWrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 20px;
    padding: 9px 15px;
    border-radius: 20px;
    border: solid 1px map-get($colors, DBlue2);
    cursor: pointer;
    img {
      height: 13px;
      width: 13px;
      margin-top: -2px;
    }

    span {
      @include typography(text1RB, map-get($colors, DBlue1));
      margin-left: 5px;
    }
  }
}

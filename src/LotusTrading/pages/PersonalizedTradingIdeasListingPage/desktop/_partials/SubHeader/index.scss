.subHeadingContainer {
  background-color: map-get($colors, PureWhite);
}

.quickFilters {
  top: 0;
  z-index: 100;
  display: flex;
  position: relative;
  padding: 14px 0 0;
  align-items: center;
  overflow: visible;
  justify-content: space-between;
  background-color: map-get($colors, PureWhite);
  box-shadow: 0px 13px 8px -4px map-get($colors, ShadowColor7) inset;
  -moz-box-shadow: 0px 13px 8px -4px map-get($colors, ShadowColor7) inset;
  -webkit-box-shadow: 0px 13px 8px -4px map-get($colors, ShadowColor7) inset;

  > div {
    &:nth-child(1) {
      &:first-child {
       > div {
        &:first-child {
          padding: 0 !important;
       }
      }
    }
  }
}
}

.filterContainer {
  flex: 1;
  gap: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.filterTooltip {
  height: 70vh;
  max-height: 783px;
  overflow: auto;
  border-radius: 10px;
  background-color: map-get($colors, PureWhite);
}

.headingWrapper {
  top: 0;
  display: flex;
  position: sticky;
  padding: 16px 16px 12px 16px;
  justify-content: space-between;
  background-color: map-get($colors, PureWhite);
  border-bottom: 1px solid map-get($colors, TertiaryColor);

  .heading {
    @include typography(title3B, map-get($colors, PrimaryColor), false, true);
  }
}

.customChipClass {
  cursor: pointer;
  border: none;
  padding: 14px 20px;
  background-color: map-get($colors, PureWhite);

 span {
  @include typography(heading13B, map-get($colors, GreyPrimary), false, true);
 }
}

.customActiveChip {
  border-radius: 0px;
  border-bottom: 2px solid map-get($colors, BrandPrimary);
  span {
    color: map-get($colors, TextLink);
  }
}

.btnContainer {
  gap: 10px;
  display: flex;
  cursor: pointer;
  padding: 6px 0px;
  width: fit-content;
  height: fit-content;
  align-items: center;
  align-self: flex-end;

  .btnText {
    @include typography(heading13B, map-get($colors, GreyPrimary), false, true);
  }

  .sortLogo {
    width: 20px;
    height: 20px;
  }

}
.active {

  .btnText {
    color: map-get($colors, TextLink);
  }
}

.tooltipContainer {
  display: flex;
  position: relative;
}

.tooltipVisibility {
  z-index: 1000;
}

.tooltip {
  width: 476px;
  height: fit-content;
  margin-left: -160px;
  background-color: map-get($colors, PureWhite);

  box-shadow: 0px;
}

.sortTooltip {
  width: 483px;
  height: fit-content;
  margin-left: -160px;
}


.customFilterWrapperClass {
  gap: 16px;
  display: grid;
  grid-template-columns: auto auto;

  > div {
    &:first-child {
      margin-top: -35px;
      margin-right: 40px;
    }
  }
}

.content {
  padding-right: 16px;
}

.close {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

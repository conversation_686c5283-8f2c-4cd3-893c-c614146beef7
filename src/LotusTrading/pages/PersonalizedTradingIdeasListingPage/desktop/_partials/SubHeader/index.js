import React, { Fragment, useEffect, useState } from 'react';
import classNames from 'classnames';

import QuickFilterSection from '../../../../../components/QuickFilterSection';

import Tooltip, { DIRECTIONS } from '../../../../../../components/Tooltip';

import useFiltersUtil from '../../../../../components/FilterAndSort/utils';
import Sorts from '../../../../../components/FilterAndSort/partials/Sorts/Sorts';
import Filters from '../../../../../components/FilterAndSort/partials/Filter/Filters';

import { useTradingIdeasContext } from '../../../../../context/TradingIdeasContext';

import { CLOSE_LOGO, FILTER_SORT } from '../TradingIdeasListBody/enums';

import styles from './index.scss';

let filterCountTemp = 0;
function SubHeader({
  quickFilterProps,
  customQuickFiltersClass,
  isMoreFilters = true,
  customChipClass,
  customActiveChip,
}) {
  const {
    activeTab,
    setSelectedSortOption,
    currentSortOption,
  } = useTradingIdeasContext();

  const [filterCount, setFilterCount] = useState(0);
  const [sortTooltip, setSortTooltip] = useState(null);
  const [activeFilter, setActiveFilter] = useState(null);
  const [filterTooltip, setFilterTooltip] = useState(null);

  const handleFilterDrawerClose = () => {
    setFilterTooltip(false);
    setActiveFilter(null);
  };

  const {
    selectedFilters,
    filtersPrevState,
    handleApplyFilter,
    setFiltersPrevState,
    setSelectedFilters,
    isFilterApplied,
    setFilterApplied,
    isDefaultSaved,
    setIsDefaultSaved,
  } = useFiltersUtil({ onFilterClose: handleFilterDrawerClose });

  const onSortClose = () => {
    setSortTooltip(false);
    setActiveFilter(null);
  };

  const onSortTooltipClose = () => {
    onSortClose();
    setSelectedSortOption(currentSortOption);
  };

  const renderTooltipContent = () => (
    <div>
      <div className={styles.headingWrapper}>
        <div className={styles.heading}>{FILTER_SORT[1].text}</div>
        <img
          className={styles.close}
          src={CLOSE_LOGO}
          alt=""
          onClick={onSortClose}
        />
      </div>
      <div className={styles.content}>
        <Sorts
          activeTab={activeTab}
          handleSortDrawerClose={onSortClose}
          onCancel={onSortClose}
        />
      </div>
    </div>
  );

  const countFilters = count => {
    const latestCount = count + filterCountTemp;
    if (latestCount < 0) return;
    filterCountTemp = latestCount;
  };

  const setFilterToPrevState = () => {
    if (filtersPrevState?.length && isFilterApplied) {
      filterCountTemp = filterCount;
      setSelectedFilters(JSON.parse(filtersPrevState));
    } else if (!filtersPrevState) {
      filterCountTemp = 0;
      setFilterCount(() => 0);
    } else if (!filtersPrevState && !isFilterApplied) {
      setSelectedFilters({});
    }
  };

  const onFilterDrawerClose = () => {
    handleFilterDrawerClose();
    setFilterToPrevState();
    if (!isFilterApplied) {
      setSelectedFilters({});
      setFiltersPrevState(null);
    }
  };
  const renderFilterTooltipContent = () => (
    <div className={styles.filterTooltip}>
      <div className={styles.headingWrapper}>
        <div className={styles.heading}>{FILTER_SORT[0].text}</div>
        <img
          className={styles.close}
          src={CLOSE_LOGO}
          alt=""
          onClick={onFilterDrawerClose}
        />
      </div>
      <Filters
        showClearAll={false}
        activeTab={activeTab}
        handleFilterDrawerClose={onFilterDrawerClose}
        selectedFilters={selectedFilters}
        setSelectedFilters={setSelectedFilters}
        isDesktop={'__BUILD_PATH__' === 'desktop'}
        setFilterApplied={value => {
          setFilterApplied(value);
        }}
        isFilterApplied={isFilterApplied}
        setFiltersPrevState={setFiltersPrevState}
        setFilterToPrevState={setFilterToPrevState}
        countFilters={countFilters}
        handleApplyFilter={() => {
          handleApplyFilter();
        }}
        isDefaultSaved={isDefaultSaved}
        setIsDefaultSaved={setIsDefaultSaved}
        customFilterWrapperClass={styles.customFilterWrapperClass}
      />
    </div>
  );

  const renderSortBtns = (item, index) => (
    <div
      key={item.text}
      className={classNames(styles.btnContainer, {
        [styles.active]: activeFilter === index,
      })}
    >
      <div className={styles.btnText}>{item.text}</div>
      <img
        className={styles.sortLogo}
        src={item.icon(activeFilter === index)}
        alt=""
      />
    </div>
  );

  const sortBtns = () => (
    <div className={styles.filterContainer}>
      {FILTER_SORT.map((item, index) => (
        <Fragment key={item.text}>
          {item.type === 'tooltip' ? (
            <div className={styles.tooltipContainer}>
              <Tooltip
                placement={DIRECTIONS.BOTTOM}
                content={renderTooltipContent()}
                space={5}
                className={styles.sortTooltip}
                hideArrow
                isOpen={sortTooltip}
                onTooltipOpen={() => {
                  setSortTooltip(true);
                  setActiveFilter(index);
                }}
                onClose={() => {
                  setActiveFilter(null);
                  onSortTooltipClose();
                }}
                contentStyle={styles.tooltipVisibility}
              >
                {renderSortBtns(
                  {
                    ...item,
                  },
                  index,
                )}
              </Tooltip>
            </div>
          ) : (
            <div className={styles.tooltipContainer}>
              <Tooltip
                placement={DIRECTIONS.BOTTOM}
                className={styles.tooltip}
                content={renderFilterTooltipContent()}
                space={5}
                hideArrow
                isOpen={filterTooltip}
                onTooltipOpen={() => {
                  setFilterTooltip(true);
                  setActiveFilter(index);
                }}
                onClose={() => {
                  setActiveFilter(null);
                  setFilterToPrevState();
                  onFilterDrawerClose();
                }}
                contentStyle={styles.tooltipVisibility}
              >
                {renderSortBtns(item, index)}
              </Tooltip>
            </div>
          )}
        </Fragment>
      ))}
    </div>
  );

  useEffect(() => {
    if (!filtersPrevState) {
      filterCountTemp = 0;
      setFilterCount(0);
    }
    if (filtersPrevState) {
      setFilterCount(filterCountTemp);
    }
  }, [filtersPrevState]);

  useEffect(() => {
    // to prevent body scroll
    if (sortTooltip || filterTooltip) {
      const body = document.querySelector('#ra-main-content');
      if (body) body.style.overflow = 'hidden';
    }
    return () => {
      if (sortTooltip || filterTooltip) {
        const body = document.querySelector('#ra-main-content');
        if (body) body.style.overflow = null;
      }
    };
  }, [sortTooltip, filterTooltip]);

  return (
    <div className={styles.subHeadingContainer}>
      <QuickFilterSection
        {...quickFilterProps}
        customClass={classNames(styles.quickFilters, customQuickFiltersClass)}
        moreFilters={isMoreFilters ? sortBtns() : null}
        customChipClass={customChipClass || styles.customChipClass}
        customActiveChip={customActiveChip || styles.customActiveChip}
      />
    </div>
  );
}

export default SubHeader;

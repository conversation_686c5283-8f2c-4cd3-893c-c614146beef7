.container {
  width: 100vw;
  display: flex;
  overflow: hidden;
  font-size: initial;
  position: relative;
  align-items: flex-start;
  height: calc(100vh - 50px);
  background-color: map-get($colors, PureWhite);
}

.mainContent {
  top: 0;
  overflow: auto;
  position: relative;
  height: calc(100vh - 2px);
  background-color: map-get($colors, PureWhite);
}

.noteWrapper {
   width: 1046px;
   max-width: $desktop-max-width;

   margin: 0 auto;
   padding: 12px 16px;
   margin-bottom: 60px;

   background: map-get($colors, YellowPrimary);

   border-radius: 10px;
   border: 0.5px solid map-get($colors, TertiaryColor);

   .heading {
    display: flex;
    margin-bottom: 10px;
    align-items: center;
    img {
      width: 20px;
      height: 20px;
      margin-right: 6px;
     }

     .note {
      @include typography(heading2B1, map-get($colors, GreyPrimary), false, true);
     }
   }
}

.noteContainer {
  @include typography(text1R, map-get($colors, GreyPrimary), false, true);
}


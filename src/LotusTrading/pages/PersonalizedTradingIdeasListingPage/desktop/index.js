import React, { useEffect, useRef, useState } from 'react';

import { NOTE, NOTE_CONTENT } from '../../../config/lotusTradingConfig';

import Loader from '../../../../components/Loader/Loader';

import HeaderLayout from '../../../../layout/HeaderLayout/HeaderLayout';

import Footer from './_partials/Footer';
import HamburgerMenuWrapper from '../../../components/HamburgerMenu';
import TradingIdeasListBody from './_partials/TradingIdeasListBody';

import history from '../../../../history';
import { navigateTo } from '../../../../services/coreUtil';

import BELL from '../../../../assets/icons/bell.png';

import { LOTUS_TRADING_ROUTES } from '../../../config/routeConfig';
import { getTncStatus } from '../../../query/lotusTradingQuery';

import { useDataFeedServiceLayer } from '../../../../context/DataFeedDesktop';
import { SCREEN_NAME_RA_LANDING } from '../../../analyticsEvents/tradingIdeasEventEnum';
import { sendWebPageOpenEvent } from '../../../analyticsEvents/tradingIdeasEventUtils';

import styles from './index.scss';

function TradingIdeasListWrapperPage({ parentProps, getTradingIdeas }) {
  const containerRef = useRef();
  const { sendWebPulseEvent } = useDataFeedServiceLayer();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isTncStatusLoading, setTncStatusLoading] = useState(true);
  const headerProps = {
    hideHeader: true,
  };

  const handleRetry = () => {
    getTradingIdeas();
  };

  const onMenu = () => {
    setIsMenuOpen(prev => !prev);
  };

  useEffect(() => {
    sendWebPageOpenEvent(SCREEN_NAME_RA_LANDING, sendWebPulseEvent);
    async function fetchData() {
      const { data: tncStatus } = await getTncStatus();
      if (!tncStatus.data.isLatestAccepted) {
        navigateTo(history, `.${LOTUS_TRADING_ROUTES.TNC}`, {}, 'replace');
      } else {
        setTncStatusLoading(false);
      }
    }
    fetchData();
  }, []);

  if (isTncStatusLoading) {
    return <Loader />;
  }

  return (
    <HeaderLayout
      {...headerProps}
      helperIconClick={handleRetry}
      {...parentProps}
    >
      <div className={styles.container}>
        <HamburgerMenuWrapper isOpen={isMenuOpen} onClose={onMenu} />
        <div
          className={styles.mainContent}
          id="ra-main-content"
          ref={containerRef}
        >
          <TradingIdeasListBody onMenu={onMenu} {...parentProps} />
          <div className={styles.noteWrapper}>
            <div className={styles.heading}>
              <img src={BELL} alt="" />
              <div className={styles.note}>{NOTE}</div>
            </div>
            <div className={styles.noteContainer}>{NOTE_CONTENT}</div>
          </div>
          <Footer containerRef={containerRef} />
        </div>
      </div>
    </HeaderLayout>
  );
}

export default TradingIdeasListWrapperPage;

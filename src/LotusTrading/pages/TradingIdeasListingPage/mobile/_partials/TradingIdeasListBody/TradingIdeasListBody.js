import React, { useEffect, useState } from 'react';

import Drawer, { useDrawer } from '../../../../../components/Drawer';

import TradingIdeasCardLoader from '../TradingIdeasCardLoader';
import TradingIdeasCard from '../TradingIdeasCard/TradingIdeasCard';

import ExpandedCardDrawer from '../ExpandedCard/ExpandedCardDrawer';

import { useTradingIdeasContext } from '../../../../../context/TradingIdeasContext';
import { fetchUserReadiness } from '../../../../../query/lotusTradingQuery';

import { isPaytmMoney } from '../../../../../../utils/commonUtils';
import { enableScreenShot } from '../../../../../../utils/bridgeUtils';
import { ENABLE_SCREENSHOT_MODE } from '../../../../../../utils/Constants';
import { NO_DATA } from '../../../../../constants';

import styles from './TradingIdeasListBody.scss';

function TradingIdeasListBody({ activeTab, ideaToSelect }) {
  const {
    tradingIdeas,
    isMarginLoading,
    isTradingCardLoading,
    advisorDefination,
  } = useTradingIdeasContext();
  const {
    onOpen: onMarginOpen,
    onClose: onMarginClose,
    isOpen: isMarginOpen,
    data: drawerContent,
  } = useDrawer();

  const {
    onOpen: onExpandedCardOpen,
    onClose: onExpandedCardClose,
    isOpen: isExpandedCardOpen,
    data: expandedCardData,
  } = useDrawer();

  const [isUserFNOReady, setIsUserFNOReady] = useState(false);
  const [isFNOLoading, setIsFNOLoading] = useState(false);

  const showAdvisorDefination = subcategory => {
    const advisor_defination = advisorDefination?.[subcategory];
    if (advisor_defination) onMarginOpen(advisor_defination);
  };

  useEffect(() => {
    if (!isPaytmMoney()) {
      setIsFNOLoading(true);
      fetchUserReadiness()
        .then(({ data }) => {
          if (!data) return;
          const fnoStatus = data?.EQUITY?.find(
            item => item.subProduct === 'FO',
          );
          setIsUserFNOReady(fnoStatus?.irStatus?.toUpperCase() === 'ACTIVE');
          setIsFNOLoading(false);
        })
        .catch(() => setIsFNOLoading(false));
    }

    enableScreenShot(false, ENABLE_SCREENSHOT_MODE.RESTRICTED);
  }, []);

  return (
    <>
      {isTradingCardLoading ? (
        [...Array(3)].map((_, index) => (
          <TradingIdeasCardLoader key={index + 1} />
        ))
      ) : tradingIdeas?.[activeTab]?.length === 0 ? (
        <div className={styles.noData}>{NO_DATA}</div>
      ) : (
        <ul className={styles.cardLists}>
          {tradingIdeas?.[activeTab]?.map((idea, index) => (
            <div className={`card-${idea.callid}`} key={idea.uniqueId}>
              <TradingIdeasCard
                index={index}
                data={idea}
                activeTab={activeTab}
                isMarginLoading={isMarginLoading}
                onMarginOpen={onMarginOpen}
                isUserFNOReady={isUserFNOReady}
                isFNOLoading={isFNOLoading}
                advisorDefination={advisorDefination?.[idea.subcategory]}
                showAdvisorDefination={() =>
                  showAdvisorDefination(idea?.subcategory)
                }
                onCardClick={item => onExpandedCardOpen(item)}
                ideaToSelect={ideaToSelect}
                list={tradingIdeas?.[activeTab]}
              />
            </div>
          ))}
        </ul>
      )}

      <Drawer
        isOpen={isMarginOpen}
        onClose={onMarginClose}
        showCross
        title={drawerContent}
        className={styles.drawer}
        wrapperClass={styles.marginDrawerPriority}
      />

      <ExpandedCardDrawer
        onExpandedCardClose={onExpandedCardClose}
        isExpandedCardOpen={isExpandedCardOpen}
        expandedCardData={expandedCardData}
        activeTab={activeTab}
        onMarginOpen={onMarginOpen}
        advisorDefination={advisorDefination}
        isUserFNOReady={isUserFNOReady}
      />
    </>
  );
}

export default TradingIdeasListBody;

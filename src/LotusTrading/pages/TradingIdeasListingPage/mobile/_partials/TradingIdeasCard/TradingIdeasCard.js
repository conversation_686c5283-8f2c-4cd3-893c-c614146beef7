import React, { useEffect } from 'react';
import classNames from 'classnames';

import Card<PERSON>ody from '../CardBody';
import DownloadReport from '../DownloadReport';
import CardHeader from '../../../../../components/CardHeader';
import CardFooter from '../../../../../components/CardFooter';

import {
  ALL,
  CARD_FIELDS,
  FiltersCategories,
  LOTUS_TRADING_LISTING_PAGE_TABS,
  CALL_EXPIRY,
  processExpiryDate,
  processTradingIdeasResponse,
} from '../../../../../config/lotusTradingConfig';

import useMargin from '../../../../../hooks/useMargin';

import { SEGMENT_TYPES } from '../../../../../../utils/enum';

import { CALL_TYPE } from '../../../../../config/expertPage';

import styles from './TradingIdeasCard.scss';

let isCardCalled = false;
function TradingIdeasCard({
  data,
  activeTab,
  onMarginOpen,
  customClass,
  onCardClick,
  isFNOLoading,
  isUserFNOReady,
  advisorDefination,
  ideaToSelect,
  list,
  showAdvisorDefination = () => {},
}) {
  const {
    name,
    callexpiry,
    entry,
    stopLoss,
    tag1,
    securityId,
    tag2,
    targetPrice,
    rationale,
    date_time,
    status,
    calltype,
    pmlId,
    exitCondition,
    sebi_details,
    provider_name,
    advisor,
    advisorid,
    internalremarks,
    futureOption,
    lot_size,
    margin,
  } = processTradingIdeasResponse(data);

  const isBuy = calltype?.toLowerCase() === CALL_TYPE.BUY;
  const { Tag1, Status } = FiltersCategories;
  const { TARGET_PRICE, ENTRY_PRICE, STOP_LOSS, SHORT_TERM } = CARD_FIELDS;
  // SEGMENT
  const { DERIVATIVES, CASH } = SEGMENT_TYPES;

  const segment =
    activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0] ? DERIVATIVES : CASH;

  const { onMargin } = useMargin({
    activeTab,
    securityId,
    segment,
    futureOption: futureOption || ' ',
    lot_size,
    isBuy,
    margin,
    pmlId,
    priceWhenCreated: entry,
    calltype,
  });
  onMargin();

  useEffect(() => {
    let timer;
    if (ideaToSelect) {
      timer = setTimeout(() => {
        const card = document.querySelector(`.card-${ideaToSelect}`);
        if (card && !isCardCalled) {
          card.scrollIntoView({ behavior: 'smooth' });
          const item = list.find(item => item.callid === Number(ideaToSelect));
          onCardClick({
            advisorDefination,
            margin: onMargin(),
            ...item,
          });
          isCardCalled = true;
        } else {
          console.warn(`Card with class .card-${ideaToSelect} not found`);
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, []);

  const onClick = () => {
    onCardClick({
      advisorDefination,
      margin: onMargin(),
      ...data,
    });
  };

  // TAGS
  const renderTags = () => {
    const renderTag1 = tag1 && tag1 === Tag1[1] ? SHORT_TERM : tag1;
    const renderTag2 = () => {
      if (activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]) {
        return `IdeaExpiry: ${callexpiry.substr(
          0,
          callexpiry.lastIndexOf(' '),
        )}`;
      }
      if (callexpiry) {
        return `IdeaExpiry: ${callexpiry.substr(
          0,
          callexpiry.lastIndexOf(' '),
        )}`;
      }

      if (tag2) {
        return tag2;
      }
    };
    const renderTag3 =
      rationale !== undefined && rationale.length !== 0 && rationale;
    return [
      {
        id: 1,
        tag: renderTag1,
        icon: false,
      },
      {
        id: 2,
        tag: renderTag2(),
        icon: false,
      },
      {
        id: 3,
        tag: renderTag3,
        onClick: showAdvisorDefination,
        icon: !!advisorDefination,
      },
    ].filter(val => val.tag !== undefined && val.tag !== false);
  };

  // RENDER CARD FIELDS
  const priceRange = [
    {
      id: 1,
      belongsTo: ALL,
      value: stopLoss,
      label: STOP_LOSS,
    },
    {
      id: 2,
      belongsTo: ALL,
      value: entry,
      label: ENTRY_PRICE,
    },
    {
      id: 3,
      belongsTo: ALL,
      value: targetPrice,
      label: TARGET_PRICE,
    },
  ];

  // PROPS
  const headerProps = {
    renderTags,
    pmlId,
    name,
    status,
    Status,
    sebi_details,
    provider_name,
    isBuy,
    date_time,
    onMarginOpen,
    advisor,
    advisorid,
    categories: [
      {
        id: 1,
        tag: [tag1 && tag1 === Tag1[1] ? SHORT_TERM : tag1],
        icon: false,
      },
    ],
  };

  const bodyProps = {
    isBuy,
    priceRange,
    activeTab,
    securityId,
    segment,
    stopLoss,
    entry,
    targetPrice,
    isExit: exitCondition && status !== Status[0],
  };

  const footerProps = {
    activeTab,
    status,
    Status,
    tag1,
    isFNOLoading,
    isUserFNOReady,
    exit: {
      isExit: exitCondition && status !== Status[0],
      value: exitCondition,
    },
    isBuy,
    customClass: styles.footer,
    ...data,
  };

  return (
    <div onClick={onClick}>
      <li className={classNames(styles.cardList, customClass)}>
        {/* top section  */}
        <CardHeader {...headerProps} />

        {/* body section  */}
        <CardBody {...bodyProps} />

        {/* footer section */}
        <CardFooter {...footerProps} />
      </li>
      {activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[1] ? (
        <div className={styles.equityBottom}>
          <div className={styles.callExpiry}>
            {CALL_EXPIRY} •{' '}
            <span>{processExpiryDate(callexpiry?.replace(/\s/, 'T'))}</span>
          </div>
          <DownloadReport internalremarks={internalremarks} />
        </div>
      ) : null}
    </div>
  );
}

export default TradingIdeasCard;

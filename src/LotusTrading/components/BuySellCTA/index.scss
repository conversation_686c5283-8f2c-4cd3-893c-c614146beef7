.button {
  gap: 4px;
  height: 100%;
  flex-grow: 0;
  display: flex;
  cursor: pointer;
  padding: 4px 12px;
  align-items: center;
  justify-content: center;

  border: none;
  border-radius: 6px;
  color: map-get($colors, PureWhite);
  background-color: map-get($colors, PureWhite);

  @include typography(text1R1B2, map-get($colors, PureWhite), false, true);
  line-height: 16px;

  img {
    width: 6px;
    height: 10px;

    margin-top: -1.3px;
  }

  &__bracket {
    padding: 0 15px;
    margin-right: 12px;
    background-color: transparent;
    color: map-get($colors, SuccessPrimary);
  }

  &__sell {
    background-color: map-get($colors, lossErrorColor);
  }

  &__buy {
    background-color: map-get($colors, SuccessPrimary);
  }

  &__intradayBuy, &__intradaySell {
    margin-right: 12px;
    background-color: transparent;
  }

  &__intradayBuy {
    color: map-get($colors, SuccessPrimary);
  }

  &__intradaySell {
    color: map-get($colors, lossErrorColor);
  }
}

.btnWrapper {
  height: 100%;
  display: flex;
}

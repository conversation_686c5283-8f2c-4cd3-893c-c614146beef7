import React from 'react';
import classNames from 'classnames';

import {
  FiltersCategories,
  LOTUS_TRADING_LISTING_PAGE_TABS,
  getBuySellCTA,
  getIntradayBtnText,
  navigateToOrderScreen,
} from '../../config/lotusTradingConfig';

import { getBracketBtnText } from '../../config/tradingIdeasUtils';

import styles from './index.scss';

const getRightArrow = stroke => (
  <svg
    width="6"
    height="10"
    viewBox="0 0 6 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1 1L5 5L1 9"
      stroke={stroke}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

function BuySellCTA({
  buttonText,
  customButtonClass,
  Icon,
  onBuyClickEvent,
  ...data
}) {
  return (
    <button
      onClick={e => {
        e.stopPropagation();
        navigateToOrderScreen({ ...data });
        onBuyClickEvent();
      }}
      className={classNames(styles.button, customButtonClass)}
    >
      {buttonText} {Icon || null}
    </button>
  );
}

export const bracketOrderButton = (isBuy, activeTab, data) => {
  const buttonText = getBracketBtnText(isBuy);
  const buttonStyle = isBuy
    ? styles.button__bracket
    : styles.button__intradaySell;

  return (
    <BuySellCTA
      buttonText={buttonText}
      customButtonClass={buttonStyle}
      Icon={getRightArrow(
        isBuy ? styles.SuccessPrimary : styles.lossErrorColor,
      )}
      activeTab={activeTab}
      {...data}
    />
  );
};

export const buySellButton = (
  activeTab,
  isBuy,
  status,
  data,
  onBuyClickEvent,
) => {
  const { Status } = FiltersCategories;
  if (status !== Status[0]) return <div />;
  const buttonText = isBuy
    ? getBuySellCTA(activeTab).BUY
    : getBuySellCTA(activeTab).SELL;
  const buttonStyle = isBuy ? styles.button__buy : styles.button__sell;

  return (
    <div className={styles.btnWrapper}>
      <BuySellCTA
        buttonText={buttonText}
        customButtonClass={buttonStyle}
        Icon={getRightArrow(styles.PureWhite)}
        activeTab={activeTab}
        {...data}
        tag1={FiltersCategories.Tag1[2]}
        onBuyClickEvent={onBuyClickEvent}
      />
    </div>
  );
};

export const intradayButton = (isBuy, status, tag1, activeTab, data) => {
  const { Tag1, Status } = FiltersCategories;
  const buttonText = getIntradayBtnText(isBuy);
  const buttonStyle = isBuy
    ? styles.button__intradayBuy
    : styles.button__intradaySell;

  if (activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[1]) return null;

  if (tag1 === Tag1[0] && status === Status[0]) {
    return (
      <BuySellCTA
        buttonText={buttonText}
        Tag1={Tag1}
        customButtonClass={buttonStyle}
        Icon={getRightArrow(
          isBuy ? styles.SuccessPrimary : styles.lossErrorColor,
        )}
        activeTab={activeTab}
        {...data}
      />
    );
  }
  return <div />;
};

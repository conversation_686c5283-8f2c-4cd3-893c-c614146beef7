import React from 'react';
import cx from 'classnames';
import TimeIcon from '../../../assets/icons/time_icon.svg';

import styles from './dateTime.scss';

const DateTime = ({ dateTime, customClass, isIcon = true }) => (
  <div className={cx(customClass, styles.dateTimeWrapper)}>
    {isIcon ? (
      <img alt="time-icon" src={TimeIcon} className={styles.timeIcon} />
    ) : null}
    <span className={styles.date}>{dateTime}</span>
  </div>
);

export default DateTime;

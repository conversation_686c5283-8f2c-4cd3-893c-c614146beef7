import { openDeepLinkPaytmMoney } from '../../../utils/bridgeUtils';
import { convertDate } from '../../../utils/commonUtils';

const subtractMonths = (date, month) => {
  // 👇 Make copy with "Date" constructor
  const dateCopy = new Date(date);
  dateCopy.setMonth(dateCopy.getMonth() - month);
  return dateCopy;
};

export const convertedDateFormat = month => {
  const date = new Date();
  return convertDate(subtractMonths(date, month));
};

export const getCurrentDate = () => {
  const date = new Date();
  return convertDate(date);
};

export const numDifferentiation = value => {
  let val = Math.abs(value);
  if (val >= 10000000) {
    val = `${(val / 10000000).toFixed(2)} Crores`;
  } else if (val >= 100000) {
    val = `${(val / 100000).toFixed(2)} Lakhs`;
  }

  return val.toFixed(2);
};

export const handleAdvisoryDeepLink = deeplink => {
  openDeepLinkPaytmMoney(deeplink);
};

export const sortBasedOnSlotID = arr =>
  arr.sort((a, b) => a.slotId.localeCompare(b.slotId));

export const splitString = string => string.split('<span>');

const splitText = (string, indexOne, indexTwo) =>
  string?.split(',')[indexOne]?.split(':')[indexTwo];

export const modifyText = text => {
  const text1 = text?.match(/{(.*?)}/g);

  return text1.map(t => {
    const actual = t.substr(t.indexOf('{') + 1, t.indexOf('}') - 1);
    const key = splitText(actual, 0, 0);
    const value = splitText(actual, 0, 1);
    const isBold = splitText(actual, 1, 1);
    return {
      [key]: value?.trim(),
      isBold: isBold?.trim(),
    };
  });
};

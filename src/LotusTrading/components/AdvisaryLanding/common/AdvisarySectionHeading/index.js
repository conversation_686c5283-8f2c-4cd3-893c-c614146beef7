import React from 'react';
import classNames from 'classnames';
import { ADVISARY_LANDING_PAGE } from '../../enums';
import Button from '../../common/Link';
import styles from './advisarySection.scss';

const AdvisarySectionHeading = ({
  subHeading,
  customClass,
  isDescription,
  description,
  isViewAll,
  id,
  onClick = () => {},
}) => (
  <div className={classNames(styles.sectionHeading, customClass)}>
    <div className={styles.flex}>
      <h2 className={styles.subheading} id={id}>
        {subHeading}
      </h2>
      {isViewAll && (
        <Button
          linkText={ADVISARY_LANDING_PAGE.BUTTON_TEXT.VIEW_ALL}
          onClick={onClick}
        />
      )}
    </div>
    {isDescription && (
      <p className={styles.description}>
        {description || ADVISARY_LANDING_PAGE.DESCRIPTION}
      </p>
    )}
  </div>
);

export default AdvisarySectionHeading;

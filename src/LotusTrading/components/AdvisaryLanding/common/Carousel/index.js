import React from 'react';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import styles from './carousel.scss';

const carouselOptions = {
  dots: false,
  arrows: false,
  infinite: false,
  autoplay: false,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1,
  variableWidth: true,
  adaptiveHeight: true,
  appendDots: dots => (
    <div>
      <ul className={styles.indicatorList}>{dots}</ul>
    </div>
  ),
  customPaging: () => (
    <div className={styles.indicatorContainer}>
      <div className={styles.indicator} />
    </div>
  ),
};

const Carousel = ({ options, children }) => {
  const settings = { ...carouselOptions, ...options };
  return (
    <div className={styles.carouselBanner}>
      <Slider {...settings}>{children}</Slider>
    </div>
  );
};

export default Carousel;

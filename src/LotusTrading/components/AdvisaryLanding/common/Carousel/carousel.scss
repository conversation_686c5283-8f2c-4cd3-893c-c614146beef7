.carouselBanner {
  .indicatorList {
    display: flex;
    align-items: center;
    margin: 0;
  }

  .indicator {
    width: 0.4rem;
    height: 0.4rem;
    border-radius: 2rem;
    background-color: map-get($colors, Grey4);
    cursor: pointer;
  }

  :global(.slick-slide)>div {
    margin: 0 10px;
  }

  :global(.slick-list) {
    overflow: hidden;
    margin: 0 -10px;
  }

  :global(.slick-track[style]) {
    display: flex;
    margin-left: 0px;
  }

  :global(.slick-dots) {
    position: static;
    margin-top: 20px;
  }

  :global(.slick-dots) li {
    width: auto;
    margin: 0 2px;
  }

  :global(li[class="slick-active"]) div div {
    background: map-get($colors, DBlue3);
    width: 0.7rem;
    height: 0.4rem;
  }
}

import React, { useEffect, useState } from 'react';

import StocksSIPCard from './StocksSIPCard';

import {
  DownloadIcon,
  VectorStrokeRed,
} from '../../../../assets/icons/advisary';

import { getInfoMsgJson } from '../../../../actions/commonActions';
import { sendCTAClickEvent } from '../../../analyticsEvents/masterLandingEventUtils';
import { USER_ACTION } from '../../../analyticsEvents/festiveListing';

import { AdvisarySectionHeading } from '../common';
import { log } from '../../../../utils/commonUtils';

import { ADVISARY_LANDING_PAGE } from '../enums';
import { SIP_RECO_API } from '../../../pages/AdvisaryLandingPage/enums';
import { CATEGORY } from '../../../analyticsEvents/masterLandingEnums';

import styles from './index.scss';

const StocksSIP = ({ stocksSIP, ...props }) => {
  const [advisaryHeading, setAdvisaryHeading] = useState({});
  const [stocksCard, setStocksCard] = useState([]);
  const { BUTTON_TEXT, STOCKS_IDEA_SIP } = ADVISARY_LANDING_PAGE;
  const { DOWNLOAD_REPORT, POWERED_BY } = STOCKS_IDEA_SIP;

  const fetchSIPRecommendation = async () => {
    try {
      const res = await getInfoMsgJson(SIP_RECO_API, props.axiosSource, true);
      if (!res.data) return;
      setStocksCard(res.data);
      setAdvisaryHeading({
        header: res.header,
        subheader: res.subheader,
      });
    } catch (e) {
      log(e);
    }
  };

  useEffect(() => {
    fetchSIPRecommendation();
  }, []);

  const stocksProps = {
    VectorStrokeRed,
    buttonText: BUTTON_TEXT.CREATE_SIP,
    DownloadIcon,
    DOWNLOAD_REPORT,
    POWERED_BY,
  };

  const { SECTION_ROLE } = ADVISARY_LANDING_PAGE;

  if (!stocksCard.length) return null;

  return (
    <>
      <section>
        <AdvisarySectionHeading
          subHeading={advisaryHeading.header}
          isDescription
          description={advisaryHeading.subheader}
          isViewAll={false}
          id={SECTION_ROLE.STOCKS_SIP}
          onClick={() => {
            sendCTAClickEvent(
              CATEGORY.STOCK_SIP_VIEW_ALL,
              USER_ACTION.VIEW_ALL_STOCK_SIP,
            );
          }}
        />
        {stocksCard.length > 0 ? (
          <div>
            <div className={styles.carousel}>
              {stocksCard.map((item, index) => (
                <StocksSIPCard
                  stocks={stocksProps}
                  item={item}
                  key={item.Pml_ID + index}
                  {...props}
                />
              ))}
            </div>
          </div>
        ) : null}
      </section>
      <hr className={styles.horizontal} />
    </>
  );
};

export default StocksSIP;

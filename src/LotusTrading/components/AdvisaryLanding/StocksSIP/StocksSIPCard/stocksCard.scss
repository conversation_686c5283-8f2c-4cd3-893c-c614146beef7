@import '../../advisaryCommon.scss';

.stocksCard {
    @include Card;
    background-image: url('../../../../../assets/icons/advisary/pattern.png');
    background-size: cover;
    &__pattern {
        padding: 16px 18px;
        background-position: right;
    }

    &__header {
        display: flex;
        justify-content: space-between;
        font-size: 14px;

        .heading {
          @include typography(text1B, map-get($colors, primaryTextColor));
        }

        svg {
            width: 100%;
            height: auto;
        }

        img {
          margin-top: 4px;
          width: 69.8px;
          height: 40px;
        }
    }

    &__body {
        span {
            @include typography(text1R, map-get($colors, secondaryTextColor));
        }

        .amount {
            font-weight: 600;
            color: map-get($colors, primaryTextColor);
        }

        .vector {
            display: flex;
            flex-direction: column;
            font-size: 14px;
            padding-top: 5px;

            span {
              @include typography(text1RB);
            }
            img {
                width: 94px;
            }
        }
    }

    &__footer {
        display: flex;
        align-items: center;
        background-color: #f1fafe;
        border-radius: 10px;
        padding: 15px;
        .downloadIcon {
            width: 24px;
        }

        .downloadReport {
          display: flex;
          align-items: center;
        }

        p {
            @include typography(text22B1, map-get($colors, DBlue1));
            line-height: 1.2;
            padding: 0 21px 0 6px;
        }
    }

    .button {
        @include typography(text1RB, map-get($colors, PureWhite));
        height: 40px;
    }
}

.lotusFunds {
    @include typography(text2, map-get($colors, Grey10));
    padding-top: 10px;
}

import React, { useEffect, useState } from 'react';

import Card from '../../../../../components/Card/Card';
import Button from '../../../../../components/Button/Button';
import Shimmer from '../../../../../components/Shimmer/Shimmer';

import { getPreviousClose } from '../../../../query/lotusTradingQuery';

import { sendCTAClickEvent } from '../../../../analyticsEvents/masterLandingEventUtils';

import {
  convertedDateFormat,
  getCurrentDate,
  numDifferentiation,
} from '../../utils';
import {
  openDeepLinkPaytmMoney,
  openInBrowser,
} from '../../../../../utils/bridgeUtils';
import { log } from '../../../../../utils/commonUtils';

import {
  CREATE_SIP_CTA,
  STOCK_SIP_LOGO,
} from '../../../../pages/AdvisaryLandingPage/enums';
import {
  CATEGORY,
  USER_ACTION,
} from '../../../../analyticsEvents/masterLandingEnums';

import styles from './stocksCard.scss';

const StocksSIPCard = ({ stocks, item, ...props }) => {
  const {
    VectorStrokeRed,
    buttonText,
    DownloadIcon,
    DOWNLOAD_REPORT,
    POWERED_BY,
  } = stocks;
  const {
    Report_Deeplink,
    Body_1,
    Body_2,
    Body_3,
    Investment_Amount,
    Powered_By,
    Script_Name,
    Pml_ID,
  } = item;

  const [previousCloseValues, setPreviousCloseValues] = useState([]);
  const [currentCloseValue, setCurrentCloseValue] = useState(null);

  const InvestedAmountPerMonth = `Rs.${Investment_Amount}/pm`;

  const fetchPreviousClose = async () => {
    try {
      const paramsOne = [
        {
          date: convertedDateFormat(12),
          pmlId: Pml_ID,
        },
        {
          date: convertedDateFormat(11),
          pmlId: Pml_ID,
        },
        {
          date: convertedDateFormat(10),
          pmlId: Pml_ID,
        },
        {
          date: convertedDateFormat(9),
          pmlId: Pml_ID,
        },
        {
          date: convertedDateFormat(8),
          pmlId: Pml_ID,
        },
        {
          date: convertedDateFormat(7),
          pmlId: Pml_ID,
        },
      ];
      const paramsTwo = [
        {
          date: convertedDateFormat(6),
          pmlId: Pml_ID,
        },
        {
          date: convertedDateFormat(5),
          pmlId: Pml_ID,
        },
        {
          date: convertedDateFormat(4),
          pmlId: Pml_ID,
        },
        {
          date: convertedDateFormat(3),
          pmlId: Pml_ID,
        },
        {
          date: convertedDateFormat(2),
          pmlId: Pml_ID,
        },
        {
          date: convertedDateFormat(1),
          pmlId: Pml_ID,
        },
      ];
      const paramsThree = [
        {
          date: getCurrentDate(),
          pmlId: Pml_ID,
        },
      ];
      const [res, res2, res3] = await Promise.all([
        getPreviousClose(paramsOne, props.axiosSource),
        getPreviousClose(paramsTwo, props.axiosSource),
        getPreviousClose(paramsThree, props.axiosSource),
      ]);
      const priceOne = res.data.results.map(val => val.p_close);
      const priceTwo = res2.data.results.map(val => val.p_close);
      const priceThree = res3.data.results.map(val => val.p_close);
      setPreviousCloseValues([...priceOne, ...priceTwo]);
      setCurrentCloseValue(...priceThree);
    } catch (err) {
      log(err);
    }
  };

  useEffect(() => {
    fetchPreviousClose();
  }, []);

  const calculateClose = () =>
    previousCloseValues
      .map(val => Investment_Amount / val)
      .reduce((accumulator, currentValue) => accumulator + currentValue, 0);

  const calculateReturns = numDifferentiation(
    calculateClose() * currentCloseValue,
  );

  const openReport = () => {
    sendCTAClickEvent(
      CATEGORY.DOWNLOAD_SIP_REPORT,
      USER_ACTION.STOCK_SIP_DOWNLOAD_REPORT,
    );
    openInBrowser(Report_Deeplink);
  };

  const openStocks = () => {
    sendCTAClickEvent(CATEGORY.CREATE_SIP, USER_ACTION.STOCK_SIP_CREATE_SIP);
    openDeepLinkPaytmMoney(CREATE_SIP_CTA(Pml_ID));
  };

  return (
    <div>
      <Card customClass={styles.stocksCard}>
        <div className={styles.stocksCard__pattern}>
          <div className={styles.stocksCard__header}>
            <div className={styles.heading}>{Script_Name}</div>
            <img src={STOCK_SIP_LOGO} alt="stocks-sip-icon" />
          </div>
          <div className={styles.stocksCard__body}>
            <span>{Body_1}</span>
            <span className={styles.amount}>
              &nbsp;{InvestedAmountPerMonth}
            </span>
            <br />
            <span>{Body_2}</span>
            <div className={styles.vector}>
              {!calculateReturns ? (
                <Shimmer height="17px" width="94px" />
              ) : (
                <span>
                  {calculateReturns}&nbsp;{Body_3}
                </span>
              )}
              <img src={VectorStrokeRed} alt="vector-stroke-red" />
            </div>
          </div>
        </div>
        <div className={styles.stocksCard__footer}>
          <div className={styles.downloadReport} onClick={openReport}>
            <img
              className={styles.downloadIcon}
              src={DownloadIcon}
              alt="download-icon"
            />
            <p>{DOWNLOAD_REPORT}</p>
          </div>
          <Button
            buttonText={buttonText}
            isPrimary
            className={styles.button}
            onClickHandler={openStocks}
          />
        </div>
      </Card>
      <p className={styles.lotusFunds}>
        {POWERED_BY}&nbsp;{Powered_By}
      </p>
    </div>
  );
};

export default StocksSIPCard;

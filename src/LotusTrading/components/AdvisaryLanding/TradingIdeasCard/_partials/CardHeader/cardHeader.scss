@mixin flex {
  display: flex;
  align-items: center;
}

.header {
  .nameWrapper {
    @include flex;

    .titleContainer {
      display: flex;
      flex-direction: column;
      width: 100%;
    }

    .icon {
      flex-grow: 0;
      padding: 1px;
      margin-right: 5px;
      border: none;

      img {
        border-radius: 100px;
      }

      .fallbackStyle {
        margin-right: 0;
        border: none;
        background-color: map-get($colors, Gray3);
        border-radius: 100px;
      }
    }

    .nameSection {
      @include flex;

      .name {
        @include typography(text1RB, map-get($colors, primaryTextColor));
      }

      .buySellIcon {
        margin-left: 3px;
        width: 14px;
      }
    }

    .secondary {
      @include flex;
      justify-content: space-between;

      .poweredBy {
        @include flex;

        .poweredByText {
          @include typography(text3R, map-get($colors, secondaryTextColor));

          span {
            @include typography(text3B, map-get($colors, secondaryTextColor));
          }
        }

        .infoIcon {
          width: 12px;
          margin-left: 4px;
        }
      }
    }
  }

  .definationInfoIcon {
    width: 12px;
    margin-left: 4px;
  }

  .tagsWrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 6.2px;
    width: 100%;
    margin-top: 11px;
    padding-bottom: 14px;
    border-bottom: 1px dashed map-get($colors, Gray1);

    .chipCustomClass {
      white-space: nowrap;
      border-radius: 115.7px;
      border: none;
      padding: 4.5px 12px;
      background-color: map-get($colors, Gray3);

      @include typography(text22B, map-get($colors, secondaryTextColor));
    }
  }
}

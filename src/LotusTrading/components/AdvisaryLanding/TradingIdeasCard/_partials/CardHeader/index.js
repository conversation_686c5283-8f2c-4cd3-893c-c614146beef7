import React from 'react';
import { POWERED_BY } from '../../../../../config/festiveTradingConfig';

import Icon from '../../../../../../components/Icon';
import BuyIcon from '../../../../../../assets/icons/buy_icon.svg';
import SellIcon from '../../../../../../assets/icons/sell_icon.svg';
import InfoIconGrey from '../../../../../../assets/icons/info_icon_grey.svg';

import Chip from '../../../../../../components/Chip/Chip';
import DateTime from '../../../../DateTime';

import { calculateDateTime } from '../../../../../utils/utilFunctions';

import styles from './cardHeader.scss';

const CardHeader = ({
  renderTags,
  pmlId,
  name,
  sebi_details,
  provider_name,
  date_time,
  isBuy,
  onMarginOpen,
}) => {
  const showIcon = isBuy ? BuyIcon : SellIcon;

  const onChipClick = val => {
    if ('onClick' in val) {
      val.onClick();
    }
  };

  return (
    <div className={styles.header}>
      <div className={styles.nameWrapper}>
        <Icon
          name={pmlId?.toString()}
          companyName={name}
          className={styles.icon}
          fallbackStyle={styles.fallbackStyle}
        />
        <div className={styles.titleContainer}>
          <div className={styles.nameSection}>
            <span className={styles.name}>{name}</span>
            <img alt="icon" src={showIcon} className={styles.buySellIcon} />
          </div>
          <div className={styles.secondary}>
            <div className={styles.poweredBy}>
              <div className={styles.poweredByText}>
                {POWERED_BY} <span>{provider_name}</span>
              </div>
              <img
                className={styles.infoIcon}
                src={InfoIconGrey}
                alt="info-icon"
                onClick={() => onMarginOpen(sebi_details?.join(' '))}
              />
            </div>
            {date_time && <DateTime dateTime={calculateDateTime(date_time)} />}
          </div>
        </div>
      </div>
      <div className={styles.tagsWrapper}>
        {renderTags().map(val => (
          <Chip
            key={val.id}
            customClass={styles.chipCustomClass}
            onClick={() => onChipClick(val)}
          >
            <>
              {val.tag}
              {val.icon ? (
                <img
                  className={styles.definationInfoIcon}
                  src={InfoIconGrey}
                  alt="info-icon"
                />
              ) : null}
            </>
          </Chip>
        ))}
      </div>
    </div>
  );
};

export default CardHeader;

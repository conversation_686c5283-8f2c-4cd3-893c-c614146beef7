import classNames from 'classnames';
import React from 'react';
import { useStockFeed } from '../../../../../../hooks/equities';
import { roundValue } from '../../../../../../utils/commonUtils';
import Shimmer from '../../../../../../components/Shimmer/Shimmer';

import styles from './etfPriceBroadcast.scss';

const EtfPriceBroadcast = ({ securityId, segment }) => {
  const { ltp, pClose, percentageChange } = useStockFeed({
    exchange: 'NSE',
    securityId,
    segment,
  });

  const renderGetChange = () => {
    const percentChange = roundValue(Math.abs(percentageChange));
    const priceChange = roundValue(ltp - pClose);
    const priceChangeStyle = priceChange > 0 ? styles.profit : styles.loss;
    return (
      <span
        className={classNames(priceChangeStyle, styles.change)}
      >{` ${priceChange}(${percentChange}%)`}</span>
    );
  };

  return (
    <>
      {ltp === undefined ? (
        <Shimmer height="15px" width="50px" />
      ) : (
        <>
          <span>{roundValue(ltp)}</span>
          {renderGetChange()}
        </>
      )}
    </>
  );
};

export default EtfPriceBroadcast;

import React from 'react';
import { ALL } from '../../../../../config/lotusTradingConfig';
import LabelValueWrapper from '../LabelValueWrapper';

import styles from './cardBody.scss';

const CardBody = ({ renderCardFields, activeTab }) => (
  <div className={styles.cardBody}>
    {renderCardFields().map(item => {
      if (item.belongsTo === activeTab)
        return (
          <LabelValueWrapper
            key={item.id}
            labelContent={item.label}
            valueContent={item.value}
          />
        );
      else if (item.belongsTo === ALL)
        return (
          <LabelValueWrapper
            key={item.id}
            labelContent={item.label}
            valueContent={item.value}
          />
        );
      return null;
    })}
  </div>
);

export default CardBody;

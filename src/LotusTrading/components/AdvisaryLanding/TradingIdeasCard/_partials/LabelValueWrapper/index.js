import React from 'react';
import cx from 'classnames';

import styles from './labelValueWrapper.scss';

const LabelValueWrapper = ({
  valueCustomClass,
  labelContent,
  valueContent,
}) => (
  <div className={styles.labelValueWrapper}>
    <div className={styles.label}>{labelContent}</div>
    <div className={cx(styles.value, valueCustomClass)}>{valueContent}</div>
  </div>
);

export default LabelValueWrapper;

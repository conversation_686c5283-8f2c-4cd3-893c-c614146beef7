import React from 'react';
import classNames from 'classnames';

import Chip from '../../../../../../components/Chip/Chip';
import LabelValueWrapper from '../LabelValueWrapper';

import styles from './cardFooter.scss';

const CardFooter = ({ children, CARD_FIELDS, status, Status }) => {
  const getStatus = () => {
    const open = status === Status[0];
    const close = status !== Status[0];
    let chipText = '';
    if (open) {
      chipText = 'open';
    } else if (close) {
      chipText = 'closed';
    }
    let chipClass = '';
    if (open) {
      chipClass = styles.statusChipClass__open;
    } else if (close) {
      chipClass = styles.statusChipClass__close;
    }

    return (
      <Chip customClass={classNames(styles.statusChipClass, chipClass)}>
        {chipText}
      </Chip>
    );
  };

  return (
    <div className={styles.footer}>
      <LabelValueWrapper
        labelContent={CARD_FIELDS.STATUS}
        valueCustomClass={styles.footer__status}
        valueContent={getStatus()}
      />
      {children}
    </div>
  );
};

export default CardFooter;

.footer {
  padding-top: 14.5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  &__status {
    padding: 1px 4px 1px 2px;
    border-radius: 20px;
    background-color: map-get($colors, PureWhite);
    box-shadow: 0 1px 1px 0 map-get($colors, Gray1);

    .statusChipClass {
      height: 15px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 10px;
      border: none;
      @include typography(text3R);

      &__open {
        background-color: rgba(55, 189, 109, 0.1);
        color: map-get($colors, Green);
      }

      &__close {
        background-color: rgba(255, 157, 0, 0.1);
        color: map-get($colors, Orange);
      }
    }
  }
}
import React from 'react';
import classNames from 'classnames';

import {
  generateQueryParamsString,
  isIosBuild,
  isPaytmMoney,
} from '../../../../utils/commonUtils';
import {
  ALL,
  CARD_FIELDS,
  FiltersCategories,
  LOTUS_TRADING_LISTING_PAGE_TABS,
} from '../../../config/lotusTradingConfig';
import {
  openDeepLink,
  openDeepLinkPaytmMoney,
  openInBrowser,
  paytmResetStatusBarBackgroundColor,
} from '../../../../utils/bridgeUtils';
import {
  PAYTM_ORDER_URL,
  PML_ORDER_URL_DERIVATIVES,
  PML_ORDER_URL_EQUITY,
} from '../../../config/urlConfig';
import { sendBuySellClickEvents } from '../../../analyticsEvents/tradingIdeasEventUtils';
import {
  EVENT_CATEGORY,
  SCREEN_NAME_RA_LANDING,
} from '../../../analyticsEvents/tradingIdeasEventEnum';
import { INSTRUMENT_TYPE, SEGMENT_TYPES } from '../../../../utils/enum';
import {
  getIntradayBtnText,
  getBracketBtnText,
  FNO_ONBOARDING,
  DOWNLOAD_PDF_LOGO,
} from './enums';
import { CALL_TYPE } from '../../../config/expertPage';
import {
  CardHeader,
  CardBody,
  CardFooter,
  LabelValueWrapper,
  EtfPriceBroadcast,
} from './_partials';

import Shimmer from '../../../../components/Shimmer/Shimmer';
import useMargin from '../../../hooks/useMargin';

import styles from './TradingIdeasCard.scss';

function TradingIdeasCard({
  data,
  activeTab,
  isMarginLoading = true,
  onMarginOpen,
  customClass,
  isUserFNOReady,
  isFNOLoading,
  eventCategory = EVENT_CATEGORY.TRADECALLS_HOME,
  eventScreen = SCREEN_NAME_RA_LANDING,
  advisorDefination,
  showAdvisorDefination = () => {},
}) {
  const {
    script_name: name,
    callexpiry,
    entryprice1: entry,
    stoplossprice1: stopLoss,
    category: tag1,
    instrument,
    script_id: securityId,
    remarks: tag2,
    targetprice1: targetPrice,
    subcategory: rationale,
    callsenttime: date_time,
    callstatus: status,
    channel,
    calltype,
    pml_id: pmlId,
    exit_condition: exitCondition,
    last_UpdateTime: lastUpdateTime,
    future_option: futureOption,
    margin,
    sebi_details,
    provider_name,
    lot_size,
    advisor,
    advisorid,
    internalremarks,
  } = data;

  const isBuy = calltype?.toLowerCase() === CALL_TYPE.BUY;
  const { Tag1, Status } = FiltersCategories;
  const {
    TARGET_PRICE,
    ENTRY_PRICE,
    CURRENT_PRICE,
    STOP_LOSS,
    LAST_UPDATE_TIME,
    MARGIN_REQUIRED,
    SHORT_TERM,
    EXIT_AT,
    DOWNLOAD_REPORT,
  } = CARD_FIELDS;
  // SEGMENT
  const { DERIVATIVES, CASH } = SEGMENT_TYPES;

  const segment =
    activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0] ? DERIVATIVES : CASH;

  const isDerivative = activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0];
  // TAGS
  const renderTags = () => {
    const renderTag1 = tag1 && tag1 === Tag1[1] ? SHORT_TERM : tag1;
    const renderTag2 = () => {
      if (activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]) {
        return `IdeaExpiry: ${callexpiry.substr(
          0,
          callexpiry.lastIndexOf(' '),
        )}`;
      }
      if (callexpiry) {
        return `IdeaExpiry: ${callexpiry.substr(
          0,
          callexpiry.lastIndexOf(' '),
        )}`;
      }

      if (tag2) {
        return tag2;
      }
    };
    const renderTag3 =
      rationale !== undefined && rationale.length !== 0 && rationale;
    return [
      {
        id: 1,
        tag: renderTag1,
        icon: false,
      },
      {
        id: 2,
        tag: renderTag2(),
        icon: false,
      },
      {
        id: 3,
        tag: renderTag3,
        onClick: showAdvisorDefination,
        icon: !!advisorDefination,
      },
    ].filter(val => val.tag !== undefined && val.tag !== false);
  };

  // BUTTONS
  const navigateToOrderScreen = async orderType => {
    sendBuySellClickEvents(
      eventCategory,
      activeTab,
      futureOption,
      tag1,
      orderType,
      isBuy,
      eventScreen,
    );
    const DELIVERY_PRODUCT_TYPE = 'C';
    const BRACKET_PRODUCT_TYPE = 'B';
    const MARKET_ORDER_TYPE = 'MKT';
    const INTRADAY_PRODUCT_TYPE = 'I';
    const LIMIT = 'LMT';
    const M = 'M';

    const commonParams = {
      productType:
        orderType === 'bracket'
          ? BRACKET_PRODUCT_TYPE
          : tag1 === Tag1[0]
          ? INTRADAY_PRODUCT_TYPE
          : activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]
          ? M
          : DELIVERY_PRODUCT_TYPE,
      orderType:
        instrument?.toUpperCase() === INSTRUMENT_TYPE.FUTSTK ||
        instrument?.toUpperCase() === INSTRUMENT_TYPE.OPTSTK
          ? LIMIT
          : MARKET_ORDER_TYPE,
    };

    const params = {
      action: 'place-order',
      txn_type: isBuy ? 'B' : 'S',
      price:
        instrument?.toUpperCase() === INSTRUMENT_TYPE.FUTSTK ||
        instrument?.toUpperCase() === INSTRUMENT_TYPE.OPTSTK
          ? entry
          : 0.0,
      product: commonParams.productType,
      order_type: commonParams.orderType,
      channel,
    };

    if (isIosBuild()) {
      paytmResetStatusBarBackgroundColor();
    }

    if (isPaytmMoney()) {
      const queryString = generateQueryParamsString(params);
      let deepLinkUrl = '';
      if (activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]) {
        deepLinkUrl = `${PML_ORDER_URL_DERIVATIVES}/${
          instrument?.toLowerCase() === 'eq' ? 'es' : instrument?.toLowerCase()
        }/${pmlId}${queryString}`;
      } else deepLinkUrl = `${PML_ORDER_URL_EQUITY}/${pmlId}${queryString}`;
      openDeepLinkPaytmMoney(deepLinkUrl);
      return;
    }

    if (isDerivative && !isUserFNOReady) {
      openDeepLink(FNO_ONBOARDING);
      return;
    }

    const { isin } = data;
    const tickSize = data.scripMaster.tick_size;
    const lotSize = data.scripMaster.lot_Size;
    let paytmParams = {
      productType:
        tag1 === Tag1[0]
          ? INTRADAY_PRODUCT_TYPE
          : activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]
          ? M
          : DELIVERY_PRODUCT_TYPE,
      orderType: commonParams.orderType,
      id: pmlId,
      securityId,
      exchange: 'NSE',
      name,
      segment,
      tickSize,
      isin,
      instrumentType:
        instrument.toLowerCase() === 'eq' ? 'ES' : instrument?.toUpperCase(),
      lotSize,
      channel,
    };
    if (orderType === 'bracket') {
      paytmParams = {
        ...paytmParams,
        activeOrderType: BRACKET_PRODUCT_TYPE,
      };
    }
    const queryString = generateQueryParamsString(paytmParams);
    let deepLinkUrl = '';
    if (activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]) {
      deepLinkUrl = `${PAYTM_ORDER_URL(isBuy)}${queryString}`;
    } else deepLinkUrl = `${PAYTM_ORDER_URL(isBuy, pmlId)}${queryString}`;
    openDeepLink(deepLinkUrl);
  };

  const isBuyBracketButtonVisible = () =>
    activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[1] &&
    tag1 === Tag1[0] &&
    status === Status[0];

  const renderButton = (buttonText, navigation, customButtonClass) => (
    <button
      onClick={() => navigateToOrderScreen(navigation)}
      className={classNames(styles.button, customButtonClass)}
    >
      {buttonText}
    </button>
  );

  const buySellButton = () => {
    if (isFNOLoading)
      return (
        <div
          className={classNames(
            styles.btnWrapperLoading,
            styles.shimmerAnimation,
          )}
        />
      );
    if (status !== Status[0]) return;
    const buttonText = isBuy ? 'Buy' : 'Sell';
    const buttonStyle = isBuy ? styles.button__buy : styles.button__sell;
    return (
      <div className={styles.btnWrapper}>
        {renderButton(buttonText, '', buttonStyle)}
      </div>
    );
  };

  const bracketOrderButton = () => {
    const buttonText = getBracketBtnText(isBuy);
    const buttonStyle = isBuy
      ? styles.button__bracket
      : styles.button__intradaySell;
    return renderButton(buttonText, 'bracket', buttonStyle);
  };

  const intradayButton = () => {
    if (isFNOLoading)
      return (
        <div
          className={classNames(
            styles.intradayBuyLoading,
            styles.shimmerAnimation,
          )}
        />
      );
    const buttonText = getIntradayBtnText(isBuy);
    const buttonStyle = isBuy
      ? styles.button__intradayBuy
      : styles.button__intradaySell;
    return renderButton(buttonText, Tag1[0], buttonStyle);
  };

  // MARGIN
  const { onMargin } = useMargin({
    activeTab,
    securityId,
    segment,
    futureOption,
    lot_size,
    isBuy,
    margin,
    pmlId,
    priceWhenCreated: entry,
    calltype,
  });

  const EtfProps = {
    securityId,
    segment,
  };

  const downloadPDF = () => {
    if (!internalremarks[0]?.message?.length) return;

    const link = internalremarks[0]?.message?.split(/<p>(.*?)<\/p>/g);

    openInBrowser(link?.[1]);
  };

  // RENDER CARD FIELDS
  const renderCardFields = () => [
    {
      id: 1,
      belongsTo: ALL,
      value: targetPrice,
      label: TARGET_PRICE,
    },
    {
      id: 2,
      value: entry,
      belongsTo: ALL,
      label: ENTRY_PRICE,
    },
    {
      id: 3,
      belongsTo: ALL,
      label: CURRENT_PRICE,
      value: <EtfPriceBroadcast {...EtfProps} />,
    },
    {
      id: 4,
      label: STOP_LOSS,
      belongsTo: ALL,
      value: stopLoss,
    },
    {
      id: 5,
      belongsTo: ALL,
      label: LAST_UPDATE_TIME,
      value: lastUpdateTime
        ? lastUpdateTime?.slice(0, lastUpdateTime?.length - 3)
        : '-- -- --',
    },
    {
      id: 6,
      label: MARGIN_REQUIRED,
      value: isMarginLoading ? (
        <Shimmer height="17px" width="50px" />
      ) : (
        <>{+onMargin() || '-'}</>
      ),
      belongsTo: LOTUS_TRADING_LISTING_PAGE_TABS[0],
    },
    {
      id: 7,
      label: DOWNLOAD_REPORT,
      value: (
        <div className={styles.download}>
          {internalremarks?.length &&
          internalremarks[0]?.message.includes('http') ? (
            <img
              className={styles.downloadIcon}
              src={DOWNLOAD_PDF_LOGO}
              alt="info-icon"
              onClick={downloadPDF}
            />
          ) : (
            '---'
          )}
        </div>
      ),
      belongsTo: LOTUS_TRADING_LISTING_PAGE_TABS[1],
    },
  ];

  // RENDER EXIT STATUS
  const renderExitStatus = () => (
    <>
      {exitCondition && status !== Status[0] ? (
        <LabelValueWrapper
          labelContent={EXIT_AT}
          valueCustomClass={styles.exitStatus}
          valueContent={exitCondition}
        />
      ) : null}
    </>
  );

  // PROPS
  const headerProps = {
    renderTags,
    pmlId,
    name,
    sebi_details,
    provider_name,
    isBuy,
    date_time,
    onMarginOpen,
    advisor,
    advisorid,
  };

  const bodyProps = {
    renderCardFields,
    activeTab,
  };

  const footerProps = {
    CARD_FIELDS,
    status,
    Status,
    exitCondition,
  };

  return (
    <li className={classNames(styles.cardList, customClass)}>
      {/* top section  */}
      <CardHeader {...headerProps} />

      {/* body section  */}
      <CardBody {...bodyProps} />

      {/* footer section */}
      <CardFooter {...footerProps}>
        {activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0] && (
          <div
            className={classNames({
              [styles.btnWrapper]: tag1 === Tag1[0],
            })}
          >
            {renderExitStatus()}
            {tag1 === Tag1[0] && status === Status[0] ? intradayButton() : null}
            {buySellButton()}
          </div>
        )}
        {activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[1] && (
          <>
            {renderExitStatus()}
            {isBuyBracketButtonVisible() && bracketOrderButton()}
            {buySellButton()}
          </>
        )}
      </CardFooter>
    </li>
  );
}

export default TradingIdeasCard;

.cardList {
  flex-grow: 0;
  margin-top: 18px;
  padding: 15px;
  border: solid 1px map-get($colors, Gray1);
  background-color: map-get($colors, PureWhite);
  border-radius: 10px;

  .button {
    height: 30px;
    flex-grow: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 22px;
    border: none;
    border-radius: 110.6px;
    color: map-get($colors, PureWhite);

    &__bracket {
      background-color: map-get($colors, PureWhite) !important;
      border: 2px solid map-get($colors, Green);
      color: map-get($colors, Green);
      padding: 0 15px;
      margin-right: 12px;
    }

    &__sell {
      background-color: map-get($colors, Red);
    }

    &__buy {
      background-color: map-get($colors, Green);
    }

    &__intradayBuy {
      background-color: map-get($colors, PureWhite);
      color: map-get($colors, Green);
      border: solid 1px map-get($colors, Green);
      margin-right: 12px;
    }

    &__intradaySell {
      background-color: map-get($colors, PureWhite);
      color: map-get($colors, Red);
      border: solid 1px map-get($colors, Red);
      margin-right: 12px;
    }
  }

  .exitStatus {
    color: map-get($colors, tertiaryTextColor) !important;
  }

  .btnWrapper {
    display: flex;
  }

  .btnWrapperLoading {
    width: 60px;
    height: 31px;
    border-radius: 110.6px;
  }

  .intradayBuyLoading {
    width: 120px;
    height: 31px;
    margin-right: 12px;
    border-radius: 110.6px;
  }

  .download {
    padding-top: 2px;

    .downloadIcon {
      width: 15px;
      height: 15px;
      cursor: pointer;
      margin-left: 2px;
    }
  }
}

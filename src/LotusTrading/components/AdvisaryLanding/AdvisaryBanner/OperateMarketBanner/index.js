import React from 'react';

import MarketCard from './MarketCard';

import { sortBasedOnSlotID } from '../../utils';
import { AdvisarySectionHeading } from '../../common';

import { ADVISARY_LANDING_PAGE } from '../../enums';

import styles from './operateMarket.scss';

const OperateMarketBanner = ({ marketBanner }) => {
  const { SECTION_ROLE, OPERATE_MARKET_HEADING } = ADVISARY_LANDING_PAGE;
  return (
    <section
      className={styles.marketContainer}
      aria-labelledby={SECTION_ROLE.MARKET}
    >
      <AdvisarySectionHeading
        subHeading={OPERATE_MARKET_HEADING}
        id={SECTION_ROLE.MARKET}
      />
      <div className={styles.carousel}>
        {sortBasedOnSlotID(marketBanner).map((item, index) => (
          <MarketCard item={item} key={item.id + index} />
        ))}
      </div>
    </section>
  );
};

export default OperateMarketBanner;

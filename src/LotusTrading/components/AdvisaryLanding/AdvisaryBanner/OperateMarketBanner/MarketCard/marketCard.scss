@import '../../../advisaryCommon.scss';

.marketCard {
    @include Card;
    height: 214px;
    display: flex;
    min-width: 250px;
    flex-direction: column;
    justify-content: space-between;

    &__header {
      position: relative;

        img {
            width: 100%;
            border-top-right-radius: 10px;
            border-top-left-radius: 10px;
        }

        .tag {
          bottom: -8px;
          padding: 5px 10px;
          margin-left: 15px;
          position: absolute;
          border-radius: 16px;
          background-color: map-get($colors, primaryBgColor);
          @include typography(text22B1, map-get($colors, DBlue2));
      }

    }

    &__body {
      display: flex;
      align-items: center;
      padding: 20px 16px 25px 16px;

        p {
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          text-overflow: ellipsis;
          -webkit-box-orient: vertical;
          @include typography(heading2B1, map-get($colors, primaryTextColor));
        }
    }

}

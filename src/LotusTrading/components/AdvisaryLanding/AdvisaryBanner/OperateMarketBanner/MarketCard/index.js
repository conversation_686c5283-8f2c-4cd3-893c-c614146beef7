import React from 'react';

import Card from '../../../../../../components/Card/Card';

import { handleAdvisoryDeepLink } from '../../../utils';

import {
  CATEGORY,
  USER_ACTION,
} from '../../../../../analyticsEvents/masterLandingEnums';
import { sendCTAClickEvent } from '../../../../../analyticsEvents/masterLandingEventUtils';

import styles from './marketCard.scss';

const MarketCard = ({ item }) => {
  const { deeplink, header, tag, banner_link } = item;

  return (
    <div
      onClick={() => {
        sendCTAClickEvent(CATEGORY.BLOG, `${USER_ACTION.BLOG_CLICK}_${header}`);
        handleAdvisoryDeepLink(deeplink);
      }}
    >
      <Card customClass={styles.marketCard}>
        <div className={styles.marketCard__header}>
          <img src={banner_link} alt="market-banner" />
          <div className={styles.tag}>{tag}</div>
        </div>
        <div className={styles.marketCard__body}>
          <p>{header}</p>
        </div>
      </Card>
    </div>
  );
};

export default MarketCard;

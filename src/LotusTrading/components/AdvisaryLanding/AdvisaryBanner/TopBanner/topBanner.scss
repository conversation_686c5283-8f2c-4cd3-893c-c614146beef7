.topBanner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 23px 16px;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  background-color: map-get($colors, LYellow);
  .bannerTextContainer {
    display: flex;
    align-items: center;

    .freeTag {
      @include typography(text2B, map-get($colors, PureWhite));
      border-radius: 10px;
      text-transform: uppercase;
      padding: 0 6px;
      background-color: map-get($colors, Orange);
    }

    .bannerText {
      padding-left: 6px;
      @include typography(textB2, map-get($colors, Grey5));
      line-height: 1;

      .highlightText {
        color: map-get($colors, Grey5);
        font-weight: bold;
      }
    }
  }

  img[alt="animating-arrows"] {
    width: 21px;
  }
}

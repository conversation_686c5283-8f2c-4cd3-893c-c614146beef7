import React from 'react';
import { handleAdvisoryDeepLink, modifyText } from '../../utils';
import styles from './topBanner.scss';
import { sendCTAClickEvent } from '../../../../analyticsEvents/masterLandingEventUtils';
import {
  CATEGORY,
  USER_ACTION,
} from '../../../../analyticsEvents/masterLandingEnums';

const TopBanner = ({ banner }) => {
  if (!Object.keys(banner)) return null;
  const {
    deeplink,
    icon_link: AnimatingArrow,
    tag: TAG,
    text_1: brokerageText,
  } = banner;

  const renderBrokerageText = () => (
    <p className={styles.bannerText}>
      {modifyText(brokerageText).map((value, index) => (
        <React.Fragment key={index}>
          {value.isBold === 'true' ? (
            <span className={styles.highlightText}>{value.text}</span>
          ) : (
            <span>&nbsp;{value.text}&nbsp;</span>
          )}
        </React.Fragment>
      ))}
    </p>
  );

  return (
    <section
      className={styles.topBanner}
      onClick={() => {
        sendCTAClickEvent(CATEGORY.CLICKS_SLEEK, USER_ACTION.SLEEK_CLICK);
        handleAdvisoryDeepLink(deeplink);
      }}
    >
      <div className={styles.bannerTextContainer}>
        <div className={styles.freeTag}>{TAG}</div>
        {renderBrokerageText()}
      </div>
      <img src={AnimatingArrow} alt="animating-arrows" />
    </section>
  );
};

export default TopBanner;

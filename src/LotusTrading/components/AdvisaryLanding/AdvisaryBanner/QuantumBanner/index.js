import React from 'react';

import history from '../../../../../history';

import { sendCTAClickEvent } from '../../../../analyticsEvents/masterLandingEventUtils';

import { navigateTo } from '../../../../../services/coreUtil';

import { handleAdvisoryDeepLink } from '../../utils';
import URL from '../../../../../routes/config/urlConfig';
import { LOTUS_TRADING_ROUTES } from '../../../../config/routeConfig';

import {
  CATEGORY,
  USER_ACTION,
} from '../../../../analyticsEvents/masterLandingEnums';

import styles from './quantumBanner.scss';

const QuantumBanner = ({ quantumBanner }) => {
  const {
    banner,
    banner_deeplink: deeplink,
    video_text: bannerText,
    video_link,
    video_icon: playIcon,
  } = quantumBanner;

  const route = `${URL.LOTUS_TRADING}${LOTUS_TRADING_ROUTES.BANNER_VIDEO}`;

  const openVideo = e => {
    e.stopPropagation();
    const videoID = video_link
      .substr(video_link.lastIndexOf('?') + 1)
      .replace('v=', '');
    sendCTAClickEvent(CATEGORY.MASTER_BANNER, USER_ACTION.VIDEO_CLICK);
    navigateTo(history, route, { videoID }, 'push');
  };

  return (
    <section
      className={styles.bannerOne}
      onClick={() => {
        sendCTAClickEvent(
          CATEGORY.MASTER_BANNER,
          USER_ACTION.MASTER_BANNER_CLICK,
        );
        handleAdvisoryDeepLink(deeplink);
      }}
    >
      <div className={styles.bannerOne__header}>
        <img src={banner} alt="quantum-banner" />
      </div>
      <div className={styles.bannerOne__footer} onClick={openVideo}>
        <img src={playIcon} alt="play-icon" />
        <p>{bannerText}</p>
      </div>
    </section>
  );
};

export default QuantumBanner;

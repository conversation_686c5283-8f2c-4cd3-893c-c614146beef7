import React from 'react';

import Carousel from '../../../Carousel';

import {
  CATEGORY,
  USER_ACTION,
} from '../../../../analyticsEvents/masterLandingEnums';
import { sendCTAClickEvent } from '../../../../analyticsEvents/masterLandingEventUtils';

import { handleAdvisoryDeepLink, sortBasedOnSlotID } from '../../utils';

import styles from './imageBanner.scss';

const ImageBanner = ({ bannerTwo }) => {
  const renderItem = (item, index) => {
    const { id, banner, banner_deeplink } = item;

    return (
      <div
        className={styles.item}
        key={id}
        onClick={() => {
          sendCTAClickEvent(
            CATEGORY.BANNER,
            `${USER_ACTION.BANNER_CLICK}_${index}`,
          );
          handleAdvisoryDeepLink(banner_deeplink);
        }}
      >
        <img src={banner} alt="banner" />
      </div>
    );
  };

  return (
    <Carousel
      className={styles.bannerTwo}
      data={sortBasedOnSlotID(bannerTwo)}
      renderItem={renderItem}
    />
  );
};

export default ImageBanner;

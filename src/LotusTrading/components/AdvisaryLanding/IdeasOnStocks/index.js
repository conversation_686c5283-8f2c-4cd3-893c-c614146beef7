import React, { useState } from 'react';

import IdeasCard from './IdeasCard';
import StocksDrawer from './StocksDrawer';

import { AdvisarySectionHeading } from '../common';

import { sortBasedOnSlotID } from '../utils';
import { ADVISARY_LANDING_PAGE } from '../enums';

import styles from '../advisaryCommon.scss';

const IdeasOnStocks = ({ ideasStocks }) => {
  const { SECTION_ROLE, IDEAS_ON_STOCKS } = ADVISARY_LANDING_PAGE;
  const [isOpen, setIsOpen] = useState(false);
  const [drawer, setDrawer] = useState({});

  const onOpen = (header, cta, text, deeplink, type) => {
    setDrawer({ header, cta, text, deeplink, type });
    setIsOpen(true);
  };

  const onClose = () => {
    setIsOpen(false);
  };

  return (
    <section>
      <AdvisarySectionHeading
        subHeading={IDEAS_ON_STOCKS.HEADING}
        id={SECTION_ROLE.IDEAS}
      />

      <div className={styles.carousel}>
        {sortBasedOnSlotID(ideasStocks).map((item, index) => (
          <IdeasCard item={item} key={index} onOpen={onOpen} />
        ))}
      </div>
      <StocksDrawer isOpen={isOpen} drawer={drawer} onClose={onClose} />
    </section>
  );
};

export default IdeasOnStocks;

import React from 'react';
import Card from '../../../../../components/Card/Card';
import styles from './ideasCard.scss';
import Button from '../../common/Link/index';
import { handleAdvisoryDeepLink, modifyText } from '../../utils';
import { sendCTAClickEvent } from '../../../../analyticsEvents/masterLandingEventUtils';
import {
  CATEGORY,
  USER_ACTION,
} from '../../../../analyticsEvents/masterLandingEnums';

const IdeasCard = ({ item, onOpen }) => {
  const {
    header,
    icon,
    cta_text: buttonText,
    text1: textOne,
    text2: textTwo,
    text3: textThree,
    logo,
    knowmore_cta,
    knowmore_text,
    link_type,
    deeplink,
  } = item;

  const modifyTextIdeas = text =>
    modifyText(text).length ? (
      <li>
        {modifyText(text).map((value, index) => (
          <React.Fragment key={index}>
            {value.isBold !== 'true' ? (
              <span>{value.text}</span>
            ) : (
              <span className={styles.highlightText}>&nbsp;{value.text}</span>
            )}
          </React.Fragment>
        ))}
      </li>
    ) : null;

  return (
    <Card
      customClass={styles.ideasCard}
      onClick={() => {
        sendCTAClickEvent(
          CATEGORY.INVESTMENT_TRADE_OPTIONS,
          `${USER_ACTION.TRADING_OPTIONS_CLICK}_${header}`,
        );
        handleAdvisoryDeepLink(deeplink);
      }}
    >
      <div className={styles.ideasCard__header}>
        <img src={logo} alt="stocks-logo" />
        <img src={icon} alt="stocks-illustration" />
      </div>
      <div className={styles.ideasCard__body}>
        <p className={styles.heading}>{header}</p>
        <ul>
          {modifyTextIdeas(textOne)}
          {modifyTextIdeas(textTwo)}
          {modifyTextIdeas(textThree)}
        </ul>
      </div>
      <div className={styles.ideasCard__footer}>
        <Button
          linkText={buttonText}
          onClick={e => {
            e.stopPropagation();
            sendCTAClickEvent(
              CATEGORY.INVESTMENT_TRADE_OPTIONS,
              `${USER_ACTION.TRADING_OPTIONS_KNOW_MORE}_${header}`,
            );
            onOpen(header, knowmore_cta, knowmore_text, deeplink, link_type);
          }}
        />
      </div>
    </Card>
  );
};

export default IdeasCard;

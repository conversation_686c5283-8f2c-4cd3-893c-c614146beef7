@import '../../advisaryCommon.scss';

.ideasCard {
    @include Card;
    min-width: 250px;
    padding: 15px 15px 20px 15px;
    min-height: 293px;
    max-height: 293px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &__header {
        display: flex;
        position: relative;
        flex-direction: column;
        align-items: center;
        img {
            &[alt="stocks-logo"] {
              width: 68px;
              height: 14px;
              align-self: flex-start;
              object-fit: cover;
            }

            &[alt="stocks-illustration"] {
              width: 64px;
              height: 64px;
              margin-top: 16.3px;
              margin-bottom: 19.7px;
              object-fit: contain;
            }
        }
    }

    &__body {

        .heading {
            @include typography(text1B, map-get($colors, primaryTextColor));
            margin: 0px;
            width: calc(100% - 10px);
            height: 31px;
            display: flex;
            align-items: center;
        }

        ul li {
            display: flex;
            align-items: center;
            @include typography(text1R2, map-get($colors, Grey5));
            &:not(:last-child) {
                margin-bottom: 10px;
            }

            .highlightText {
                font-weight: 700;
                color: map-get($colors, primaryTextColor);
            }
        }

        ul li::before {
            content: url('../../../../../assets/icons/advisary/check.svg');
            height: 16px;
            margin-right: 4px;
        }
    }

    &__footer {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

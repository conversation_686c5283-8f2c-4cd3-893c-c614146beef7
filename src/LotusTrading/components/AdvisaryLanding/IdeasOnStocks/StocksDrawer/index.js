import React from 'react';
import Button from '../../../../../components/Button/Button';
import Drawer, { ALIGNMENTS } from '../../../../../components/Drawer';
import {
  openDeepLinkPaytmMoney,
  openInBrowser,
} from '../../../../../utils/bridgeUtils';
import { ADVISARY_LANDING_PAGE } from '../../enums';
import styles from './stocksDrawer.scss';

const StocksDrawer = ({ isOpen, onClose, drawer }) => {
  const { CTA_LINK_TYPE } = ADVISARY_LANDING_PAGE;

  const { header, cta, text, deeplink, type } = drawer;
  const openCTALink = () => {
    if (type === CTA_LINK_TYPE.INTERNAL) {
      openDeepLinkPaytmMoney(deeplink);
    } else if (type === CTA_LINK_TYPE.EXTERNAL) {
      openInBrowser(deeplink);
    }
  };

  return (
    <Drawer
      align={ALIGNMENTS.LEFT}
      isOpen={isOpen}
      onClose={onClose}
      showCross
      title={header}
    >
      <div className={styles.stocksDrawer}>
        <p>{text}</p>
        <Button
          buttonText={cta}
          className={styles.button}
          onClickHandler={openCTALink}
        />
      </div>
    </Drawer>
  );
};

export default StocksDrawer;

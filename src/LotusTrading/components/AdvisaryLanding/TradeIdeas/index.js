import React, { useEffect } from 'react';

import Drawer from '../../../../components/Drawer';
import HeaderWithTabs from '../../../components/HeaderWithTabs';
import TradingIdeasCard from '../../../components/AdvisaryLanding/TradingIdeasCard/TradingIdeasCard';

import QuickFilterSection from '../../../components/QuickFilterSection';

import { AdvisarySectionHeading, Carousel } from '../common';

import useMarginDrawer from '../../../hooks/useMarginDrawer';
import { useTradingIdeasContext } from '../../../context/TradingIdeasContext';

import { handleAdvisoryDeepLink } from '../utils';
import {
  sendCTAClickEvent,
  sendTabChangeEvent,
} from '../../../analyticsEvents/masterLandingEventUtils';

import {
  LOTUS_TRADING_LISTING_PAGE_TABS,
  QUICK_FILTERS_DERIVATIVES,
  QUICK_FILTERS_EQUITY,
} from '../../../config/lotusTradingConfig';

import {
  CATEGORY,
  EVENT_CATEGORY,
  MASTER_LANDING_SCREEN_NAME,
  USER_ACTION,
} from '../../../analyticsEvents/masterLandingEnums';
import { ADVISARY_LANDING_PAGE, LISTING_URL } from '../enums';

import styles from './tradeIdeas.scss';

const TradeIdeas = ({ ideas }) => {
  const {
    activeTab,
    tradingIdeas,
    isMarginLoading,
    activeQuickFilter,
    setActiveQuickFilter,
    setSelectedSortOption,
    setActiveTab,
  } = useTradingIdeasContext();

  const { card_limit } = ideas;
  // On Click of i icon inside card(Drawer)
  const {
    onMarginOpen,
    onMarginClose,
    isMarginOpen,
    sebiDetails,
  } = useMarginDrawer();

  useEffect(() => {
    setActiveQuickFilter(QUICK_FILTERS_DERIVATIVES[0].param);
  }, [activeTab]);

  const handleTabChange = tab => {
    sendTabChangeEvent(tab);
    setActiveTab(tab);
  };

  const CarouselOptions = {
    dots: true,
    slidesToShow: 1,
    slidesToScroll: 1,
    centerMode: true,
    autoplay: true,
    infinite: true,
    autoplaySpeed: 5000,
  };

  const { TRADE_IDEA, SECTION_ROLE } = ADVISARY_LANDING_PAGE;

  const quickFilterProps = {
    activeTab,
    activeQuickFilter,
    setActiveQuickFilter,
    setSelectedSortOption,
    eventCategory: EVENT_CATEGORY,
    eventScreenName: MASTER_LANDING_SCREEN_NAME,
  };

  return (
    <section
      aria-labelledby={SECTION_ROLE.TRADE_IDEA}
      className={styles.container}
    >
      <div className={styles.backgroundWhite}>
        <AdvisarySectionHeading
          subHeading={TRADE_IDEA.HEADING}
          isDescription
          isViewAll
          id={SECTION_ROLE.TRADE_IDEA}
          onClick={() => {
            sendCTAClickEvent(
              CATEGORY.VIEW_ALL_TRADE_IDEAS,
              USER_ACTION.TRADING_IDEAS_VIEW_ALL,
            );
            handleAdvisoryDeepLink(LISTING_URL);
          }}
        />
        <HeaderWithTabs
          tabs={TRADE_IDEA.TABS}
          customClass={styles.headerCustomClass}
          tabContainerCustomClass={styles.tabContainerCustomClass}
          activeTab={activeTab}
          handleTabChange={handleTabChange}
        />
      </div>

      <div className={styles.quickFilter}>
        <QuickFilterSection
          filters={
            activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]
              ? QUICK_FILTERS_DERIVATIVES
              : QUICK_FILTERS_EQUITY
          }
          activeChip={activeQuickFilter}
          {...quickFilterProps}
        />
      </div>
      <div className={styles.padding}>
        {tradingIdeas?.[activeTab.toLowerCase()]?.length > 0 ? (
          <Carousel options={CarouselOptions}>
            {tradingIdeas?.[activeTab.toLowerCase()]
              ?.slice(0, Number(card_limit))
              .map(idea => (
                <TradingIdeasCard
                  key={idea.uniqueId}
                  data={idea}
                  activeTab={activeTab}
                  customClass={styles.tradingCard}
                  onMarginOpen={onMarginOpen}
                  isMarginLoading={isMarginLoading}
                  eventCategory={EVENT_CATEGORY}
                  eventScreen={MASTER_LANDING_SCREEN_NAME}
                />
              ))}
          </Carousel>
        ) : null}
        <Drawer
          isOpen={isMarginOpen}
          onClose={onMarginClose}
          showCross
          title={sebiDetails}
          className={styles.drawer}
        />
      </div>
    </section>
  );
};

export default TradeIdeas;

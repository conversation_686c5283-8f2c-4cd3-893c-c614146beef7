.container {
  margin-top: -9px;
  margin-bottom: 25px;

}
.headerCustomClass {
 height: 100%;
 background: none;
 padding-left: 6px;
}

.tabContainerCustomClass {
  gap: 10px;
}

.padding {
  padding: 0 15px 0 15px;
}

.backgroundWhite {
    background-color: map-get($colors, primaryBgColor);
    box-shadow: 0 -1px 10px 0 rgba(16, 16, 16, 0.05);
    border-top-right-radius: 20px;
    border-top-left-radius: 20px;
}

.tradingCard {
    list-style: none;
    width: calc(100vw - 60px);
    box-shadow: 0 2px 10px 0 map-get($colors, ShadowColor);
    margin-top: 20px;
}

.drawer {
    p {
        padding: 15px;
    }
}

.quickFilter {
  top: 0;
}

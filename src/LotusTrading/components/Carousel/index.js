import React from 'react';
import classNames from 'classnames';

import styles from './index.scss';

function Carousel({ data = [], className, renderItem }) {
  return (
    <div className={classNames(styles.carouselContainer, className)}>
      {data.map((item, index) => (
        <div key={index} className={styles.snap}>
          {renderItem(item, index)}
        </div>
      ))}
    </div>
  );
}

export default Carousel;

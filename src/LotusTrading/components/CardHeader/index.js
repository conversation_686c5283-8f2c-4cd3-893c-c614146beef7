import React, { Fragment } from 'react';
import classNames from 'classnames';

import Icon from '../../../components/Icon';

import INFO from '../../../assets/icons/tradingIdeas/info.png';

import {
  BADGE_FLAG_MAPPING,
  POWERED_BY,
} from '../../config/lotusTradingConfig';
import { renderHeaderDate } from '../../config/tradingIdeasUtils';

import styles from './index.scss';

const CardHeader = ({
  pmlId,
  name,
  status,
  Status,
  sebi_details,
  provider_name,
  date_time,
  isBuy,
  onMarginOpen,
  categories,
  customClass,
  isExpandedCard,
  advisorDefination,
  tiBadgeData,
}) => {
  const renderDateTime = dateTime => (
    <div className={styles.dateTimeWrapper}>
      <span className={styles.date}>{dateTime}</span>
    </div>
  );

  const mappedLabels = tiBadgeData?.tags
    .map((value, index) => BADGE_FLAG_MAPPING[index].label[value])
    .filter(label => label !== '');

  const getStatus = () => {
    const open = status === Status[0];
    const close = status !== Status[0];
    let text = '';
    if (open) {
      text = 'Open';
    } else if (close) {
      text = 'Closed';
    }
    let chipClass = '';
    if (open) {
      chipClass = styles.open;
    } else if (close) {
      chipClass = styles.close;
    }

    return (
      <div
        className={classNames({
          [styles.absoluteStatusContainer]: categories.length === 1,
        })}
      >
        <div className={classNames(styles.statusChipClass, chipClass)}>
          {text}
        </div>
        {mappedLabels?.length > 0 &&
          mappedLabels?.map(label => (
            <div
              className={classNames(
                styles.statusChipClass,
                styles.secondaryChipClass,
              )}
            >
              {label}
            </div>
          ))}
      </div>
    );
  };

  const onCategoryClick = category => {
    if ('onClick' in category) {
      category.onClick(advisorDefination?.[category.tag]);
    }
  };

  const renderCategoryTag = category => (
    <div className={styles.category} onClick={() => onCategoryClick(category)}>
      {category.tag}
      {category.icon ? (
        <img className={styles.infoIcon} src={INFO} alt="" />
      ) : null}
    </div>
  );

  return (
    <div className={classNames(styles.header, customClass)}>
      {!isExpandedCard ? getStatus() : null}
      <div className={styles.headingContainer}>
        <Icon
          name={pmlId?.toString()}
          companyName={name}
          className={styles.icon}
          fallbackStyle={styles.fallbackStyle}
        />
        <div className={styles.nameSection}>
          <span className={styles.name}>{name}</span>
          <div
            className={classNames({
              [styles.buy]: isBuy,
              [styles.sell]: !isBuy,
            })}
          >
            {isBuy ? 'B' : 'S'}
          </div>
        </div>
      </div>
      <div className={styles.titleContainer}>
        <div className={styles.secondary}>
          {categories.length && !isExpandedCard
            ? renderCategoryTag(categories[0])
            : null}
          {isExpandedCard ? getStatus() : null}
          <div className={styles.poweredBy}>
            <div className={styles.poweredByText}>
              {POWERED_BY} <span>{provider_name}</span>
            </div>
            <img
              className={styles.infoIcon}
              src={INFO}
              alt="info-icon"
              onClick={e => {
                e.stopPropagation();
                onMarginOpen(sebi_details?.join(' '));
              }}
            />
          </div>
          {date_time ? renderDateTime(renderHeaderDate(date_time)) : null}
        </div>
      </div>

      {categories.length > 1 ? (
        <div className={styles.tags}>
          {categories.map(category => (
            <Fragment key={category}>{renderCategoryTag(category)}</Fragment>
          ))}
        </div>
      ) : null}
    </div>
  );
};

export default CardHeader;

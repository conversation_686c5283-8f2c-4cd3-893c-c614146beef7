@mixin flex {
  display: flex;
  align-items: center;
}

$PADDING: 15px;

.header {
  width: 100%;
  padding-bottom: 10px;
  border-bottom: 1px dashed map-get($colors, TertiaryColor);

  .absoluteStatusContainer {
    position: absolute;
    margin-top: -6px;
    margin-left: 16px;
    display: flex;
    gap: 8px;
  }

  .statusChipClass {
    padding: 2px 4px;
    border-radius: 2px;
    width: fit-content;

    @include typography(text3B2, map-get($colors, SuccessPrimary), false, true);
    line-height: 8px;
  }

  .secondaryChipClass {
    color: var(--primary-text-color);
    background: var(--secondary-blue-sip);
    background-color: var(--secondary-blue-sip);
    border: 0.75px solid var(--secondary-blue-sip);
  }

  .open {
    background: map-get($colors, SuccessSecondary);
  }

  .close {
    color: map-get($colors, YellowSecondary);
    background: map-get($colors, YellowPrimary);
  }

  .headingContainer {
    gap: 6px;
    display: flex;
    align-items: center;
    padding: $PADDING $PADDING 0 $PADDING;

    .icon {
      width: 20px;
      height: 20px;
      flex-grow: 0;
      padding: 1px;
      border: none;
      margin-right: 0;

      img {
        width: 20px;
        height: 20px;
        border-radius: 100px;
      }

      .fallbackStyle {
        margin-right: 0;
        border: 1px solid map-get($colors, TertiaryColor);
        background-color: map-get($colors, Gray3);
        border-radius: 100px;
        width: 20px;
        height: 20px;

        @include typography(
          heading2B2,
          map-get($colors, PrimaryColor),
          false,
          true
        );
      }
    }

    .nameSection {
      @include flex;
      gap: 6px;

      .name {
        @include typography(
          heading2B1,
          map-get($colors, PrimaryColor),
          false,
          true
        );
      }

      .buy {
        border: 0.75px solid map-get($colors, SuccessPrimary);
        @include typography(
          text3B2,
          map-get($colors, SuccessPrimary),
          false,
          true
        );
      }

      .sell {
        border: 0.75px solid map-get($colors, lossErrorColor);
        @include typography(
          text3B2,
          map-get($colors, lossErrorColor),
          false,
          true
        );
      }

      .buy,
      .sell {
        gap: 10px;
        display: flex;
        line-height: 0;
        padding: 8px 5px;
        border-radius: 2px;
        align-items: flex-start;
      }
    }
  }

  .category {
    display: flex;
    align-items: center;

    cursor: pointer;

    padding: 4px;

    border-radius: 2px;
    border: 0.75px solid map-get($colors, TertiaryColor);

    @include typography(text3B5, map-get($colors, GreySecondary), false, true);

    .infoIcon {
      width: 8px;
      height: 8px;

      margin-left: 2px;
    }
  }

  .titleContainer {
    display: flex;
    flex-direction: column;
    width: 100%;

    .secondary {
      gap: 6px;
      @include flex;
      padding: 10px $PADDING 0 $PADDING;

      .poweredBy {
        @include flex;
        gap: 2px;
        max-width: 147px;

        .poweredByText {
          @include typography(
            text22B1,
            map-get($colors, GreyPrimary),
            false,
            true
          );
        }

        .infoIcon {
          width: 16px;
          height: 16px;
          cursor: pointer;
        }
      }

      .dateTimeWrapper {
        flex: 1;
        text-align: end;

        @include typography(text24, map-get($colors, GreyPrimary), false, true);
        font-weight: 400;
      }
    }
  }

  .tags {
    gap: 4px;
    display: flex;
    align-items: center;
    margin: 9px 16px 0px 16px;
  }
}

import { sendAnalyticsEvent } from '../../services/coreUtil';

// Constants for personalized trade call events
export const PERSONALIZED_TRADE_CALL_VERTICAL_NAME = 'tradecall_2.0';
export const PERSONALIZED_TRADE_CALL_VERTICAL_NAME_V21 = 'tradecall_2.1';
export const PERSONALIZED_TRADE_CALL_VERTICAL_NAME_V22 = 'tradecall_2.2';

export const PERSONALIZED_TRADE_CALL_EVENT_CATEGORY = 'custom_event';
export const PERSONALIZED_TRADE_CALL_EVENT = 'trade_call';

// Screen names
export const SCREEN_NAMES = {
  TRADE_CALL_PERSONAL_H5: '/trade_call_personal_h5',
  HOLDINGS: '/holdings',
  POSITIONS: '/position',
  WATCHLIST: '/watchlist',
  GL<PERSON>BAL_SEARCH: '/global_search',
};

// Event actions for Trade Call H5 Page
export const TRADE_CALL_ACTIONS = {
  LANDING: 'trade_call_landing',
  REFRESH: 'trade_call_refresh',
  ALL_SELECTED: 'trade_call_all_selected',
  WATCHLIST_SELECTED: 'trade_call_watchlist_selected',
  HOLDINGS_SELECTED: 'trade_call_holdings_selected',
  POSITIONS_SELECTED: 'trade_call_positions_selected',
  SEARCH_SELECTED: 'trade_call_search_selected',
  TRADE_CALL_CLICKED: 'trade_call_clicked',
  BUY_CLICKED: 'trade_call_BUY_clicked',
  BUY_MORE_CLICKED: 'trade_call_BUY_MORE_clicked',
  NOW_CLICKED: 'trade_call_NOW_clicked',
  START_SIP_CLICKED: 'trade_call_START_SIP_clicked',
  SHARE_CLICKED: 'trade_call_SHARE_clicked',
};

// Event actions for other screens
export const OTHER_SCREEN_ACTIONS = {
  CALLOUT_CLICKED_HOLDINGS: 'trade_call_callout_clicked_holdings',
  HOLDINGS_CLICKED: 'trade_call_holdings_clicked',
  CALLOUT_CLICKED_POSITIONS: 'trade_call_callout_clicked_positions',
  POSITIONS_CLICKED: 'trade_call_positions_clicked',
  CALLOUT_CLICKED_WATCHLIST: 'trade_call_callout_clicked_watchlist',
  WATCHLIST_CLICKED: 'trade_call_watchlist_clicked',
  CALLOUT_CLICKED_SEARCH: 'trade_call_callout_clicked_search',
  SEARCH_CLICKED: 'trade_call_search_clicked',
};

// Source tags for trade calls
export const SOURCE_TAGS = {
  WATCHLIST: 'Watchlist',
  HOLDINGS: 'Holdings',
  POSITIONS: 'Positions',
  SEARCH: 'Search',
};

// Asset types
export const ASSET_TYPES = {
  EQUITY: 'Equity',
  FNO: 'FnO',
};

/**
 * Generic function to send personalized trade call analytics events
 */
const sendPersonalizedTradeCallEvent = (
  screenName,
  verticalName,
  eventAction,
  eventLabel = '',
  eventLabel2 = '',
  eventLabel3 = '',
  eventLabel4 = '',
  eventLabel5 = '',
  eventLabel6 = '',
) => {
  const data = {
    vertical_name: verticalName,
    event: PERSONALIZED_TRADE_CALL_EVENT,
    event_category: PERSONALIZED_TRADE_CALL_EVENT_CATEGORY,
    event_action: eventAction,
    event_label: eventLabel,
    event_label2: eventLabel2,
    event_label3: eventLabel3,
    event_label4: eventLabel4,
    event_label5: eventLabel5,
    event_label6: eventLabel6,
  };

  sendAnalyticsEvent(screenName, data);
};

// Trade Call H5 Page Events
export const sendTradeCallLandingEvent = landingSource => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.TRADE_CALL_PERSONAL_H5,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    TRADE_CALL_ACTIONS.LANDING,
    landingSource,
  );
};

export const sendTradeCallRefreshEvent = () => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.TRADE_CALL_PERSONAL_H5,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    TRADE_CALL_ACTIONS.REFRESH,
  );
};

export const sendTradeCallAllSelectedEvent = () => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.TRADE_CALL_PERSONAL_H5,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    TRADE_CALL_ACTIONS.ALL_SELECTED,
  );
};

export const sendTradeCallWatchlistSelectedEvent = () => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.TRADE_CALL_PERSONAL_H5,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    TRADE_CALL_ACTIONS.WATCHLIST_SELECTED,
  );
};

export const sendTradeCallHoldingsSelectedEvent = () => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.TRADE_CALL_PERSONAL_H5,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    TRADE_CALL_ACTIONS.HOLDINGS_SELECTED,
  );
};

export const sendTradeCallPositionsSelectedEvent = () => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.TRADE_CALL_PERSONAL_H5,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    TRADE_CALL_ACTIONS.POSITIONS_SELECTED,
  );
};

export const sendTradeCallSearchSelectedEvent = () => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.TRADE_CALL_PERSONAL_H5,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    TRADE_CALL_ACTIONS.SEARCH_SELECTED,
  );
};

export const sendTradeCallClickedEvent = (
  stockName,
  pmlId,
  assetType,
  raName,
  sourceTag,
) => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.TRADE_CALL_PERSONAL_H5,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    TRADE_CALL_ACTIONS.TRADE_CALL_CLICKED,
    stockName,
    pmlId,
    assetType,
    raName,
    sourceTag,
  );
};

export const sendTradeCallBuyClickedEvent = (
  stockName,
  pmlId,
  assetType,
  raName,
  sourceTag,
) => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.TRADE_CALL_PERSONAL_H5,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    TRADE_CALL_ACTIONS.BUY_CLICKED,
    stockName,
    pmlId,
    assetType,
    raName,
    sourceTag,
  );
};

export const sendTradeCallBuyMoreClickedEvent = (
  stockName,
  pmlId,
  assetType,
  raName,
  sourceTag,
) => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.TRADE_CALL_PERSONAL_H5,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME_V21,
    TRADE_CALL_ACTIONS.BUY_MORE_CLICKED,
    stockName,
    pmlId,
    assetType,
    raName,
    sourceTag,
  );
};

export const sendTradeCallNowClickedEvent = (
  stockName,
  pmlId,
  assetType,
  raName,
  sourceTag,
) => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.TRADE_CALL_PERSONAL_H5,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME_V22,
    TRADE_CALL_ACTIONS.NOW_CLICKED,
    stockName,
    pmlId,
    assetType,
    raName,
    sourceTag,
  );
};

export const sendTradeCallStartSipClickedEvent = (
  stockName,
  pmlId,
  assetType,
  raName,
  sourceTag,
) => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.TRADE_CALL_PERSONAL_H5,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    TRADE_CALL_ACTIONS.START_SIP_CLICKED,
    stockName,
    pmlId,
    assetType,
    raName,
    sourceTag,
  );
};

export const sendTradeCallShareClickedEvent = (
  stockName,
  pmlId,
  assetType,
  raName,
  sourceTag,
) => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.TRADE_CALL_PERSONAL_H5,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    TRADE_CALL_ACTIONS.SHARE_CLICKED,
    stockName,
    pmlId,
    assetType,
    raName,
    sourceTag,
  );
};

// Holdings Events
export const sendTradeCallCalloutClickedHoldingsEvent = () => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.HOLDINGS,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    OTHER_SCREEN_ACTIONS.CALLOUT_CLICKED_HOLDINGS,
  );
};

export const sendTradeCallHoldingsClickedEvent = (
  stockName,
  pmlId,
  assetType,
) => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.HOLDINGS,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    OTHER_SCREEN_ACTIONS.HOLDINGS_CLICKED,
    stockName,
    pmlId,
    assetType,
  );
};

// Positions Events
export const sendTradeCallCalloutClickedPositionsEvent = () => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.POSITIONS,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    OTHER_SCREEN_ACTIONS.CALLOUT_CLICKED_POSITIONS,
  );
};

export const sendTradeCallPositionsClickedEvent = (
  stockName,
  pmlId,
  assetType,
) => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.POSITIONS,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    OTHER_SCREEN_ACTIONS.POSITIONS_CLICKED,
    stockName,
    pmlId,
    assetType,
  );
};

// Watchlist Events
export const sendTradeCallCalloutClickedWatchlistEvent = () => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.WATCHLIST,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    OTHER_SCREEN_ACTIONS.CALLOUT_CLICKED_WATCHLIST,
  );
};

export const sendTradeCallWatchlistClickedEvent = (
  stockName,
  pmlId,
  assetType,
) => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.WATCHLIST,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    OTHER_SCREEN_ACTIONS.WATCHLIST_CLICKED,
    stockName,
    pmlId,
    assetType,
  );
};

// Search Events
export const sendTradeCallCalloutClickedSearchEvent = () => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.GLOBAL_SEARCH,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    OTHER_SCREEN_ACTIONS.CALLOUT_CLICKED_SEARCH,
  );
};

export const sendTradeCallSearchClickedEvent = (
  stockName,
  pmlId,
  assetType,
) => {
  sendPersonalizedTradeCallEvent(
    SCREEN_NAMES.GLOBAL_SEARCH,
    PERSONALIZED_TRADE_CALL_VERTICAL_NAME,
    OTHER_SCREEN_ACTIONS.SEARCH_CLICKED,
    stockName,
    pmlId,
    assetType,
  );
};

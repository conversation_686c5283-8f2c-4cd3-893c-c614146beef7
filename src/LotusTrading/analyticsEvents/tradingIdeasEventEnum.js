export const TRADING_IDEAS_TAB_ACTIONS = {
  SECTIONSELECT_EQUITY_CLICK: 'SectionSelect_Equity_Click',
  SECTIONSELECT_DERIVATIVES_CLICK: 'SectionSelect_Derivatives_Click',
  FILTERDERIVATIVES_ALL_CLICK: 'FilterDerivatives_All_click',
  FILTERDERIVATIVES_FUTURES_CLICK: 'FilterDerivatives_Futures_click',
  FILTERDERIVATIVES_OPTIONS_CLICK: 'FilterDerivatives_Options_click',
  FILTEREQUITY_ALL_CLICK: 'FilterEquity_All_click',
  FILTEREQUITY_INTRADAY_CLICK: 'FilterEquity_Intraday_click',
  EQUITY_POSITIONAL_CLICK: 'Equity_Positional_click',
  EQUITY_LONGTERM_CLICK: 'Equity_Longterm_click',
};
export const USER_ACTION = {
  ...TRADING_IDEAS_TAB_ACTIONS,
  RAHOME_OPEN: 'RAHome_Open',
  RAHOMEADVISORID_OPEN: 'RAHomeAdvisorID_Open',
  DERIVATIVESBUY_FUTURE_INTRADAY_CLICK: 'DerivativesBuy_Future_Intraday_Click',
  DERIVATIVESBUY_FUTURE_SWING_CLICK: 'DerivativesBuy_Future_Swing_Click',
  DERIVATIVESBUY_OPTION_INTRADAY_CLICK: 'DerivativesBuy_Option_Intraday_Click',
  DERIVATIVESBUY_OPTION_SWING_CLICK: 'DerivativesBuy_Option_Swing_Click',
  EQUITYBUY_INTRADAY_BUY_CLICK: 'EquityBuy_Intraday_Buy_Click',
  EQUITYBUY_INTRADAY_BUYBRACKET_CLICK: 'EquityBuy_Intraday_BuyBracket_Click',
  EQUITYBUY_SWING_BUY_CLICK: 'EquityBuy_Swing_Buy_Click',
  EQUITYBUY_LONGTERM_BUY_CLICK: 'EquityBuy_Longterm_Buy_Click',
  DERIVATIVESSELL_FUTURE_INTRADAY_CLICK:
    'DerivativesSell_Future_Intraday_Click',
  DERIVATIVESSELL_FUTURE_SWING_CLICK: 'DerivativesSell_Future_Swing_Click',
  DERIVATIVESSELL_OPTION_INTRADAY_CLICK:
    'DerivativesSell_Option_Intraday_Click',
  DERIVATIVESSELL_OPTION_SWING_CLICK: 'DerivativesSell_Option_Swing_Click',
  EQUITYSELL_INTRADAY_SELL_CLICK: 'EquitySell_Intraday_Sell_Click',
  EQUITYSELL_INTRADAY_SELLBRACKET_CLICK:
    'EquitySell_Intraday_SellBracket_Click',
  EQUITYSELL_SWING_SELL_CLICK: 'EquitySell_Swing_Sell_Click',
  EQUITYSELL_LONGTERM_SELL_CLICK: 'EquitySell_Longterm_Sell_Click',
};

export const SCREEN_NAME_RA_TNC = 'RA_TnC';
export const SCREEN_NAME_RA_LANDING = 'RA_landing';
export const SCREEN_NAME_ADVISOR = 'RA_AdvisorSpecific';

export const TRADING_IDEAS_VERTICAL_NAME = 'TradeIdeas';

export const OPEN_SCREEN = 'openScreen';

export const EVENT_CATEGORY = {
  TRADECALLS_HOME: 'TradeCalls_Home',
};

export const TRADING_IDEAS_WEB_PAGE_OPEN = 'open_screen';

export const TRADING_IDEAS_WEB_PAGE_OPEN_EVENT_CATEGORY = 'User open page';

export const TRADING_IDEAS_CATEGORY = {
  PAGE_OPEN: 'PageOpen',
  SECTION_SELECT: 'SectionSelect',
  FILTER_DERIVATIVES: 'FilterDerivatives',
  FILTER_EQUITY: 'FilterEquity',
  DERIVATIVES_BUY: 'DerivativesBuy',
  EQUITY_BUY: 'EquityBuy',
  DERIVATIVES_SELL: 'DerivativesSell',
  EQUITY_SELL: 'EquitySell',
};

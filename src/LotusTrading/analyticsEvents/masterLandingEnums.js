import { TRADING_IDEAS_TAB_ACTIONS } from './tradingIdeasEventEnum';

export const USER_ACTION = {
  ...TRADING_IDEAS_TAB_ACTIONS,
  HOME: 'RAHomeAdvisorID_Open',
  EXT_BEARISH: 'drag_bearish',
  EXT_BULLISH: 'drag_bullish',
  NEUTRAL: 'drag_neutral',
  BULLISH: 'drag_bullish',
  BEARISH: 'drag_bearish',
  SLEEK_CLICK: 'sleek_click',
  TRADING_IDEAS_VIEW_ALL: 'tradingIdeas_viewall_click',
  STOCK_SIP_VIEW_ALL: 'stocksip_viewall_click',
  STOCK_SIP_CREATE_SIP: 'stocksip_createsip_click',
  STOCK_SIP_DOWNLOAD_REPORT: 'stocksip_report_click',
  TRADING_OPTIONS_KNOW_MORE: 'tradingoptions_knowmore',
  TRADING_OPTIONS_CLICK: 'tradingoptions_click',
  MASTER_BANNER_CLICK: 'masterbanner_click',
  VIDEO_CLICK: 'masterbanner_video_click',
  BANNER_CLICK: 'banner_click',
  BLOG_CLICK: 'blog_click',
};

export const MASTER_LANDING_SCREEN_NAME = 'TradeCasllsMaster_landing';

export const EVENT_CATEGORY = 'TradeCalls_Home_Master';

export const CATEGORY = {
  PAGE_OPEN: 'PageOpen',
  MARKET_SENTIMENT: 'UserVotesMarketSentiment',
  SELECT_SELECT: 'SectionSelect',
  USER_VOTES_MARKET_SENTIMENT: 'UserVotesMarketSentiment',
  CLICKS_SLEEK: 'ClicksSleek',
  VIEW_ALL_TRADE_IDEAS: 'ViewAllTradingIdeas',
  VIEW_ALL_STOCK_SIP: 'ViewAllStockSIP',
  CREATE_SIP: 'CreateSIP',
  DOWNLOAD_SIP_REPORT: 'DownloadSIPReport',
  INVESTMENT_TRADE_OPTIONS: 'InvestmentTradingOptions',
  MASTER_BANNER: 'MasterBanner',
  BANNER: 'BANNER',
  BLOG: 'Blog',
};

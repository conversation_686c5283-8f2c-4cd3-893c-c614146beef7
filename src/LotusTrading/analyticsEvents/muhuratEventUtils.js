import { sendAnalyticsEvent } from '../../services/coreUtil';
import {
  EVENT_CATEGORY,
  SCREEN_NAME,
  USER_ACTION,
  VERTICAL_NAME,
} from './muhuratListing';

const sendEvent = (eventCategory, eventAction, label3 = '') => {
  const data = {
    vertical_name: VERTICAL_NAME,
    event_category: eventCategory,
    event_action: eventAction,
    event_label2: '',
    event_label3: label3,
    event_label6: 6,
  };
  sendAnalyticsEvent(SCREEN_NAME, data);
};

export const sendBuySellClickEvents = scriptName => {
  sendEvent(EVENT_CATEGORY.BUY_BUTTON, USER_ACTION.BUTTON_CLICKED, scriptName);
};

export const sendBackButtonClickEvent = () => {
  sendEvent(EVENT_CATEGORY.BACK_BUTTON, USER_ACTION.BUTTON_CLICKED);
};

export const sendMuhuratListingOpenEvent = () => {
  sendEvent(EVENT_CATEGORY.MUHARAT_TRADING_PAGE, USER_ACTION.OPEN_SCREEN);
};

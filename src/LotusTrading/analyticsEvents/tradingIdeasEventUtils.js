import { sendAnalyticsEvent } from '../../services/coreUtil';
import {
  LOTUS_TRADING_LISTING_PAGE_TABS,
  QUICK_FILTERS_DERIVATIVES,
  QUICK_FILTERS_EQUITY,
} from '../config/lotusTradingConfig';
import {
  SCREEN_NAME_RA_LANDING,
  TRADING_IDEAS_CATEGORY,
  TRADING_IDEAS_WEB_PAGE_OPEN,
  TRADING_IDEAS_WEB_PAGE_OPEN_EVENT_CATEGORY,
  USER_ACTION,
  TRADING_IDEAS_VERTICAL_NAME,
  OPEN_SCREEN,
} from './tradingIdeasEventEnum';

const sendEvent = (
  eventCategory,
  userAction,
  screenName = SCREEN_NAME_RA_LANDING,
  category = '',
) => {
  let data = {
    event_category: eventCategory,
    event_action: userAction,
    vertical_name: TRADING_IDEAS_VERTICAL_NAME,
  };
  if (category) {
    data = {
      ...data,
      category,
    };
  }
  sendAnalyticsEvent(screenName, data);
};

export const sendBuySellClickEvents = (
  eventCategory,
  activeTab,
  futureOption,
  tag1,
  orderType,
  isBuy,
  eventScreen,
) => {
  let userAction = '';
  let category = '';
  const activeQuickFilter = tag1?.replaceAll(' ', '');
  if (activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]) {
    category = isBuy
      ? TRADING_IDEAS_CATEGORY.DERIVATIVES_BUY
      : TRADING_IDEAS_CATEGORY.DERIVATIVES_SELL;
    switch (activeTab + futureOption + activeQuickFilter) {
      case `derivativesFutureIntraday`:
        userAction = isBuy
          ? USER_ACTION.DERIVATIVESBUY_FUTURE_INTRADAY_CLICK
          : USER_ACTION.DERIVATIVESSELL_FUTURE_INTRADAY_CLICK;
        break;

      case 'derivativesFutureShortTerm':
        userAction = isBuy
          ? USER_ACTION.DERIVATIVESBUY_FUTURE_SWING_CLICK
          : USER_ACTION.DERIVATIVESSELL_FUTURE_SWING_CLICK;
        break;

      case 'derivativesOptionIntraday':
        userAction = isBuy
          ? USER_ACTION.DERIVATIVESBUY_OPTION_INTRADAY_CLICK
          : USER_ACTION.DERIVATIVESSELL_OPTION_INTRADAY_CLICK;
        break;

      case `derivativesOptionShortTerm`:
        userAction = isBuy
          ? USER_ACTION.DERIVATIVESBUY_OPTION_SWING_CLICK
          : USER_ACTION.DERIVATIVESSELL_OPTION_SWING_CLICK;
        break;

      default:
        break;
    }
  } else {
    category = isBuy
      ? TRADING_IDEAS_CATEGORY.EQUITY_BUY
      : TRADING_IDEAS_CATEGORY.EQUITY_SELL;
    switch (activeTab + activeQuickFilter + orderType) {
      case 'equityIntraday':
        userAction = isBuy
          ? USER_ACTION.EQUITYBUY_INTRADAY_BUY_CLICK
          : USER_ACTION.EQUITYSELL_INTRADAY_SELL_CLICK;
        break;

      case 'equityIntradaybracket':
        userAction = isBuy
          ? USER_ACTION.EQUITYBUY_INTRADAY_BUYBRACKET_CLICK
          : USER_ACTION.EQUITYSELL_INTRADAY_SELLBRACKET_CLICK;
        break;

      case `equityShortTerm`:
        userAction = isBuy
          ? USER_ACTION.EQUITYBUY_SWING_BUY_CLICK
          : USER_ACTION.EQUITYSELL_SWING_SELL_CLICK;
        break;

      case 'equityLongTerm':
        userAction = isBuy
          ? USER_ACTION.EQUITYBUY_LONGTERM_BUY_CLICK
          : USER_ACTION.EQUITYSELL_LONGTERM_SELL_CLICK;
        break;

      default:
        break;
    }
  }
  sendEvent(eventCategory, userAction, eventScreen, category);
};

export const sendQuickFilterClickEvent = (
  eventCategory,
  activeTab,
  filter,
  screenName,
) => {
  let userAction = '';
  let category = '';
  if (activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]) {
    category = TRADING_IDEAS_CATEGORY.FILTER_DERIVATIVES;
    switch (filter) {
      case QUICK_FILTERS_DERIVATIVES[1].param:
        userAction = USER_ACTION.FILTERDERIVATIVES_FUTURES_CLICK;
        break;
      case QUICK_FILTERS_DERIVATIVES[2].param:
        userAction = USER_ACTION.FILTERDERIVATIVES_OPTIONS_CLICK;
        break;
      default:
        userAction = USER_ACTION.FILTERDERIVATIVES_ALL_CLICK;
        break;
    }
  } else {
    category = TRADING_IDEAS_CATEGORY.FILTER_EQUITY;
    switch (filter) {
      case QUICK_FILTERS_EQUITY[1].param:
        userAction = USER_ACTION.FILTEREQUITY_INTRADAY_CLICK;
        break;
      case QUICK_FILTERS_EQUITY[2].param:
        userAction = USER_ACTION.EQUITY_POSITIONAL_CLICK;
        break;
      case QUICK_FILTERS_EQUITY[3].param:
        userAction = USER_ACTION.EQUITY_LONGTERM_CLICK;
        break;
      default:
        userAction = USER_ACTION.FILTEREQUITY_ALL_CLICK;
        break;
    }
  }
  sendEvent(eventCategory, userAction, screenName, category);
};

export const sendTabChangeEvent = (eventCategory, tab) => {
  let userAction = '';
  if (tab === LOTUS_TRADING_LISTING_PAGE_TABS[0]) {
    userAction = USER_ACTION.SECTIONSELECT_DERIVATIVES_CLICK;
  } else {
    userAction = USER_ACTION.SECTIONSELECT_EQUITY_CLICK;
  }
  sendEvent(eventCategory, userAction, TRADING_IDEAS_CATEGORY.SECTION_SELECT);
};

export const sendLandingPageOpenEvent = (eventCategory, userAction) => {
  sendEvent(eventCategory, userAction, TRADING_IDEAS_CATEGORY.PAGE_OPEN);
};

export const sendWebPageOpenEvent = (screen_name, sendWebPulseEvent) => {
  if (typeof sendWebPulseEvent === 'function') {
    sendWebPulseEvent({
      eventDetails: {
        event_category: TRADING_IDEAS_WEB_PAGE_OPEN_EVENT_CATEGORY,
        event_action: TRADING_IDEAS_WEB_PAGE_OPEN,
        event_label: '',
        eventType: OPEN_SCREEN,
        vertical_name: TRADING_IDEAS_VERTICAL_NAME,
        screenName: `/${screen_name}`,
      },
    });
  }
};

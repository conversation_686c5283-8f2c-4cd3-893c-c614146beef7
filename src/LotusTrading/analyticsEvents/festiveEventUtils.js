import { sendAnalyticsEvent } from '../../services/coreUtil';
import { getQueryParams } from '../../utils/commonUtils';
import {
  EVENT_CATEGORY,
  LINK_TYPE,
  SCREEN_NAME,
  USER_ACTION,
  VERTICAL_NAME,
} from './festiveListing';

const getLinkType = () => {
  const params = getQueryParams();
  const { availableOnPaytm } = params;
  if (availableOnPaytm === 'onelink') {
    return LINK_TYPE.onelink;
  } else if (availableOnPaytm === 'mini') {
    return LINK_TYPE.mini;
  } else if (availableOnPaytm === 'miniBypass') {
    return LINK_TYPE.miniSpecialLinktype;
  }
  return LINK_TYPE.main;
};

const sendEvent = (eventCategory, eventAction, label2 = '', label3 = '') => {
  const data = {
    vertical_name: VERTICAL_NAME,
    event_category: eventCategory,
    event_action: eventAction,
    event_label2: label2,
    event_label3: label3,
    event_label4: getLinkType(),
    event_label6: 6,
  };
  sendAnalyticsEvent(SCREEN_NAME, data);
};

export const sendBackButtonClickEvent = () => {
  sendEvent(EVENT_CATEGORY.BACK_BUTTON, USER_ACTION.BUTTON_CLICKED);
};

export const sendFestiveListingOpenEvent = () => {
  sendEvent(EVENT_CATEGORY.DIWALI_LANDING_PAGE, USER_ACTION.OPEN_SCREEN);
};

export const sendNudgeClickEvent = () => {
  sendEvent(EVENT_CATEGORY.WHY_LINK, USER_ACTION.LINK_CLICKED);
};

export const sendBuyClickEvents = (isMain = false, name = '') => {
  const label2 = isMain ? 'main_button' : 'nudge_button';
  sendEvent(
    EVENT_CATEGORY.BUY_BUTTON,
    USER_ACTION.BUTTON_CLICKED,
    label2,
    name,
  );
};

export const sendBannerClickEvents = () => {
  sendEvent(EVENT_CATEGORY.BANNER, USER_ACTION.BANNER_CLICKED);
};

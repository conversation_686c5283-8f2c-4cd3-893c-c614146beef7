import { sendAnalyticsEvent } from '../../services/coreUtil';
import { LOTUS_TRADING_LISTING_PAGE_TABS } from '../config/lotusTradingConfig';
import {
  CATEGORY,
  EVENT_CATEGORY,
  MASTER_LANDING_SCREEN_NAME,
  USER_ACTION,
} from './masterLandingEnums';
import { TRADING_IDEAS_VERTICAL_NAME } from './tradingIdeasEventEnum';

const sendEvent = (eventCategory, userAction, category = '') => {
  let data = {
    event_category: eventCategory,
    event_action: userAction,
    vertical_name: TRADING_IDEAS_VERTICAL_NAME,
  };
  if (category) {
    data = {
      ...data,
      category,
    };
  }
  sendAnalyticsEvent(MASTER_LANDING_SCREEN_NAME, data);
};

export const sendTabChangeEvent = tab => {
  let userAction = '';
  const category = CATEGORY.SELECT_SELECT;
  if (tab === LOTUS_TRADING_LISTING_PAGE_TABS[0]) {
    userAction = USER_ACTION.SECTIONSELECT_DERIVATIVES_CLICK;
  } else {
    userAction = USER_ACTION.SECTIONSELECT_EQUITY_CLICK;
  }
  sendEvent(EVENT_CATEGORY, userAction, category);
};

export const sendLandingPageOpenEvent = () => {
  sendEvent(EVENT_CATEGORY, USER_ACTION.HOME, CATEGORY.PAGE_OPEN);
};

export const sendCTAClickEvent = (category, userAction) => {
  sendEvent(EVENT_CATEGORY, userAction, category);
};

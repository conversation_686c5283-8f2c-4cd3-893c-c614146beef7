import React, {
  useMemo,
  createContext,
  useContext,
  useState,
  useEffect,
} from 'react';
import _ from 'lodash';
import { log } from '../../utils/commonUtils';
import { getMarginData } from '../actions/lotusActions';
import {
  DEFAULT_DERIVATIVES_SORT,
  DEFAULT_EQUITY_SORT,
  DEFAULT_QUICK_FILTERS,
  LOTUS_TRADING_LISTING_PAGE_TABS,
  PERSONALIZED_TRADING_IDEAS_TABS,
} from '../config/lotusTradingConfig';
import {
  filterTradingIdeasData,
  filterBasedOnScriptID,
  scrollToTop,
} from '../utils/utilFunctions';
import { getInfoMsgJson } from '../../actions/commonActions';
import { INSTRUMENT_TYPE, EXCHANGE } from '../../utils/enum';
import { getTradingIdeas } from '../query/lotusTradingQuery';
import { BASE_URL } from '../../config/envConfig';
import { setRootError } from '../../actions/genericActions';
import { DATA_NO_MSG } from './enums';
import { isSavedFilters } from '../config/tradingIdeasUtils';
import { makeApiGetCall, getGenericAppHeaders } from '../../utils/apiUtil';
import {
  HOLDINGS_API,
  POSITIONS_API,
  BADGE_SERVICE_API,
  SEARCHED_API,
  WATCHLIST_SEC_API,
  WATCHLIST_API,
} from '../config/urlConfig';

const PersonalizedTradingIdeasContext = createContext();

let isJamoonFallbackCalled = false;
let isAdvisorsSet = false;
let modifiedAdvisorsCache = {};

export const PersonalizedTradingIdeasProvider = ({ children, ...props }) => {
  // STATE VARIABLES
  const [isTradingCardLoading, setIsTradingCardLoading] = useState(false);
  const [tradingIdeas, setTradingIdeas] = useState({});
  const [tradingIdeasClone, setTradingIdeasClone] = useState({});
  const [isMarginLoading, setMarginLoading] = useState(true);
  const [marginData, setMarginData] = useState([]);
  const [activeTab, setActiveTab] = useState(
    PERSONALIZED_TRADING_IDEAS_TABS[0].toLowerCase(),
  );
  const [activeQuickFilter, setActiveQuickFilter] = useState(
    DEFAULT_QUICK_FILTERS,
  );
  const [filteredMarginSort, setFilteredMarginSort] = useState({});

  const defaultSortOption =
    activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]
      ? DEFAULT_DERIVATIVES_SORT
      : DEFAULT_EQUITY_SORT;

  const [selectedSortOption, setSelectedSortOption] = useState(
    defaultSortOption,
  );
  const [advisorJSON, setAdvisorJSON] = useState({});
  const [filterDerivativeValues, setFilterDerivateValues] = useState(null);
  const [filterEquityValues, setFilterEquityValues] = useState(null);
  const [isEntryNull, setIsEntryNull] = useState(1);
  const [isJamoonApiFailed, setIsJamoonApiFailed] = useState(false);
  const [advisorDefination, setAdvisorDefination] = useState(null);
  const [currentSortOption, setCurrentSortOption] = useState(defaultSortOption);
  const [watchlistData, setWatchlistData] = useState([]);
  const [holdingsData, setHoldingsData] = useState([]);
  const [positionsData, setPositionsData] = useState([]);
  const [searchData, setSearchData] = useState([]);
  const [isWatchlistLoading, setIsWatchlistLoading] = useState(false);
  const [isHoldingsLoading, setIsHoldingsLoading] = useState(false);
  const [isPositionsLoading, setIsPositionsLoading] = useState(false);
  const [isSearchLoading, setIsSearchLoading] = useState(false);
  const [filteredWatchlistIdeas, setFilteredWatchlistIdeas] = useState([]);
  const [filteredHoldingsIdeas, setFilteredHoldingsIdeas] = useState([]);
  const [filteredPositionsIdeas, setFilteredPositionsIdeas] = useState([]);
  const [filteredSearchIdeas, setFilteredSearchIdeas] = useState([]);
  const [allTradingIdeas, setAllTradingIdeas] = useState([]);
  const [isBadgeLoading, setIsBadgeLoading] = useState(false);
  const [tiBadgeData, setTiBadgeData] = useState([]);

  // TRADING IDEAS FETCH
  const filterTradingIdeas = (res, isClone = false) => {
    // return filtered cards with invalid/falsy values of the following:
    // pml_id, script_id, script_name, date, status, time,
    // reco, rationale, tag1, price_when_created, stop
    const { derivatives, equity } = res;
    setAllTradingIdeas([...derivatives, ...equity]);

    if (activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]) {
      return {
        derivatives: filterTradingIdeasData(derivatives),
        equity: isSavedFilters() && !isClone ? [] : equity,
      };
    }
    return {
      derivatives: isSavedFilters() && !isClone ? [] : derivatives,
      equity: filterTradingIdeasData(equity),
    };
  };

  const fetchDataWithMargins = async filteredResponse => {
    const { derivatives } = filteredResponse;
    const res = await getMarginData(filterBasedOnScriptID(derivatives));

    setMarginData(res);
    setMarginLoading(false);
  };

  const getTradingIdeasList = async (params, isFailed) => {
    const equity = [];
    const derivatives = [];

    const res = await getTradingIdeas(props.axiosSource, params, isFailed);

    if (res.flag === 'false') {
      setRootError({
        message: DATA_NO_MSG,
      });
      return;
    }
    const advisorsRes = await getInfoMsgJson(
      BASE_URL.ADVISOR_JSON,
      props.axiosSource,
    );

    const advisors = {};
    advisorsRes.forEach(advisor => {
      advisors[advisor.id] = advisor;
    });

    if (res && res?.entries) {
      let derivativesFilterValues = {
        Open: 0,
        Closed: 0,
        Intraday: 0,
        ShortTerm: 0,
        optidx: 0,
        optstk: 0,
        futstk: 0,
        futidx: 0,
      };

      let equityFilterValues = {
        Open: 0,
        Closed: 0,
        Intraday: 0,
        ShortTerm: 0,
        LongTerm: 0,
      };

      const derivativeAdvisorsCount = {};
      const equityAdvisorsCount = {};
      setIsEntryNull(!res.entries.entry.length);

      res.entries.entry.forEach(entry => {
        const modifiedIdeasObj = {
          script_id: entry.scripMaster.script_ID,
          uniqueId: _.uniqueId(entry.scripMaster.script_ID),
          pml_id: entry.scripMaster.pml_ID,
          lot_size: entry.scripMaster.lot_Size,
          script_name: entry.scripMaster.script_Name,
          last_UpdateTime: entry.scripMaster.last_UpdateTime,
          exit_condition: entry.closeprice,
          provider_name: advisors[entry.advisorid]?.name,
          channel: advisors[entry.advisorid]?.channel,
          advisorDescription: advisors[entry.advisorid]?.description,
          sebi_details: advisors[entry.advisorid]?.sebi_details,
          advisorsPointers: advisors[entry.advisorid]?.pointers,
          advisorsPhoto: advisors[entry.advisorid]?.photo,
          future_option:
            entry.instrument === INSTRUMENT_TYPE.OPTIDX ||
            entry.instrument === INSTRUMENT_TYPE.OPTSTK
              ? 'Option'
              : 'Future',
          ...entry,
        };

        // Derivative
        if (entry.exchange === EXCHANGE.NSEFNO) {
          derivativeAdvisorsCount[entry.advisorid] = derivativeAdvisorsCount[
            entry.advisorid
          ]
            ? derivativeAdvisorsCount[entry.advisorid] + 1
            : 1;
          derivativesFilterValues = {
            Open: (derivativesFilterValues.Open +=
              entry.callstatus === 'Open' ? 1 : 0),
            Closed: (derivativesFilterValues.Closed +=
              entry.callstatus !== 'Open' ? 1 : 0),
            Intraday: (derivativesFilterValues.Intraday +=
              entry.category === 'Intraday' ? 1 : 0),
            ShortTerm: (derivativesFilterValues.ShortTerm +=
              entry.category === 'Short Term' ? 1 : 0),
            optidx: (derivativesFilterValues.optidx +=
              entry.instrument === 'OPTIDX' ? 1 : 0),
            optstk: (derivativesFilterValues.optstk +=
              entry.instrument === 'OPTSTK' ? 1 : 0),
            futstk: (derivativesFilterValues.futstk +=
              entry.instrument === 'FUTSTK' ? 1 : 0),
            futidx: (derivativesFilterValues.futidx +=
              entry.instrument === 'FUTIDX' ? 1 : 0),
          };
          derivatives.push(modifiedIdeasObj);
        } else if (entry.exchange === EXCHANGE.NSECASH) {
          // Eqeuity
          equityAdvisorsCount[entry.advisorid] = equityAdvisorsCount[
            entry.advisorid
          ]
            ? equityAdvisorsCount[entry.advisorid] + 1
            : 1;
          equityFilterValues = {
            Open: (equityFilterValues.Open +=
              entry.callstatus === 'Open' ? 1 : 0),
            Closed: (equityFilterValues.Closed +=
              entry.callstatus !== 'Open' ? 1 : 0),
            Intraday: (equityFilterValues.Intraday +=
              entry.category === 'Intraday' ? 1 : 0),
            ShortTerm: (equityFilterValues.ShortTerm +=
              entry.category === 'Short Term' ? 1 : 0),
            LongTerm: (equityFilterValues.LongTerm +=
              entry.category === 'Long Term' ? 1 : 0),
            ...equityFilterValues,
          };
          equity.push(modifiedIdeasObj);
        }
      });
      if (Object.keys(modifiedAdvisorsCache).length) {
        setAdvisorJSON(modifiedAdvisorsCache);
      } else if (!isAdvisorsSet) {
        const modifiedAdvisors = {
          advisors: advisorsRes.map(advisor => ({
            count:
              (equityAdvisorsCount[advisor.id] || 0) +
              (derivativeAdvisorsCount[advisor.id] || 0),
            ...advisor,
          })),
          derivativeAdvisorsCount,
          equityAdvisorsCount,
        };
        setAdvisorJSON(modifiedAdvisors);
        modifiedAdvisorsCache = modifiedAdvisors;
        isAdvisorsSet = true;
      }
      setFilterDerivateValues(derivativesFilterValues);
      setFilterEquityValues(equityFilterValues);
    }

    return { equity, derivatives };
  };

  const fetchTradingIdeas = async (params, isFailed) => {
    try {
      setIsTradingCardLoading(true);
      const res = await getTradingIdeasList(params, isFailed);
      // filter response for invalid fields
      setTradingIdeas(filterTradingIdeas(res, false));
      setTradingIdeasClone(filterTradingIdeas(res, true));
      setIsTradingCardLoading(false);
      fetchDataWithMargins(filterTradingIdeas(res, false));
      setSelectedSortOption(
        activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]
          ? DEFAULT_DERIVATIVES_SORT
          : DEFAULT_EQUITY_SORT,
      );
      setCurrentSortOption(
        activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]
          ? DEFAULT_DERIVATIVES_SORT
          : DEFAULT_EQUITY_SORT,
      );
      isJamoonFallbackCalled = false;
      if (!isFailed) {
        setIsJamoonApiFailed(false);
      }
    } catch (err) {
      if (!isJamoonFallbackCalled) {
        fetchTradingIdeas('', true);
        setIsJamoonApiFailed(true);
        isJamoonFallbackCalled = true;
      }
      log(err);
    }
  };

  const filteredIdeas = data => ({
    ...tradingIdeasClone,
    [activeTab]: (tradingIdeasClone[activeTab] || []).filter(
      ele => ele[data]?.split(' ').join('') === activeQuickFilter,
    ),
  });

  const fetchAdvisorDefination = async () => {
    const advisorDefinationRes = await getInfoMsgJson(
      BASE_URL.ADVISOR_DEFINATION,
      props.axiosSource,
    );

    setAdvisorDefination(advisorDefinationRes);
  };

  // HELPER FUNCTION TO FILTER TRADING IDEAS BY ISIN
  const filterTradingIdeasByIsin = (apiData, tradingIdeasData) => {
    if (!Array.isArray(apiData) || !Array.isArray(tradingIdeasData)) {
      return [];
    }

    const isinSet = new Set(
      apiData.flatMap(item => [item.isin, item.isin_code].filter(Boolean)),
    );

    return tradingIdeasData.filter(
      idea => isinSet.has(idea.isin) || isinSet.has(idea.isin_code),
    );
  };

  useEffect(() => {
    if (!allTradingIdeas?.length) return;
    const fetchBadgeData = async () => {
      try {
        setIsBadgeLoading(true);

        const url = BADGE_SERVICE_API;
        const headers = getGenericAppHeaders();

        const response = await makeApiGetCall({
          url,
          headers,
          axiosSource: props.axiosSource,
        });

        if (response && response.data) {
          setTiBadgeData(response?.data?.data.results || []);
        }
      } catch (error) {
        log('Error fetching badge data:', error);
      } finally {
        setIsBadgeLoading(false);
      }
    };
    fetchBadgeData();
  }, [tradingIdeas]);

  useEffect(() => {
    if (!allTradingIdeas?.length) return;
    const fetchWatchlist = async () => {
      try {
        setIsWatchlistLoading(true);

        const watchlistUrl = WATCHLIST_API;
        const headers = getGenericAppHeaders();

        const watchlistResponse = await makeApiGetCall({
          url: watchlistUrl,
          headers,
          axiosSource: props.axiosSource,
        });

        if (watchlistResponse && watchlistResponse.data) {
          const watchlistValues =
            watchlistResponse?.data?.data?.watchlists?.map(w => String(w.id)) ||
            [];

          const securityPromises = watchlistValues.map(async value => {
            try {
              const securityUrl = WATCHLIST_SEC_API(value);
              const securityResponse = await makeApiGetCall({
                url: securityUrl,
                headers,
                axiosSource: props.axiosSource,
              });
              return securityResponse?.data || [];
            } catch (error) {
              log(`Error fetching security for value ${value}:`, error);
              return [];
            }
          });

          const securityResults = await Promise.all(securityPromises);

          const allSecurities = securityResults.flatMap(
            watchlist => watchlist.data.securities,
          );

          const uniqueSecurities = Object.values(
            allSecurities.reduce((acc, security) => {
              acc[security.isin] = security;
              return acc;
            }, {}),
          );
          setWatchlistData(uniqueSecurities);

          const filteredWatchlist = filterTradingIdeasByIsin(
            uniqueSecurities,
            allTradingIdeas,
          );
          setFilteredWatchlistIdeas(filteredWatchlist);
        }
      } catch (error) {
        log('Error fetching watchlist:', error);
      } finally {
        setIsWatchlistLoading(false);
      }
    };

    fetchWatchlist();
  }, [tradingIdeas]);

  useEffect(() => {
    if (!allTradingIdeas?.length) return;
    const fetchHoldings = async () => {
      try {
        setIsHoldingsLoading(true);

        const date = new Date().toISOString().split('T')[0];
        const url = HOLDINGS_API(date);
        const headers = getGenericAppHeaders();

        const response = await makeApiGetCall({
          url,
          headers,
          axiosSource: props.axiosSource,
        });

        if (response && response.data) {
          setHoldingsData(response?.data?.data?.results);
          const filteredHoldings = filterTradingIdeasByIsin(
            response?.data?.data?.results,
            allTradingIdeas,
          );
          setFilteredHoldingsIdeas(filteredHoldings);
        }
      } catch (error) {
        log('Error fetching holdings:', error);
      } finally {
        setIsHoldingsLoading(false);
      }
    };

    fetchHoldings();
  }, [tradingIdeas]);

  useEffect(() => {
    if (!allTradingIdeas?.length) return;
    const fetchPositions = async () => {
      try {
        setIsPositionsLoading(true);

        const url = POSITIONS_API;
        const headers = getGenericAppHeaders();

        const response = await makeApiGetCall({
          url,
          headers,
          axiosSource: props.axiosSource,
        });

        if (response && response.data) {
          setPositionsData(response?.data?.data);
          const filteredPositions = filterTradingIdeasByIsin(
            response?.data?.data,
            allTradingIdeas,
          );
          setFilteredPositionsIdeas(filteredPositions);
        }
      } catch (error) {
        log('Error fetching positions:', error);
      } finally {
        setIsPositionsLoading(false);
      }
    };

    fetchPositions();
  }, [tradingIdeas]);

  useEffect(() => {
    if (!allTradingIdeas?.length) return;
    const fetchSearchedData = async () => {
      try {
        setIsSearchLoading(true);

        const url = SEARCHED_API;
        const headers = getGenericAppHeaders();

        const response = await makeApiGetCall({
          url,
          headers,
          axiosSource: props.axiosSource,
        });
        if (response && response.data) {
          setSearchData(response?.data?.data?.results);
          const filteredSearch = filterTradingIdeasByIsin(
            response?.data?.data?.results,
            allTradingIdeas,
          );
          setFilteredSearchIdeas(filteredSearch);
        }
      } catch (error) {
        log('Error fetching searched data:', error);
      } finally {
        setIsSearchLoading(false);
      }
    };

    fetchSearchedData();
  }, [tradingIdeas]);

  useEffect(() => {
    if (
      !Object.keys(filteredMarginSort).length &&
      Object.keys(tradingIdeas).length
    ) {
      setFilteredMarginSort(tradingIdeas);
      scrollToTop();
    }
  }, [tradingIdeas]);

  useEffect(() => {
    if (!isSavedFilters()) {
      if (activeQuickFilter === null || !tradingIdeasClone) return;
      if (activeQuickFilter === DEFAULT_QUICK_FILTERS) {
        setTradingIdeas(tradingIdeasClone);
        setFilteredMarginSort(tradingIdeasClone);
      } else if (activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]) {
        const futureOption = filteredIdeas('future_option');
        setTradingIdeas(futureOption);
        setFilteredMarginSort(futureOption);
      } else {
        const tag = filteredIdeas('category');
        setTradingIdeas(tag);
        setFilteredMarginSort(tag);
      }
    }
  }, [activeQuickFilter]);

  useEffect(() => {
    if (
      selectedSortOption !== DEFAULT_DERIVATIVES_SORT ||
      selectedSortOption !== DEFAULT_EQUITY_SORT
    ) {
      setSelectedSortOption(
        activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]
          ? DEFAULT_DERIVATIVES_SORT
          : DEFAULT_EQUITY_SORT,
      );
      setCurrentSortOption(
        activeTab === LOTUS_TRADING_LISTING_PAGE_TABS[0]
          ? DEFAULT_DERIVATIVES_SORT
          : DEFAULT_EQUITY_SORT,
      );
    }

    if (!Object.keys(tradingIdeas).length) return;
    if (!isSavedFilters())
      setTradingIdeas(ideas => filterTradingIdeas(ideas, false));
  }, [activeTab]);

  useEffect(() => {
    fetchAdvisorDefination();
  }, []);

  const value = useMemo(
    () => ({
      activeTab,
      setActiveTab,
      fetchTradingIdeas,
      tradingIdeas,
      tradingIdeasClone,
      setTradingIdeas,
      isMarginLoading,
      isTradingCardLoading,
      activeQuickFilter,
      setActiveQuickFilter,
      filteredMarginSort,
      selectedSortOption,
      setSelectedSortOption,
      defaultSortOption,
      marginData,
      advisorJSON,
      filterDerivativeValues,
      filterEquityValues,
      isEntryNull,
      isJamoonApiFailed,
      advisorDefination,
      currentSortOption,
      setCurrentSortOption,
      setFilteredMarginSort,
      watchlistData,
      holdingsData,
      positionsData,
      searchData,
      isWatchlistLoading,
      isHoldingsLoading,
      isPositionsLoading,
      isSearchLoading,
      personalizedTradingIdeas: {
        watchlist: filteredWatchlistIdeas,
        holdings: filteredHoldingsIdeas,
        positions: filteredPositionsIdeas,
        search: filteredSearchIdeas,
      },
      tiBadgeData,
      isBadgeLoading,
    }),
    [
      activeTab,
      setActiveTab,
      fetchTradingIdeas,
      tradingIdeas,
      setTradingIdeas,
      isMarginLoading,
      isTradingCardLoading,
      tradingIdeasClone,
      activeQuickFilter,
      setActiveQuickFilter,
      filteredMarginSort,
      selectedSortOption,
      setSelectedSortOption,
      defaultSortOption,
      marginData,
      advisorJSON,
      filterDerivativeValues,
      filterEquityValues,
      isEntryNull,
      isJamoonApiFailed,
      advisorDefination,
      currentSortOption,
      setCurrentSortOption,
      setFilteredMarginSort,
      watchlistData,
      holdingsData,
      positionsData,
      isWatchlistLoading,
      isHoldingsLoading,
      isPositionsLoading,
      isSearchLoading,
      filteredWatchlistIdeas,
      filteredHoldingsIdeas,
      filteredPositionsIdeas,
      tiBadgeData,
    ],
  );

  return (
    <PersonalizedTradingIdeasContext.Provider value={value}>
      {children}
    </PersonalizedTradingIdeasContext.Provider>
  );
};

export const usePersonalizedTradingIdeasContext = () =>
  useContext(PersonalizedTradingIdeasContext);

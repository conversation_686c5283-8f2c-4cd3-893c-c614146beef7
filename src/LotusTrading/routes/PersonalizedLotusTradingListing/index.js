import React from 'react';

import Layout from '../../../components/Layout/Layout';

import EquityContext from '../../../pages/Stocks';
import PersonalizedTradingIdeasListingPage from '../../pages/PersonalizedTradingIdeasListingPage';

import { TRADING_IDEAS_SEO } from '../../constants';

import { TradingIdeasProvider } from '../../context/TradingIdeasContext';
import { PersonalizedTradingIdeasProvider } from '../../context/PersonalizedTradingIdeasContext';

function LotusTradingListing(props) {
  const seo = {
    title: TRADING_IDEAS_SEO.TITLE,
    description: TRADING_IDEAS_SEO.DESC,
  };
  return {
    home: true,
    seo,
    component: (
      <Layout>
        <EquityContext {...props}>
          <TradingIdeasProvider {...props}>
            <PersonalizedTradingIdeasProvider {...props}>
              <PersonalizedTradingIdeasListingPage />
            </PersonalizedTradingIdeasProvider>
          </TradingIdeasProvider>
        </EquityContext>
      </Layout>
    ),
  };
}

export default LotusTradingListing;

import { getMargin } from '../query/lotusTradingQuery';
import { getInstrumentType } from '../utils/utilFunctions';
import { log } from '../../utils/commonUtils';

const getDerivativesMarginObject = (idea, strikePrice) => ({
  segment: 'D',
  exchange: 'NSE',
  security_id: idea.script_id,
  txn_type: idea.calltype?.toLowerCase() === 'sell' ? 'S' : 'B',
  quantity: idea.lot_size,
  product: 'M',
  instrument: idea.instrument,
  strike_price: strikePrice,
  trigger_price: 0,
  price: idea.entryprice1,
});

export const getMarginData = async ideasList => {
  let strikePrice = 0;
  const marginListParam = [];

  for (const i in ideasList) {
    const idea = ideasList[i];
    if (idea.future_option === 'Option') {
      if (idea.calltype?.toLowerCase() === 'sell') {
        strikePrice = idea.strikeprice;
        marginListParam.push(getDerivativesMarginObject(idea, strikePrice));
      }
    } else {
      marginListParam.push(getDerivativesMarginObject(idea, strikePrice));
    }
  }
  // get margin
  try {
    const data = await getMargin({
      source: 'W',
      margin_list: marginListParam,
    });
    if (data?.status === 200) {
      const { marginList } = data.data.data;
      const marginData = marginList.reduce((marginDataObj, margin) => {
        /* eslint no-param-reassign: "error" */
        marginDataObj[margin.security_id] = margin.total_margin;
        return marginDataObj;
      }, {});
      return marginData;
    }
  } catch (err) {
    log(err);
  }
  return {};
};

export const getMuhuratMarginData = async tradingIdeas => {
  const ideasList = { ...tradingIdeas };
  let strikePrice = 0;
  let marginListParam = [];
  /* eslint-disable no-unused-expressions */
  ideasList?.derivatives.items.forEach(options => {
    const tempArr = options.items?.map(idea => {
      if (idea.future_option === 'Option') {
        if (idea.calltype?.toLowerCase() === 'sell') {
          strikePrice = idea.strike_price;
        } else {
          return false;
        }
      }
      return {
        segment: 'D',
        exchange: 'NSE',
        security_id: idea.script_id,
        txn_type: idea.calltype?.toLowerCase() === 'sell' ? 'S' : 'B',
        quantity: idea.lot_size,
        product: 'M',
        instrument: getInstrumentType(idea.future_option, idea.stock_index),
        strike_price: strikePrice,
        trigger_price: 0,
        price: idea.price_when_created,
      };
    });
    marginListParam = [...marginListParam, ...tempArr];
  });

  marginListParam = marginListParam.filter(marginParam => marginParam);
  // get margin
  const data = await getMargin({
    source: 'W',
    margin_list: marginListParam,
  });
  if (data?.status === 200) {
    const { marginList } = data.data.data;
    const marginData = marginList.reduce((marginDataObj, margin) => {
      /* eslint no-param-reassign: "error" */
      marginDataObj[margin.security_id] = margin.total_margin;
      return marginDataObj;
    }, {});
    return marginData;
  }
  return {};
};

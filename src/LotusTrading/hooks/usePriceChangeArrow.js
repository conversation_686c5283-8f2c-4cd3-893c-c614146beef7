import React, { useCallback, useEffect } from 'react';
import { roundValue, throttle } from '../../utils/commonUtils';
import { calculateArrowPosition } from '../config/tradingIdeasUtils';
import {
  POLYGON_LEFT_ICON,
  POLYGON_RIGHT_ICON,
} from '../config/lotusTradingConfig';

function usePriceChangeArrow({ priceChange, stopLoss, targetPrice, isBuy }) {
  const arrowRef = React.useRef();
  const layoutPercent = Number(
    roundValue(
      calculateArrowPosition(priceChange, stopLoss, targetPrice, isBuy),
    ),
  );

  const renderArrow = useCallback(
    polygon => {
      const icon = layoutPercent > 50 ? POLYGON_RIGHT_ICON : POLYGON_LEFT_ICON;

      return <img ref={arrowRef} src={icon} alt="" className={polygon} />;
    },
    [priceChange],
  );
  useEffect(() => {
    throttle(() => {
      if (arrowRef.current) {
        arrowRef.current.style.marginLeft =
          Number(priceChange) > 0
            ? `calc(${layoutPercent}% - ${layoutPercent > 50 ? '30px' : '0px'})`
            : 0;
      }
    }, 1000)();
  }, [priceChange]);

  return { layoutPercent, renderArrow };
}

export default usePriceChangeArrow;

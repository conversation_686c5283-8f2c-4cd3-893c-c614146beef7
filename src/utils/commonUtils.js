/* eslint-disable radix */
import React from 'react';
import get from 'lodash/get';
import queryString from 'query-string';
import { RUPEE_SYMBOL, LOCAL_STORAGE_KEYS } from './Constants';
import DeviceInfoProvider from './Providers/DeviceInfoProvider';
import {
  getH5NativeDeepLinkData,
  resetH5NativeDeepLinkData,
  isPhoenixContainer,
  navigateTo,
} from '../services/coreUtil';
import history from '../history';
import { getBridge } from './bridgeUtils';
import { BUYBACK_ROUTES } from '../buyback/config/urlConfig';
import URL from '../routes/config/urlConfig';
import { SIP_CALCULATOR_ROUTES } from '../SIPCalculator/config/urlConfig';
import { sendErrorToBackend } from '../actions/runtime';

export const processQuarryPrams = searchString => {
  const data = searchString.substring(1);
  const array = data.split('&');
  return array.reduce((oldData, currentData) => {
    const split = currentData.split('=');
    // eslint-disable-next-line no-param-reassign
    oldData[split[0]] = split[1];
    return oldData;
  }, {});
};

// eslint-disable-next-line no-console
export const log = () => {}; // console.log.bind(console);
//  console.log.bind(console);

export const errorLog = () => {}; // console.error.bind(console);

export const getOrigin = () => DeviceInfoProvider.getInfo('origin') || 'PAYTM';

/**
 * @TODO remove css origin hardcoding later
 */
export const getPaytmMoneyCss = () => 'PAYTMMONEY';
// getOrigin() === 'PAYTMMONEY' ? 'is_paytm_money' : '';

export const isDarkMode = () => {
  const pathname = history.location?.pathname.split('/')[1];
  return `/${pathname}` === URL.TAX_HARVESTING ||
    `/${pathname}` === SIP_CALCULATOR_ROUTES.SIP_CALCULATOR ||
    `/${pathname}` === URL.FNO_TNC ||
    `/${pathname}` === URL.SIP_RECOMMENDATIONS ||
    `/${pathname}` === URL.MTF ||
    `/${pathname}` === URL.LOTUS_TRADING ||
    `/${pathname}` === URL.COMPANY_BREAKOUT ||
    `/${pathname}` === URL.DEMAT_HOLDINGS
    ? DeviceInfoProvider.getInfo('darkmode') === 'true'
    : false;
};

export const isPaytmMoney = () => getOrigin() === 'PAYTMMONEY';

export const isPaytm = () => getOrigin() === 'PAYTM';

export const isIBL = () => getOrigin() === 'IBL';

const isCDSLRedirect = () =>
  history.location?.pathname === BUYBACK_ROUTES.APPLY;

export const isHideSplash = () =>
  isPaytm() || isPaytmMoney() || isCDSLRedirect();

export const isNull = value => value === null;

export const isValidNumber = value => {
  if (!value) return false;
  return !(Number.isNaN(value) || value === Infinity);
};

export const roundValue = (value, decimals = 2) => {
  if (!isValidNumber(value)) {
    return '0.00';
  }
  const base = 10 ** decimals;
  return (Math.round(value * base) / base).toFixed(decimals);
};

export const formatNumber = input => {
  // let fontWeightLighter = {fontWeight: '300'};
  const fontWeightLighter = {};
  const num = roundValue(input);
  let n1;
  n1 = num.split('.');
  const n2 = n1[1] || null; // contains digits after decimal
  n1 = n1 && n1[0].replace(/(\d)(?=(\d\d)+\d$)/g, '$1,'); // gives digits before decimal(comma separated)

  return n2 ? (
    <>
      <span>
        {RUPEE_SYMBOL}
        {n1}.
      </span>
      <span style={fontWeightLighter}>{n2}</span>
    </>
  ) : n1 ? (
    <>
      <span>
        {RUPEE_SYMBOL}
        {n1}.
      </span>
      <span style={fontWeightLighter}>00</span>
    </>
  ) : (
    <span>--</span>
  );
};

export const getCookieValue = name => {
  const b = document.cookie.match(`(^|[^;]+)\\s*${name}\\s*=\\s*([^;]+)`);
  return b ? decodeURIComponent(b.pop()) : '';
};

export const formatNumberWithoutHtml = input => {
  const num = roundValue(input);
  let number;
  number = num.split('.');
  const decimal = number[1] || null; // contains digits after decimal
  number = number && number[0].replace(/(\d)(?=(\d\d)+\d$)/g, '$1,');

  if (decimal === '00') {
    return number;
  }
  return `${number}.${decimal}`;
};

export const getOrdinalNum = n =>
  n +
  (n > 0
    ? ['th', 'st', 'nd', 'rd'][(n > 3 && n < 21) || n % 10 > 3 ? 0 : n % 10]
    : '');

export const extractVersionNumber = versionString => {
  if (!versionString) {
    return 1;
  }
  const modifiedString = versionString.split('.').join('');
  if (modifiedString) {
    const versionInInt = parseInt(modifiedString);
    return versionInInt;
  }
  return 1;
};

export const isAppVersionGreaterThan940 = appVersionName => {
  const version = extractVersionNumber(appVersionName);
  if (version >= 940) {
    return true;
  }
  return false;
};

export const isAppVersionGreaterThan = (appVersionName, targetVersion) => {
  // returns true if appVersionNumber is greater than targetVersion
  const version = extractVersionNumber(appVersionName);
  if (version >= targetVersion) {
    return true;
  }
  return false;
};

export const isNewIOSBuild = appVersionName => {
  const version = extractVersionNumber(appVersionName);
  return version >= 8104;
};

export const handleKeyboardClose = (windowInitialHeight, elementId) => {
  const element = document.getElementById(elementId);
  if (element) {
    if (window.innerHeight === windowInitialHeight) {
      element.style.display = 'block';
    } else if (window.innerHeight < windowInitialHeight) {
      element.style.display = 'none';
    }
  }
};

export const isIosBuild = () =>
  DeviceInfoProvider.getInfo('device_type') === 'ios' ||
  DeviceInfoProvider.getInfo('device_type') === 'iosapp';

export const isAndroidBuild = () =>
  DeviceInfoProvider.getInfo('device_type') === 'android';

export const isNativePaymentAvailable = () => {
  const currentBundleVersion = DeviceInfoProvider.getInfo('ver')
    ? parseInt(DeviceInfoProvider.getInfo('ver'))
    : 0;

  return (
    isAndroidBuild() &&
    currentBundleVersion >= 3 &&
    DeviceInfoProvider.getInfo('origin') === 'PAYTM'
  );
};

export const NativeBackPress = {
  subscribe: callBackFn => {
    const bridgeName = getBridge();
    if (isPhoenixContainer()) {
      if (typeof bridgeName !== 'undefined') {
        bridgeName.subscribe('back', callBackFn);
      }
    } else {
      document.addEventListener('back', callBackFn, false);
    }
  },
  unSubscribe: callBackFn => {
    const bridgeName = getBridge();
    if (isPhoenixContainer()) {
      if (typeof bridgeName !== 'undefined') {
        bridgeName.unsubscribe('back', callBackFn);
      }
    } else {
      document.removeEventListener('back', callBackFn, false);
    }
  },
};

export const useCalculateDimensions = (
  aspectRatio = {
    width: 16,
    height: 9,
  },
) => {
  const { height: heightR, width: widthR } = aspectRatio;
  const { innerWidth, innerHeight } = window;
  let width = innerWidth;
  let height = parseInt(((width / widthR) * heightR).toFixed(2), 10);
  if (height > innerHeight) {
    height = innerHeight;
    width = parseInt(((height / widthR) * heightR).toFixed(2), 10);
  }
  if (width > innerWidth) {
    width = innerWidth;
  }
  return {
    width,
    height,
  };
};

export const formatSecToYodaTime = (seconds = 1) => {
  const isoString = new Date(seconds * 1000).toISOString();
  if (seconds >= 3600) {
    return isoString.substr(11, 8);
  }
  return isoString.substr(14, 5);
};

export const checkDeepLink = () => {
  const deepLinkUrl = getH5NativeDeepLinkData();
  const HOST = 'https://www.paytmmoney.com/stocks/ipo/';
  log('deepLinkUrl', deepLinkUrl);
  if (deepLinkUrl && deepLinkUrl.startsWith(HOST)) {
    resetH5NativeDeepLinkData();
    log(`Url starts with ${HOST}`);
    const trimmedDeepLinkUrl = deepLinkUrl.replace(HOST, '');
    log('trimmedDeepLinkUrl', trimmedDeepLinkUrl);
    const trimmedDeepLinkUrlArray = trimmedDeepLinkUrl.split('/');
    log('trimmedDeepLinkUrlArray', trimmedDeepLinkUrlArray);
    switch (trimmedDeepLinkUrlArray[0]) {
      case 'orders':
        // navigateTo(history, IPO_ROUTES.ORDERS, {}, 'replace');
        break;
      case 'details':
        navigateTo(
          history,
          // IPO_ROUTES.DETAILS,
          { id: trimmedDeepLinkUrlArray[1] },
          'push',
        );
        break;
      default:
        break;
    }
  }
};

export const isShareScreenshotBridgeAvailable = () =>
  getOrigin() === 'PAYTM' &&
  isAndroidBuild() &&
  isAppVersionGreaterThan(DeviceInfoProvider.getInfo('appVersionName'), 9110);

export const generateUniqSerial = number =>
  'xxxx-xxxx-xxx-xxxx'.replace(/[x]/g, () => {
    const r = Math.floor(Math.random() * 16);
    return r.toString(number);
  });

export function generateUniqueKey() {
  return Math.random()
    .toString(16)
    .slice(2);
}

export const isCurrentTimeStampBtwStartAndEnd = (
  currentTimeStamp,
  startTimeStamp,
  endTimeStamp,
) => {
  if (startTimeStamp === null && endTimeStamp === null) {
    return false;
  } else if (startTimeStamp === null && endTimeStamp !== null) {
    return endTimeStamp >= currentTimeStamp;
  } else if (endTimeStamp === null && startTimeStamp !== null) {
    return startTimeStamp <= currentTimeStamp;
  }
  return endTimeStamp >= currentTimeStamp && currentTimeStamp >= startTimeStamp;
};

export const convertGainFormat = lg => {
  const stringGain = `${Math.abs(lg) * 100}`.split('.');
  let res = '';
  if (stringGain[1]) {
    res = (Math.abs(lg) * 100).toFixed(2);
  } else {
    res = Math.abs(lg) * 100;
  }
  return `${lg > 0 ? '+' : '-'}${res}%`;
};

export const generateQueryParamsString = (obj = {}) =>
  `?${new URLSearchParams(obj).toString()}`;

export const getQueryParams = () => {
  const urlSearchParams = new URLSearchParams(window.location.search);
  return Object.fromEntries(urlSearchParams.entries());
};

export const getQueryParamsFromString = str => {
  const urlSearchParams = new URLSearchParams(str);
  return Object.fromEntries(urlSearchParams.entries());
};

export const AppEvents = {
  subscribe: callBackFn => {
    const bridgeName = getBridge();
    if (isPhoenixContainer()) {
      if (typeof bridgeName !== 'undefined') {
        bridgeName.subscribe('resume', callBackFn);
        bridgeName.subscribe('pause', callBackFn);
      }
    }
  },
  unSubscribe: callBackFn => {
    const bridgeName = getBridge();
    if (isPhoenixContainer()) {
      if (typeof bridgeName !== 'undefined') {
        bridgeName.subscribe('resume', callBackFn);
        bridgeName.subscribe('pause', callBackFn);
      }
    }
  },
};

export function getAbsoluteValue(value, decimals = 2, getAbsolute = true) {
  if (!isValidNumber(value)) {
    return '0.00';
  }
  const roundedValue = roundValue(value, decimals);
  if (!getAbsolute) {
    return roundedValue;
  }
  return roundedValue >= 0
    ? roundedValue
    : roundValue(-1 * roundedValue, decimals);
}

export const openWindowWithPost = ({ url, params, target = '_self' }) => {
  const width = 1000;
  const height = 700;
  const form = document.createElement('form');
  form.setAttribute('method', 'post');
  form.setAttribute('action', url);

  Object.keys(params).forEach(name => {
    const input = document.createElement('input');
    input.setAttribute('type', 'hidden');
    input.setAttribute('name', name);
    input.setAttribute('value', params[name]);
    form.appendChild(input);
  });

  if (target === '_blank') {
    const left = window.screen.width / 2 - width / 2;
    const top = window.screen.height / 2 - height / 2;
    const w = window.open(
      '',
      '_blank',
      `top=${top},left=${left},width=${width},height=${height}`,
    );
    w.document.body.appendChild(form);
    form.submit();
    return w;
  }
  document.body.appendChild(form);
  form.submit();
};

export const getDeeplinkData = key => {
  const query = getH5NativeDeepLinkData();
  return get(queryString.parse(query), key, '');
};

export const getDeeplinkDataOrQueryParam = key =>
  getDeeplinkData(key) || getQueryParams()[key];

export const convertDate = date =>
  `${`0${date.getDate()}`.slice(-2)}-${`0${date.getMonth() + 1}`.slice(
    -2,
  )}-${date.getFullYear()}`;

export const downloadCSV = (rows, name) => {
  let csvContent = 'data:text/csv;charset=utf-8,';

  rows.forEach(rowArray => {
    const row = rowArray.join(',');
    csvContent += `${row}\r\n`;
  });

  const encodedUri = encodeURI(csvContent);
  const link = document.createElement('a');
  link.setAttribute('href', encodedUri);
  link.setAttribute('download', name);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

export const getParamsValue = () => {
  if (isPaytmMoney()) {
    const deepLinkUrl = getH5NativeDeepLinkData();
    if (
      deepLinkUrl &&
      (deepLinkUrl.includes('paytmmoney:///mini-app') ||
        deepLinkUrl.includes('https://paytmmoney.com/mini-app'))
    ) {
      const data = queryString.parse(deepLinkUrl);

      const {
        merchant = null,
        refreshToken = null,
        merchantDeeplink = null,
      } = data;
      return {
        merchant,
        refreshToken,
        merchantDeeplink,
      };
    }
  } else if ('URLSearchParams' in window) {
    const searchParams = new URLSearchParams(window.location.search);
    return {
      merchant: searchParams.get('merchant'),
      refreshToken: searchParams.get('refreshToken'),
      merchantDeeplink: searchParams.get('merchantDeeplink'),
    };
  }
  return {
    merchant: null,
    refreshToken: null,
    merchantDeeplink: null,
  };
};

export const API_STATUS = {
  PAGE_EXPIRED: 419,
};

export const sendDataToBackend = (error, urlAlias) => {
  let errObj = {};
  if (error.response) {
    // Axios API Error Response
    errObj = {
      status: error.response?.status,
      data: error.response?.data,
      url: urlAlias || error.response?.config?.url,
      method: error.response?.config?.method,
    };
  } else if (error.message || error.config) {
    // Network Error Case
    errObj = {
      message: error.message,
      url: urlAlias || error.config?.url,
      timeout: error.config?.timeout,
      method: error.config?.method,
    };
  }
  sendErrorToBackend({
    data: JSON.stringify(errObj),
  });
};

export function throttle(mainFunction, delay) {
  let timerFlag = null; // Variable to keep track of the timer

  // Returning a throttled version
  return (...args) => {
    if (timerFlag === null) {
      // If there is no timer currently running
      mainFunction(...args); // Execute the main function
      timerFlag = setTimeout(() => {
        // Set a timer to clear the timerFlag after the specified delay
        timerFlag = null; // Clear the timerFlag to allow the main function to be executed again
      }, delay);
    }
  };
}

export const determineAccessForUser = data => {
  const { percentage, hasAccess, enabled } = data;
  const random = Math.random();
  if (enabled) {
    if (hasAccess) {
      return true;
    }
    if (random < percentage / 100) {
      localStorage.setItem(LOCAL_STORAGE_KEYS.TRADETRON_PML_COHORT, true);
      return true;
    }
    return false;
  }
  return false;
};

export const formatedTimestamp = () => {
  const d = new Date();
  const date = d.toISOString().split('T')[0];
  const time = d.toTimeString().split(' ')[0];
  return `${date} ${time}`;
};

export const setLocalstorage = (
  key,
  value,
  expiry = 24 * 60 * 60,
  setExpire = true,
) => {
  if (!(window && window.localStorage)) return;
  const finalObj = {
    value,
    createdAt: new Date().getTime(),
  };

  if (setExpire) {
    finalObj.expiry = expiry * 1000;
  }

  window.localStorage.setItem(key, JSON.stringify(finalObj));
};

export const deleteItem = key => {
  if (!(window && window.localStorage)) return null;
  return window.localStorage.removeItem(key);
};

export const getLocalstorage = (key, checkExpire = true) => {
  if (!(window && window.localStorage)) return null;
  const stringifiedObj = window.localStorage.getItem(key);
  if (!stringifiedObj) return null;
  const finalObj = JSON.parse(stringifiedObj);
  if (!checkExpire) {
    return finalObj.value;
  }
  if (finalObj.createdAt + finalObj.expiry > new Date().getTime()) {
    return finalObj.value;
  }
  deleteItem(key);
  return null;
};

export function sanitizeUrl(url) {
  const firstQuestionMarkIndex = url.indexOf('?');
  if (firstQuestionMarkIndex === -1) return url;

  const beforeFirstQuestionMark = url.substring(0, firstQuestionMarkIndex + 1);
  const afterFirstQuestionMark = url.substring(firstQuestionMarkIndex + 1);

  return beforeFirstQuestionMark + afterFirstQuestionMark.replace(/\?/g, '&');
}

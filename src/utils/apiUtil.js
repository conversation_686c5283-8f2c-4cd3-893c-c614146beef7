import axios from 'axios';
import {
  getAppVersion,
  parseDeviceToken,
  getDeviceID,
  getDeviceName,
  getDeviceType,
  getNormalSSOToken,
  getOsVersion,
  getUserId,
  getX2FAToken,
  isH5Container,
} from '../services/coreUtil';
import { isH5 } from './bridgeUtils';
import { getOrigin, isIBL, log } from './commonUtils';

const { CancelToken } = axios;
const source = CancelToken.source();

export const getPlatformValue = () => {
  const deviceType = getDeviceType();
  if (isH5()) {
    if (getOrigin() === 'PAYTM') {
      return `paytm-${deviceType}`;
    } else if (isIBL()) {
      return `ibl-${deviceType}`;
    }
  }
  return deviceType;
};

export const getUserAgent = () => {
  if (isH5()) {
    return {
      platform: getPlatformValue(),
      app_version: getAppVersion(),
      model: getDeviceName(),
      os_version: getOsVersion(),
      user_id: getUserId(),
      source: getOrigin(),
      h5: isH5Container(),
      device_id: parseDeviceToken(),
    };
  }

  return {
    user_id: getUserId(),
    platform: getPlatformValue(),
    h5: true,
    device_id: getDeviceID(),
  };
};

//    device_id: getDeviceID(), for now removed...

export const getGenericAppHeaders = () => {
  log(
    `SSO TOKEN From Generic APP Header Value:::${JSON.stringify(
      getNormalSSOToken(),
    )}`,
  );
  const defaultHeaders = {
    'Content-Type': 'application/json; charset=utf-8',
    'x-user-agent': JSON.stringify(getUserAgent()),
    'x-sso-token': getNormalSSOToken(),
    'x-pmngx-key': 'paytmmoney',
  };

  const twoFAToken = getX2FAToken();
  if (twoFAToken) {
    defaultHeaders['x-2fa-token'] =
      'txk+VW2Sf/cyqh6Upl/AZOK89RqBMxvoaYy71ko6IXE=';
  }

  log(
    `getGenericAppHeaders getGenericAppHeaders:: Value:::: ${JSON.stringify(
      defaultHeaders,
    )}`,
  );

  return defaultHeaders;
};

export const makeApiGetCall = async ({
  url,
  headers = {},
  axiosSource = source,
  queryParams = {},
}) => {
  const response = await axios.get(url, {
    cancelToken: axiosSource.token,
    headers,
    params: queryParams,
  });
  return response;
};
export const makeApiPostCall = async ({
  url,
  body = {},
  headers = {},
  queryParams = {},
  axiosSource = source,
}) => {
  const response = await axios.post(url, body, {
    cancelToken: axiosSource.token,
    headers,
    params: queryParams,
  });
  return response;
};
export const makeApiPutCall = async ({
  url,
  body = {},
  headers = {},
  queryParams = {},
  axiosSource = source,
}) => {
  const response = await axios.put(url, body, {
    cancelToken: axiosSource.token,
    headers,
    params: queryParams,
  });
  return response;
};

export const getStoreFrontHeaders = () => {
  const defaultHeaders = {
    'Content-Type': 'application/json; charset=utf-8',
    sso_token: getNormalSSOToken(),
  };
  return defaultHeaders;
};

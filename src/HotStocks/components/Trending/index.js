import React, { useEffect, useState } from 'react';
import { getTrendingStocks } from '../../../actions/hotStocksActions';
import InfiniteScroll from '../../../components/InfiniteScroll/InfiniteScroll';
import { NO_DATA } from '../../utils/enums';
import ListItem from '../ListItem';
import ShimmerLoading from '../ListItem/_partials/ShimmerLoading';
import NoData from '../NoData';
import styles from './index.scss';

function Trending({ activeTab, position, ...props }) {
  const scrollRef = React.createRef();
  const [pageData, setPageData] = useState({
    pg_num: 1,
    pg_size: 25,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const [isPaginationLoading, setIsPaginationLoading] = useState(false);

  const [data, setData] = useState([]);
  const getData = async () => {
    if (!isPaginationLoading && hasMore) {
      setIsPaginationLoading(true);
      const body = {
        index: [],
        industry: [],
        instrument_type: 'ES',
        mcap_type: [],
        sector: [],
        ...pageData,
      };
      const response = await getTrendingStocks(props.axiosSource, body);
      setIsPaginationLoading(false);
      if (isLoading) setIsLoading(false);
      if (response) {
        setData(prev => [...prev, ...response.data.results]);
        setPageData(prev => ({
          ...prev,
          pg_num:
            prev.pg_num <= response.data.pg_cxt.tl_count
              ? prev.pg_num + 1
              : prev.pg_num,
        }));
        setHasMore(
          pageData.pg_num * response.data.pg_cxt.pg_size <
            response.data.pg_cxt.tl_count,
        );
      }
    }
  };

  useEffect(() => {
    if (activeTab === position) {
      getData();
    }
  }, [activeTab]);

  if (isLoading) {
    return (
      <div className={styles.container}>
        {Array.from({ length: 5 }).map((_, i) => (
          <ShimmerLoading key={i} />
        ))}
      </div>
    );
  }

  if (!data.length) {
    return (
      <NoData
        title={NO_DATA.TRENDING.TITLE}
        description={NO_DATA.TRENDING.DESC}
      />
    );
  }

  const paginationLoader = () => <ShimmerLoading />;
  return (
    <div className={styles.container} ref={scrollRef}>
      <InfiniteScroll
        pageStart={0}
        initialLoad={false}
        loadMore={getData}
        hasMore={hasMore}
        loader={paginationLoader()}
        ref={() => scrollRef.current}
        useWindow={false}
      >
        {data.map(item => (
          <ListItem key={item.security_id} {...item} symbol={item.name} />
        ))}
      </InfiniteScroll>
    </div>
  );
}

export default Trending;

.container {
  padding: 20px 0;

  gap: 12px;
  display: flex;
  justify-content: space-between;

  border-bottom: 1px solid map-get($colors, OffsetSecondary);

  .stockNameContainer {
    flex: 1;

    .stockName {
      @include typography(heading2B3, map-get($colors, PrimaryColor), false, true);
      font-weight: 400;
      line-height: 20px;
      word-wrap: break-word;
    }

    .exchange {
      @include typography(text1R, map-get($colors, GreyPrimary), false, true);
      font-weight: 400;
      line-height: 20px;
      margin-top: 4px;
    }
  }

  .companyLogo {
    width: 40px;
    height: 40px;

    margin-right: 0;
    span {
      width: inherit;
      height: inherit;
      margin-right: 0;
    }
  }

  .priceContainer {
    display: flex;
    flex-direction: column;
    text-align: right;
    gap: 4px;
  }
}

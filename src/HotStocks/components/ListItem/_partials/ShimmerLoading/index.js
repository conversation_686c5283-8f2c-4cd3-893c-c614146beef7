import React from 'react';

import classNames from 'classnames';
import styles from './index.scss';

function ShimmerLoading() {
  return (
    <div className={styles.container}>
      <div
        className={classNames(styles.companyLogo, styles.shimmerAnimation)}
      />
      <div className={styles.stockNameContainer}>
        <div
          className={classNames(styles.stockName, styles.shimmerAnimation)}
        />
        <div className={classNames(styles.exchange, styles.shimmerAnimation)} />
      </div>
      <div className={styles.priceContainer}>
        <div className={classNames(styles.price, styles.shimmerAnimation)} />
      </div>
    </div>
  );
}

export default ShimmerLoading;

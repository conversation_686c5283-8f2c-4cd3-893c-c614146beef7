import React, { useEffect, useState } from 'react';
import { getRecentSearch } from '../../../actions/hotStocksActions';
import ListItem from '../ListItem';
import ShimmerLoading from '../ListItem/_partials/ShimmerLoading';

import { NO_DATA } from '../../utils/enums';
import NoData from '../NoData';
import styles from './index.scss';

function RecentSearch({ activeTab, position, ...props }) {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const getData = async () => {
    const response = await getRecentSearch(props.axiosSource);
    setIsLoading(false);
    if (response) {
      setData(response.data.results);
    }
  };

  useEffect(() => {
    if (activeTab === position) {
      getData();
    }
  }, [activeTab]);

  if (isLoading) {
    return (
      <div className={styles.container}>
        {Array.from({ length: 5 }).map((_, i) => (
          <ShimmerLoading key={i} />
        ))}
      </div>
    );
  }

  if (!data.length) {
    return (
      <NoData
        title={NO_DATA.RECENT_SEARCH.TITLE}
        description={NO_DATA.RECENT_SEARCH.DESC}
      />
    );
  }

  return (
    <div className={styles.container}>
      {data.map(item => (
        <ListItem key={item.security_id} {...item} symbol={item.name} />
      ))}
    </div>
  );
}

export default RecentSearch;

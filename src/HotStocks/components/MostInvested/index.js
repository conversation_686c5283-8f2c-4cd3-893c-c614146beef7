import React, { useEffect, useState } from 'react';

import { getMostInvested } from '../../../actions/hotStocksActions';
import ListItem from '../ListItem';
import ShimmerLoading from '../ListItem/_partials/ShimmerLoading';

import { NO_DATA } from '../../utils/enums';
import NoData from '../NoData';
import styles from './index.scss';

function MostInvested({ activeTab, position, ...props }) {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const getData = async () => {
    const response = await getMostInvested(props.axiosSource);
    setIsLoading(false);

    if (response && response.data?.length) {
      setData(response.data);
    }
  };

  useEffect(() => {
    if (activeTab === position) {
      getData();
    }
  }, [activeTab]);

  if (isLoading) {
    return (
      <div className={styles.container}>
        {Array.from({ length: 5 }).map((_, i) => (
          <ShimmerLoading key={i} />
        ))}
      </div>
    );
  }

  if (!data.length) {
    return (
      <NoData
        title={NO_DATA.MOST_INVESTED.TITLE}
        description={NO_DATA.MOST_INVESTED.DESC}
      />
    );
  }

  return (
    <div className={styles.container}>
      {data.map((item, index) => (
        <ListItem key={item.pml_id + index} id={item.pml_id} {...item} />
      ))}
    </div>
  );
}

export default MostInvested;

.container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  min-height: 60vh;


  .title {
    margin-top: 24px;
    @include typography(heading1B2, map-get($colors, PrimaryColor), false, true);
  }

  img {
    width: 100%;
  }

  .description {
    @include typography(heading2B3, map-get($colors, GreySecondary), false, true);
    font-weight: 400;
    margin-top: 4px;
    text-align: center;
  }
}

import { isPaytmMoney } from '../../utils/commonUtils';

export const HOT_STOCKS_HEADING = 'Top Stocks';

export const HOT_STOCKS_SEO = {
  HOT_STOCKS: {
    TITLE: 'Hot Stocks',
    DESC: 'Hot Stocks',
  },
};

export const HOT_STOCKS_TABS = [
  {
    id: 'trending',
    name: 'Trending',
  },
  {
    id: 'mostInvested',
    name: 'Most Invested',
  },
  {
    id: 'RecentSearch',
    name: 'Recent Search',
  },
];

export const FOOTER = {
  PRIMARY_CTA: {
    TEXT: 'Add Funds',
    deeplink: '',
  },
  SECONDARY_CTA: {
    TEXT: 'Go to portfolio',
    deeplink: '',
  },
};

export const NO_DATA = {
  TRENDING: {
    TITLE: 'No data found',
    DESC: 'Currently, there are no Trending stocks to display.',
  },
  MOST_INVESTED: {
    TITLE: 'No data found',
    DESC: 'Currently there are no Most Invested stocks to display.',
  },
  RECENT_SEARCH: {
    TITLE: 'No data found',
    DESC: 'Currently there are no Recent Search to display.',
  },
};

export const DEEPLINKS = {
  ADD_FUNDS: isPaytmMoney()
    ? 'https://www.paytmmoney.com/stocks/funds'
    : 'paytmmp://paytmmoney/stocks/funds',
  PORTFOLIO: isPaytmMoney()
    ? 'https://www.paytmmoney.com/stocks/portfolio'
    : 'paytmmp://paytmmoney/stocks/portfolio',
  COMPANY_PAGE_PAYTM: id => `paytmmp://paytmmoney/stocks/company/${id}/dl`,
  COMPANY_PAGE: id => `https://www.paytmmoney.com/stocks/company/${id}`,
};

import React, { useEffect } from 'react';
import SwipeableTabs from '../../../components/SwipeableTabs';
import { HOT_STOCKS_HEADING, HOT_STOCKS_TABS } from '../../utils/enums';

import history from '../../../history';
import baseComponent from '../../../HOC/BaseComponent/BaseComponent';
import HeaderLayout from '../../../layout/HeaderLayout/HeaderLayout';
import { goBack } from '../../../services/coreUtil';
import { getQueryParams } from '../../../utils/commonUtils';
import { useBackPress } from '../../../utils/react';
import MostInvested from '../../components/MostInvested';
import RecentSearch from '../../components/RecentSearch';
import Trending from '../../components/Trending';
import Footer from './_partials/Footer';

import styles from './styles.scss';

function HotStocks(props) {
  const { pushStack, clearStack } = useBackPress();

  const [activeTab, setActiveTab] = React.useState(0);

  const params = getQueryParams();

  useEffect(() => {
    if (params && params.tab) {
      const position = HOT_STOCKS_TABS.findIndex(tab => tab.id === params.tab);
      setActiveTab(position > 0 ? position : 0);
    } else {
      setActiveTab(0);
    }
  }, []);

  const handleBackPress = () => {
    goBack(history);
  };

  useEffect(() => {
    pushStack(handleBackPress);
    return () => {
      clearStack();
    };
  }, []);

  const headerProps = {
    heading: HOT_STOCKS_HEADING,
    disableShadow: true,
    showHelp: false,
    onClickHandler: handleBackPress,
    customClass: styles.headerCustomClass,
  };

  return (
    <HeaderLayout {...headerProps} {...props} footer={<Footer />}>
      <div className={styles.container}>
        <SwipeableTabs
          tabs={HOT_STOCKS_TABS}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          content={[
            {
              id: 0,
              children: (
                <Trending {...props} activeTab={activeTab} position={0} />
              ),
            },
            {
              id: 1,
              children: (
                <MostInvested {...props} activeTab={activeTab} position={1} />
              ),
            },
            {
              id: 2,
              children: (
                <RecentSearch {...props} activeTab={activeTab} position={2} />
              ),
            },
          ]}
        />
      </div>
    </HeaderLayout>
  );
}

export default baseComponent(HotStocks);

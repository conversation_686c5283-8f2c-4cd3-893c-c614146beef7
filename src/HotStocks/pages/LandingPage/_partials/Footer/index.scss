.container {
  width: calc(100% - 32px);

  padding: 12px 16px;

  bottom: 0;
  position: fixed;
  height: fit-content;

  display: flex;
  gap: 12px;
  background-color: map-get($colors, PureWhite);

  border-top: 1px solid map-get($colors, OffsetSecondary);

  .button {
    border-radius: 8px;

    &:first-child {
      border: 1px solid map-get($colors, OffsetSecondary);
      @include typography(heading1B2, map-get($colors, DBlue), false, true);
    }

    &:last-child {

      display: flex;
      align-items: center;
      justify-content: center;

      height: 48px;
      width: 100%;

      padding: 0 10px;
      border-radius: 6px;
      border: none;
      outline: none;
      cursor: pointer;

      background-color: map-get($colors, DBlue);

      @include typography(heading1B2, map-get($colors, PureWhite), false, true);

      span {
        font-size: 20px;
        margin-top: -2px;
        margin-right: 5px;
      }
    }
  }
}

import React from 'react';

import Button from '../../../../../components/Button/Button';
import {
  openDeepLink,
  openDeepLinkPaytmMoney,
} from '../../../../../utils/bridgeUtils';
import { isPaytmMoney } from '../../../../../utils/commonUtils';
import { DEEPLINKS, FOOTER } from '../../../../utils/enums';
import styles from './index.scss';

function Footer() {
  const callDeeplink = deeplink => {
    if (isPaytmMoney()) {
      openDeepLinkPaytmMoney(deeplink);
    } else {
      openDeepLink(deeplink);
    }
  };

  const primaryClick = () => {
    callDeeplink(DEEPLINKS.ADD_FUNDS);
  };
  const secondaryClick = () => {
    callDeeplink(DEEPLINKS.PORTFOLIO);
  };

  return (
    <div className={styles.container}>
      <Button
        className={styles.button}
        onClickHandler={secondaryClick}
        buttonText={FOOTER.SECONDARY_CTA.TEXT}
        isTextOnly
      />
      <button className={styles.button} onClick={primaryClick}>
        <span>+</span>
        {FOOTER.PRIMARY_CTA.TEXT}
      </button>
    </div>
  );
}

export default Footer;

import React, { useContext } from 'react';
import axios from 'axios';

import Loader from '../../components/Loader/Loader';
import AppContext from '../../context/AppContext';
import Drawer from '../../components/Drawer';
import Button from '../../components/Button/Button';
import { setRootError } from '../../actions/genericActions';
import { useBackPress } from '../../utils/react';

import s from './BaseComponent.scss';

const baseComponent = WrappedComponent => {
  function BaseComponent(props) {
    // useStyles(s);
    const { Context } = useContext(AppContext);
    // eslint-disable-next-line no-underscore-dangle
    window._context = Context;

    const { CancelToken } = axios;
    const axiosSource = CancelToken.source();

    const showLoader = loader => {
      if (loader) return <Loader />;
    };

    const { stack } = useBackPress();

    const showErrorPopup = errorData => {
      if (errorData) {
        function handleCta() {
          const callback = stack.pop();
          if (errorData.callback) {
            errorData.callback();
          }
          if (callback) {
            callback();
          } else {
            setRootError(false);
          }
        }
        return (
          <Drawer
            isOpen
            popup
            showCross={false}
            onClose={() => setRootError(false)}
            isDesktop={'__BUILD_PATH__' === 'desktop'}
          >
            <div className={s.ErrorWrapper}>
              {/* !errorData.hideImage && <img alt="" /> */
              /* TODO: add img */}
              {errorData.mainMsg && <h3>{errorData.mainMsg}</h3>}
              <p>
                {errorData?.message ||
                  errorData?.Message ||
                  errorData?.error ||
                  errorData?.meta?.display_message ||
                  'Something Went Wrong!'}
              </p>
              {!errorData.hideBtn && (
                <Button
                  isPrimaryBlue
                  isPrimaryText
                  buttonText={errorData.btnTxt || 'OK'}
                  onClickHandler={handleCta}
                />
              )}
            </div>
          </Drawer>
        );
      }
    };

    const { loader, error } = Context;

    return (
      <>
        <WrappedComponent
          {...props}
          context={Context}
          axiosSource={axiosSource}
        />
        {showLoader(loader)}
        {showErrorPopup(error)}
      </>
    );
  }

  return BaseComponent;
};

export default baseComponent;

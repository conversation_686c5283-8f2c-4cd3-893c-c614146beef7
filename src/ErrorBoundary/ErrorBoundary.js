/* eslint-disable no-undef */
import React from 'react';
import GenericErrorPage from '../components/GenericErrorPage/GenericErrorPage';
import { sendErrorToBackend } from '../actions/runtime';

const windowHeight = window.innerHeight;
const isProd = __ENV__ === 'production';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: undefined };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI.
    return { hasError: true, error };
  }

  render() {
    if (this.state.hasError) {
      if (isProd) {
        sendErrorToBackend({
          data: this.state.error,
          level: 'fatal',
          key: 'fatal_error',
        });
      }
      return (
        <GenericErrorPage
          cardHeight={windowHeight - 40}
          error={!isProd && this.state.error.toString()}
        />
      );
    }
    return this.props.children;
  }
}

export default ErrorBoundary;

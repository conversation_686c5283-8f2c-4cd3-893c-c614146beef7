import React, { useEffect, useState } from 'react';
import styles from './index.scss';
import Header from '../../components/Header/index';
import DematStatusCard from '../../components/DematStatusCard';
import HoldingsTable from '../../components/HoldingsTable';
import ErrorFallbackPage from '../../components/ErrorFallbackPage';
import baseComponent from '../../../HOC/BaseComponent/BaseComponent';
import { fetchDematHoldingsData } from '../../../actions/dematHoldingsActions';
import { DEMAT_HOLDINGS_TEXTS } from '../../utils/enum';

const DematHoldings = props => {
  const [dematData, setDematData] = useState([]);
  const [error, setError] = useState(false);
  const [loading, setLoading] = useState(true);

  const getDematHoldingsData = async () => {
    try {
      setLoading(true);
      setError(false);

      const response = await fetchDematHoldingsData(props.axiosSource);

      if (response?.data?.results) {
        setDematData(response.data.results);
      } else {
        setError(true);
      }
    } catch (err) {
      setError(true);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getDematHoldingsData();
  }, []);

  const handleRetry = () => {
    getDematHoldingsData();
  };

  return (
    <div className={styles.container}>
      {!error && <div className={styles.background} />}
      <div className={styles.mainWrapper}>
        <Header
          {...props}
          title={DEMAT_HOLDINGS_TEXTS.title}
          subTitle={DEMAT_HOLDINGS_TEXTS.subTitle}
          shrinked={error}
        />
        {error ? (
          <ErrorFallbackPage onRetry={handleRetry} />
        ) : (
          <>
            <DematStatusCard data={dematData[0]} loading={loading} />
            <HoldingsTable data={dematData} loading={loading} />
          </>
        )}
      </div>
    </div>
  );
};

export default baseComponent(DematHoldings);

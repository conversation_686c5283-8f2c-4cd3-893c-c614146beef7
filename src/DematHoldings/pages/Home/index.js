import React, { useEffect, useState } from 'react';
import styles from './index.scss';
import Header from '../../components/Header/index';
import DematStatusCard from '../../components/DematStatusCard';
import HoldingsTable from '../../components/HoldingsTable';
import ErrorFallbackPage from '../../components/ErrorFallbackPage';
import InfiniteScroll from '../../../components/InfiniteScroll/InfiniteScroll';
import TextShimmer from '../../components/_partials/TextShimmer/TextShimmer';
import baseComponent from '../../../HOC/BaseComponent/BaseComponent';
import { fetchDematHoldingsData } from '../../../actions/dematHoldingsActions';
import { DEMAT_HOLDINGS_TEXTS, PAGINATION_CONSTANTS } from '../../utils/enum';

const DematHoldings = props => {
  const [dematData, setDematData] = useState([]);
  const [error, setError] = useState(false);
  const [loading, setLoading] = useState(true);
  const [hasNext, setHasNext] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [dataFetchComplete, setDataFetchComplete] = useState(false); // State variable as per requirement - set to true when hasNext = false
  const [isPaginationLoading, setIsPaginationLoading] = useState(false);

  const getDematHoldingsData = async (pageNo = 0, isLoadMore = false) => {
    try {
      if (!isLoadMore) {
        setLoading(true);
        setError(false);
        setDematData([]);
        setCurrentPage(0);
        setDataFetchComplete(false);
      } else {
        setIsPaginationLoading(true);
      }

      const response = await fetchDematHoldingsData(
        props.axiosSource,
        pageNo,
        PAGINATION_CONSTANTS.PAGE_SIZE,
      );

      if (response?.data?.results) {
        const newData = response.data.results;
        const pageContext = response.data.page_context;

        if (isLoadMore) {
          setDematData(prevData => [...prevData, ...newData]);
        } else {
          setDematData(newData);
        }

        setHasNext(pageContext?.has_next || false);
        setCurrentPage(pageNo);

        if (!pageContext?.has_next) {
          setDataFetchComplete(true);
        }
      } else {
        setError(true);
      }
    } catch (err) {
      setError(true);
    } finally {
      if (!isLoadMore) {
        setLoading(false);
      } else {
        setIsPaginationLoading(false);
      }
    }
  };

  const loadMoreData = () => {
    if (hasNext && !isPaginationLoading) {
      getDematHoldingsData(currentPage + 1, true);
    }
  };

  useEffect(() => {
    getDematHoldingsData();
  }, []);

  const handleRetry = () => {
    getDematHoldingsData();
  };

  return (
    <div className={styles.container}>
      {!error && <div className={styles.background} />}
      <div className={styles.mainWrapper}>
        <Header
          {...props}
          title={DEMAT_HOLDINGS_TEXTS.title}
          subTitle={DEMAT_HOLDINGS_TEXTS.subTitle}
          shrinked={error}
        />
        {error ? (
          <ErrorFallbackPage onRetry={handleRetry} />
        ) : (
          <>
            <DematStatusCard data={dematData[0]} loading={loading} />
            <InfiniteScroll
              pageStart={0}
              initialLoad={false}
              loadMore={loadMoreData}
              hasMore={hasNext}
              loader={
                <div style={{ padding: '10px' }}>
                  <TextShimmer />
                </div>
              }
              useWindow={false}
            >
              <HoldingsTable data={dematData} loading={loading} />
            </InfiniteScroll>
          </>
        )}
      </div>
    </div>
  );
};

export default baseComponent(DematHoldings);

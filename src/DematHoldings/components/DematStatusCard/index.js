import React from 'react';
import styles from './index.scss';
import Card from '../_partials/Card/Card';
import TextShimmer from '../_partials/TextShimmer/TextShimmer';
import { DEMAT_STATUS } from '../../utils/enum';

const DematStatusCard = ({ data, loading }) => {
  const getValue = value => {
    if (value === undefined || value === null || value === '') {
      return '--';
    }
    return value;
  };

  const content = {
    dematNumber: {
      label: DEMAT_STATUS.dematNumber,
      value: loading ? (
        <TextShimmer styling={styles.shimmerWidth} />
      ) : (
        getValue(data?.boid)
      ),
    },
    dematStatus: {
      label: DEMAT_STATUS.dematStatus,
      value: loading ? (
        <TextShimmer styling={styles.shimmerWidth} />
      ) : (
        getValue(data?.status)
      ),
    },
    asOnDate: {
      label: DEMAT_STATUS.asOnDate,
      value: loading ? (
        <TextShimmer styling={styles.shimmerWidth} />
      ) : (
        getValue(data?.as_on_date)
      ),
    },
  };

  return (
    <Card customStyles={styles.card}>
      <div className={styles.column}>
        <label className={styles.label}>{content.dematNumber.label}</label>
        <div className={styles.data}>{content.dematNumber.value}</div>
      </div>
      <div className={styles.row}>
        <div className={styles.column}>
          <label className={styles.label}>{content.dematStatus.label}</label>
          <div className={styles.data}>{content.dematStatus.value}</div>
        </div>
        <div className={styles.column}>
          <label className={styles.label}>{content.asOnDate.label}</label>
          <div className={styles.data}>{content.asOnDate.value}</div>
        </div>
      </div>
    </Card>
  );
};

export default DematStatusCard;

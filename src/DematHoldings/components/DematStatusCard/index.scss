.card {
  display: flex;
  flex-direction: column;
  align-items: start;

  .row {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 4px;

    .column {
      .shimmerWidth {
        width: 50%;
      }
    }
  }

  .column {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 4px;

    .shimmerWidth {
      width: 50%;
    }
  }

  .label {
    @include typography(text1216, map-get($colors, DHTertiary), false, true);
    font-weight: 400;
  }

  .data {
    @include typography(text1420, map-get($colors, DHPrimary), false, true);
    font-weight: 500;
  }
}

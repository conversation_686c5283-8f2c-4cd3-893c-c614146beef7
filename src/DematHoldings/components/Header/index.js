import React, { useState, useEffect, useRef } from 'react';
import classNames from 'classnames';
import styles from './index.scss';
import Icon, { ICONS_NAME } from '../../../components/Icon';
import { goBack } from '../../../services/coreUtil';
import history from '../../../history';

const Header = ({ title, subTitle, shrinked = false }) => {
  const headerRef = useRef();
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const distanceY = window.scrollY || document.documentElement.scrollTop;
      if (distanceY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleBackClick = () => {
    goBack(history);
  };

  if (shrinked) {
    return (
      <div
        ref={headerRef}
        className={classNames(styles.container, {
          [styles.scrolled]: <PERSON>olean(shrinked),
        })}
      >
        <div className={styles.headerContent}>
          <button className={styles.backButton} onClick={handleBackClick}>
            <Icon
              name={ICONS_NAME.ARROW_BACK}
              height="24px"
              width="24px"
              className={classNames(styles.iconWrapper, {
                [styles.iconBordered]: Boolean(shrinked),
              })}
            />
          </button>
          <div
            className={classNames(styles.title, {
              [styles.titleSmaller]: Boolean(shrinked),
            })}
          >
            {title}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={headerRef}
      className={classNames(styles.container, {
        [styles.scrolled]: isScrolled,
      })}
    >
      <div className={styles.headerContent}>
        <button className={styles.backButton} onClick={handleBackClick}>
          <Icon
            name={ICONS_NAME.ARROW_BACK}
            height="24px"
            width="24px"
            className={classNames(styles.iconWrapper, {
              [styles.iconBordered]: isScrolled,
            })}
          />
        </button>
        <div
          className={classNames(styles.title, {
            [styles.titleSmaller]: isScrolled,
          })}
        >
          {title}
        </div>
      </div>
      <div className={styles.expandedHeader}>
        <div className={styles.title}>{title}</div>
        <div className={styles.subtitle}>{subTitle}</div>
      </div>
    </div>
  );
};

export default Header;

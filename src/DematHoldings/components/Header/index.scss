@import 'src/commonStyles/commoncss';

.container {
  position: relative;
  transition: all 0.1s ease-in-out;
  z-index: 10;
  transition: height 0.3s ease-in-out;

  .headerContent {
    gap: 16px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease-in-out;

    padding: 16px 16px 0 16px;

    top: 0;
    left: 0;
    position: fixed;
    width: 100%;

    .backButton {
      all: unset;
      display: inline-block;
      cursor: pointer;
    }

    .title {
      @include typography(text1824, map-get($colors, DHPrimary), false, true);
      font-weight: 500;

      opacity: 0;
      transition: all 0.1s ease-in-out;
      transform: translateY(20px);
    }
  }

  .expandedHeader {
    padding: 66px 16px 16px 0;

    .backButton {
      all: unset;
      display: inline-block;
      cursor: pointer;
    }

    .title {
      @include typography(text2432, map-get($colors, DHPrimary), false, true);
      font-weight: 600;
      margin-top: 16px;
      opacity: 1;
      transition: all 0.1s ease-in-out;
    }

    .subtitle {
      @include typography(text1420, map-get($colors, DHPrimary), false, true);
      font-weight: 400;
      margin-top: 4px;
    }

    .hyperlink {
      @include typography(text1216, map-get($colors, DHHyperlink), false, true);
      font-weight: 500;
      margin-top: 4px;
    }
  }

  .iconWrapper {
    padding: 0;
    background-color: map-get($colors, DHBG1);
    border-radius: 50%;
    border: 1px solid transparent;
    height: 40px;
    width: 40px;
  }
}

.scrolled {
  .headerContent {
    background-color: map-get($colors, PlainBackground);
    background-color: map-get($colors, DHBG1);
    padding-bottom: 8px;
    padding-top: 8px;

    .title {
      transform: translateY(0px);
      opacity: 1 !important;
    }

    .iconBordered {
      border: 1px solid map-get($colors, DHQuaternary);
    }
  }
}

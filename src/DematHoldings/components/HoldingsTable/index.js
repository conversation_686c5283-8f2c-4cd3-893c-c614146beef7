import React, { useState } from 'react';
import classNames from 'classnames';
import styles from './index.scss';
import Card from '../_partials/Card/Card';
import TableHeader from './_partials/Header';
import Fallback from './_partials/Fallback/Fallback';
import TextShimmer from '../_partials/TextShimmer/TextShimmer';
import { FALLBACK_MESSAGES_HT, TABLE_HEADERS_HT } from '../../utils/enum';

const HoldingsTable = ({ data, loading }) => {
  const [searchQuery, setSearchQuery] = useState('');

  const filteredStocks = data?.filter(stock =>
    stock.name.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const getFallbackValue = value =>
    value === undefined || value === null || value === ''
      ? FALLBACK_MESSAGES_HT.emptyValue
      : value;

  const getHeaderValue = value =>
    TABLE_HEADERS_HT[value] || FALLBACK_MESSAGES_HT.emptyValue;

  if (!loading && data && data.length === 0) {
    return (
      <Card customStyles={styles.customCard}>
        <Fallback title={FALLBACK_MESSAGES_HT.noHoldings} />
      </Card>
    );
  }

  return (
    <Card customStyles={styles.customCard}>
      <TableHeader searchQuery={searchQuery} onSearch={setSearchQuery} />
      <div className={styles.tableWrapper}>
        {!loading && filteredStocks.length === 0 ? (
          <Fallback
            showHeader={false}
            title={FALLBACK_MESSAGES_HT.noSearchResults}
            showHyperlink={false}
            styling={styles.customFallback}
          />
        ) : (
          <table className={styles.table}>
            <thead>
              {loading ? (
                <tr>
                  <th>
                    <TextShimmer />
                  </th>
                </tr>
              ) : (
                <tr>
                  {Object.keys(TABLE_HEADERS_HT)?.map((column, index) => (
                    <th
                      key={index}
                      className={
                        index === 0
                          ? styles.fixedColumn
                          : styles.scrollableColumn
                      }
                    >
                      {index === 0 ? (
                        <div>{getHeaderValue(column, true)}</div>
                      ) : (
                        getHeaderValue(column)
                      )}
                    </th>
                  ))}
                </tr>
              )}
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td>
                    <TextShimmer />
                  </td>
                </tr>
              ) : (
                filteredStocks?.map((stock, rowIndex) => (
                  <tr key={rowIndex}>
                    {Object.keys(TABLE_HEADERS_HT)?.map((column, colIndex) => (
                      <td
                        key={colIndex}
                        className={classNames({
                          [styles.fixedColumn]: colIndex === 0,
                          [styles.scrollableColumn]: colIndex !== 0,
                          [styles.textRight]: colIndex !== 0,
                        })}
                      >
                        {getFallbackValue(stock[column])}
                      </td>
                    ))}
                  </tr>
                ))
              )}
            </tbody>
          </table>
        )}
      </div>
    </Card>
  );
};

export default HoldingsTable;

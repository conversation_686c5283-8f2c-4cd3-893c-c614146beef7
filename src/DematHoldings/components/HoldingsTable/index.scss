.customCard {
  position: relative;
  padding: 0;
}

.tableWrapper {
  @include noScrollBar;
  position: relative;
  overflow-x: auto;
  border-radius: 12px;
  padding: 0;
  top: 52px;
  width: 100%;
  max-height: 500px;

  .customFallback {
    padding: 0 0 66px 0;
  }

  .table {
    @include typography(text1420, map-get($colors, DHPrimary), false, true);
    font-weight: 400;
    border-spacing: 0;
    border-collapse: unset;
    background-color: map-get($colors, DHBG1);
    min-height: 89px;
    width: 100%;

    td {
      border: none;
      border-bottom: 1px solid map-get($colors, DHBorder1);
    }

    thead {
      tr {
        th {
          @include typography(
            text1016,
            map-get($colors, DHTertiary),
            false,
            true
          );
          padding: 16px 16px;
          padding-bottom: 0;
          text-align: left;
          font-weight: 400;
          text-align: left;

          &:first-of-type {
            min-width: 80px;
          }
        }
      }
    }

    tbody {
      tr {
        td {
          @include typography(
            text1420,
            map-get($colors, DHPrimary),
            false,
            true
          );
          min-width: 50px;
          font-weight: 400;
          padding: 16px 16px;

          &:nth-of-type(1) td {
            padding-top: 12px;
          }

          &first-of-type {
            min-width: 80px;
          }
        }
      }
    }
  }
}

.fixedColumn {
  position: sticky;
  left: 0;
  z-index: 1;
  white-space: nowrap;
  background-color: map-get($colors, DHBG1);
  z-index: 2;
  box-shadow: 4px 4px 5px rgba(0, 0, 0, 0.1);
}

.scrollableColumn {
  white-space: nowrap;
  background-color: map-get($colors, DHBG1);
  z-index: 1;
}

.textRight {
  text-align: right;
}

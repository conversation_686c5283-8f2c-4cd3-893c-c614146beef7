import React, { useEffect, useRef, useState } from 'react';
import styles from './index.scss';
import Icon, { ICONS_NAME } from '../../../../../components/Icon';

const TableHeader = ({ searchQuery, onSearch, showSearch = true }) => {
  const [isSearching, setIsSearching] = useState(false);
  const inputRef = useRef(null);

  useEffect(() => {
    if (isSearching && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isSearching]);

  const handleSearchClick = () => {
    setIsSearching(true);
  };

  const handleBackClick = () => {
    setIsSearching(false);
  };

  const handleInputChange = e => {
    onSearch(e.target.value);
  };

  const handleClearClick = () => {
    onSearch('');
  };

  return (
    <div className={styles.searchContainer}>
      {showSearch && isSearching ? (
        <div className={styles.inputWrapper}>
          <button className={styles.backButton} onClick={handleBackClick}>
            <Icon name={ICONS_NAME.ARROW_BACK} height="18px" width="18px" />
          </button>
          <div className={styles.searchInput}>
            <input
              ref={inputRef}
              type="text"
              placeholder="Search funds"
              value={searchQuery}
              onChange={handleInputChange}
            />
            {true && (
              <button className={styles.clearButton} onClick={handleClearClick}>
                <Icon
                  name={ICONS_NAME.CLOSE_CIRCLE}
                  height="18px"
                  width="18px"
                />
              </button>
            )}
          </div>
        </div>
      ) : (
        <div className={styles.rowLayout}>
          <div className={styles.title}>
            <span className={styles.text}>Holding Details </span>
            <button className={styles.clearButton} onClick={handleClearClick}>
              {/* <Icon name={ICONS_NAME.INFO_ICON} height="18px" width="18px" /> */}
            </button>
          </div>
          {showSearch && (
            <button className={styles.searchButton} onClick={handleSearchClick}>
              <Icon name={ICONS_NAME.SEARCH} height="18px" width="18px" />
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default TableHeader;

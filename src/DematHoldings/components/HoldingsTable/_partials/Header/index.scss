.searchContainer {
  @include typography(text1420, map-get($colors, DHPrimary), false, true);
  font-weight: 600;
  width: 100%;
  height: 52px;
  position: absolute;
  top: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 5;
}

.rowLayout {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 16px;

  .title {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;

    .text {
      @include typography(text1420, map-get($colors, DHPrimary), false, true);
      font-weight: 600;
    }
  }
}

.text {
  font-size: 16px;
}

.searchButton {
  padding: 6px 12px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.inputWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 20px;
  gap: 8px;
  padding: 16px 0;

  .backButton,
  .clearButton {
    all: unset;
    display: inline-block;
    cursor: pointer;
    padding-left: 16px;
  }

  .searchInput {
    width: 100%;
    display: flex;
    padding-right: 16px;

    input {
      width: 100%;
      border: none;
      outline: none;
      background-color: map-get($colors, DHBG1);
      caret-color: map-get($colors, DHPrimary);
      color: map-get($colors, DHPrimary);
    }
  }
}

.backButton,
.clearButton,
.searchButton {
  all: unset;
  display: inline-block;
  cursor: pointer;
}

input[type='text'] {
  flex-grow: 1;
  padding: 6px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.clearButton {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #999;
}

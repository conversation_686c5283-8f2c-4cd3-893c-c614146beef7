.container {
  @include typography(text1420, map-get($colors, DHPrimary));
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 96px 0 102px 0;

  .text1 {
    @include typography(text1420, map-get($colors, DHPrimary), false, true);
    font-size: 16px;
    line-height: 22px;
    font-weight: 500;
    text-align: center;
  }

  .text2 {
    @include typography(text1420, map-get($colors, DHSecondary), false, true);
    font-weight: 400;
    text-align: center;
  }

  .hyperlink {
    @include typography(text1420, map-get($colors, DHHyperlink), false, true);
    font-weight: 500;
    text-align: center;
  }
}

import React from 'react';
import classNames from 'classnames';
import styles from './Fallback.scss';
import Icon, { ICONS_NAME } from '../../../../../components/Icon';
import TableHeader from '../Header';
import { DEEPLINKS, FALLBACK_TEXTS } from '../../../../utils/enum';
import { isPaytmMoney } from '../../../../../utils/commonUtils';
import {
  openDeepLink,
  openDeepLinkPaytmMoney,
} from '../../../../../utils/bridgeUtils';

const Fallback = ({
  showHeader = true,
  title,
  subtitle,
  showHyperlink = true,
  styling,
}) => {
  const callDeeplink = deeplink => {
    if (isPaytmMoney()) {
      openDeepLinkPaytmMoney(deeplink);
    } else {
      openDeepLink(deeplink);
    }
  };

  const primaryClick = () => {
    callDeeplink(DEEPLINKS.WATCHLIST);
  };

  return (
    <div className={classNames(styles.container, styling)}>
      {showHeader && <TableHeader showSearch={false} />}
      <div>
        <Icon name={ICONS_NAME.NO_HOLDINGS} width="200px" />
      </div>
      <div>
        <div className={styles.text1}>{title || FALLBACK_TEXTS.title}</div>
        <div className={styles.text2}>
          {subtitle || FALLBACK_TEXTS.subtitle}
        </div>
      </div>
      {showHyperlink && (
        <div className={styles.hyperlink} onClick={primaryClick}>
          {FALLBACK_TEXTS.hyperlinkText}
        </div>
      )}
    </div>
  );
};

export default Fallback;

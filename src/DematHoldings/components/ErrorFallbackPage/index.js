import React from 'react';
import styles from './index.scss';
import TableHeader from '../Header';
import Icon, { ICONS_NAME } from '../../../components/Icon';

const ErrorFallbackPage = ({ onRetry }) => (
  <div className={styles.container}>
    <div>
      <Icon name={ICONS_NAME.DEMAT_HOLDING_FALLBACK} width="278px" />
    </div>
    <div className={styles.text}>
      Some of the values could not be loaded due to network bandwidth.
    </div>
    <div className={styles.hyperlink} onClick={onRetry}>
      Retry
    </div>
  </div>
);

export default ErrorFallbackPage;

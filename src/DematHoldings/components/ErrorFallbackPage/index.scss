.container {
  @include typography(text1420, map-get($colors, DHPrimary));
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding-bottom: 16px;

  .text {
    @include typography(text1420, map-get($colors, DHPrimary));
    font-weight: 400;
    text-align: center;
    max-width: 344px;
  }

  .hyperlink {
    @include typography(text1420, map-get($colors, DHBG1));
    font-weight: 600;
    text-align: center;
    padding: 8px 40px;
    background-color: map-get($colors, DHPrimary);
    border-radius: 48px;
  }
}

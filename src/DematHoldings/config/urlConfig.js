import { EQ_HOST } from '../../config/urlConfig';

export const COMPANY_BREAKOUT_ROUTES = {
  HOME: '/home',
};

// const getStaticHost = () => {
//   if (__ENV__ === 'production') {
//     return 'https://static.paytmmoney.com/data/v1/production/';
//   }

//   return 'https://static.paytmmoney.com/data/v1/staging/';
// };

export const DEMAT_HOLDINGS_APIS = {
  GET_DEMAT_HOLDINGS: `${EQ_HOST}holdings/v1/get-demat-holdings-data`,
  // GET_DEMAT_HOLDINGS: `https://run.mocky.io/v3/eaf37c84-0901-4903-a5a1-fde70d6459f8`,
};

import { isPaytmMoney } from '../../utils/commonUtils';

export const DEEPLINKS = {
  WATCHLIST: isPaytmMoney()
    ? 'https://www.paytmmoney.com/stocks/watchlist'
    : 'paytmmp://paytmmoney/stocks/equity-dashboard?activeTab=watchlist&dl=true',
};

export const DEMAT_STATUS = {
  dematNumber: 'Demat (BOID) No',
  dematStatus: 'Demat Status',
  asOnDate: 'As On Date',
};

export const FALLBACK_TEXTS = {
  title: 'You have no holdings!',
  subtitle: 'Place a trade and increase your portfolio',
  hyperlinkText: 'Go to Watchlist',
};

export const TABLE_HEADERS_HT = {
  name: 'Name',
  free_qty: 'Free Qty',
  pledged_qty: 'Pledged Qty',
  locked_qty: 'Locked Qty',
  earmark_qty: 'Earmarked Qty',
  safe_keep_qty: 'Safe Keep Qty',
};

export const FALLBACK_MESSAGES_HT = {
  noHoldings: 'You have no holdings!',
  noSearchResults: 'No holdings match your search',
  emptyValue: '--',
};

export const DEMAT_HOLDINGS_TEXTS = {
  title: 'Demat Holdings',
  subTitle: 'View your portfolio in one place with ease.',
};

export const ERROR_MESSAGES = {
  genericError: 'An error occurred. Please try again.',
};

export const PAGINATION_CONSTANTS = {
  PAGE_SIZE: 20,
};

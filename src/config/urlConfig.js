/* eslint-disable no-undef */
export const env = __ENV__ === 'production' ? 'production' : 'staging';

const getEqHost = () => {
  const BASE_API_URL_EQ_PROD = 'https://api-eq.paytmmoney.com/';
  const BASE_API_URL_EQ_STG = 'https://api-eq-stg.paytmmoney.com/';
  if (env === 'production') {
    return BASE_API_URL_EQ_PROD;
  } else if (env === 'staging') {
    return BASE_API_URL_EQ_STG;
  }
  return BASE_API_URL_EQ_PROD;
};

export const getEqAuthHost = () => {
  const BASE_API_URL_EQ_PROD = 'https://stocks.paytmmoney.com/';
  const BASE_API_URL_EQ_STG = 'https://stocks-stg.paytmmoney.com/';
  if (__ENV__ === 'production') {
    return BASE_API_URL_EQ_PROD;
  } else if (__ENV__ === 'staging') {
    return BASE_API_URL_EQ_STG;
  }
  return BASE_API_URL_EQ_PROD;
};

const getPmApiHost = () => {
  const PM_API_HOST_PROD = 'https://api.paytmmoney.com/';
  const PM_API_HOST_STAG = 'https://api-staging.paytmmoney.com/';
  if (__ENV__ === 'production') {
    return PM_API_HOST_PROD;
  } else if (__ENV__ === 'staging') {
    return PM_API_HOST_STAG;
  }
  return PM_API_HOST_STAG;
};

const getStaticHost = () => {
  if (__ENV__ === 'production') {
    return 'https://static.paytmmoney.com/data/v1/production/';
  }

  return 'https://static.paytmmoney.com/data/v1/staging/';
};

export const EQ_HOST = getEqHost();
export const EQ_AUTH_HOST = getEqAuthHost();
export const PM_API_HOST = getPmApiHost();
const STATIC_HOST = getStaticHost();

export const EQ_AUTH_URL = {
  LOGIN_URL: `${EQ_AUTH_HOST}?returnUrl={redirectURL}`,
};

const getLoggerHost = () => {
  const BASE_API_URL_LOGGER = 'https://logger.paytmmoney.com/';
  const BASE_API_URL_EQ_STG = 'https://api-eq-stg.paytmmoney.com/';
  if (env === 'production') {
    return BASE_API_URL_LOGGER;
  } else if (__ENV__ === 'beta') {
    return BASE_API_URL_LOGGER;
  } else if (env === 'staging') {
    return BASE_API_URL_EQ_STG;
  }
  return BASE_API_URL_LOGGER;
};

export const GENERIC_API_URL = {
  APP_LOG: `${getLoggerHost()}logger/log`,
  GET_CUSTOMER_PLAN: (userId, productType) =>
    `${EQ_HOST}subscription/customer/${userId}/plan?productType=${productType}`,
  CHARGES_INFO: {
    POST: `${EQ_HOST}fms/api/v1/charges/info`,
  },
  SCRIPT_INFO: `${EQ_HOST}mtf/order/api/v2/scrips`,
};

const CDSL = {
  production: {
    VERIFY_PIN:
      'https://www.paytmmoney.com/stocks/corporate-actions/buyback-apply',
    REDIRECTION_URL:
      'https://www.paytmmoney.com/stocks/corporate-actions/buyback-apply',
  },
  staging: {
    VERIFY_PIN:
      'https://web-staging.paytmmoney.com/stocks/corporate-actions/buyback-apply',
    REDIRECTION_URL:
      'https://web-staging.paytmmoney.com/stocks/corporate-actions/buyback-apply',
  },
};

export const CDSL_URL = CDSL[env];

export const getCombinedDashboardWebUrl = () => {
  const COMBINED_DASHBOARD_WEB_URL_PROD =
    'https://www.paytmmoney.com/dashboard';
  const COMBINED_DASHBOARD_WEB_URL_BETA =
    'https://www.paytmmoney.com/dashboard';
  const COMBINED_DASHBOARD_WEB_URL_STG =
    'https://web-staging.paytmmoney.com/dashboard';
  if (__ENV__ === 'production') {
    return COMBINED_DASHBOARD_WEB_URL_PROD;
  } else if (__ENV__ === 'beta') {
    return COMBINED_DASHBOARD_WEB_URL_BETA;
  } else if (__ENV__ === 'staging') {
    return COMBINED_DASHBOARD_WEB_URL_STG;
  }
  return COMBINED_DASHBOARD_WEB_URL_PROD;
};

const getPfHost = () => {
  const BASE_API_URL_PF_PROD = 'https://api-pf.paytmmoney.com/';
  const BASE_API_URL_PF_STG = 'https://pf-stg.paytmmoney.com/';
  if (env === 'staging') {
    return BASE_API_URL_PF_STG;
  }
  return BASE_API_URL_PF_PROD;
};

export const getPfAuthHost = () => {
  const BASE_API_URL_PF_PROD = 'https://login.paytmmoney.com/';
  const BASE_API_URL_PF_BETA = 'https://login-preprod.paytmmoney.com/';
  const BASE_API_URL_PF_STG = 'https://login-stg.paytmmoney.com/';
  if (__ENV__ === 'production') {
    return BASE_API_URL_PF_PROD;
  } else if (__ENV__ === 'beta') {
    return BASE_API_URL_PF_BETA;
  } else if (__ENV__ === 'staging') {
    return BASE_API_URL_PF_STG;
  }
  return BASE_API_URL_PF_PROD;
};

export const getLoginHost = () => {
  const LOGIN_URL_PROD = 'https://h5-login.paytmmoney.com/';
  const LOGIN_URL_BETA = 'https://h5-login-beta.paytmmoney.com/';
  const LOGIN_URL_STG = 'https://h5-login-stag.paytmmoney.com/';
  if (__ENV__ === 'production') {
    return LOGIN_URL_PROD;
  } else if (__ENV__ === 'beta') {
    return LOGIN_URL_BETA;
  } else if (__ENV__ === 'staging') {
    return LOGIN_URL_STG;
  }
  return LOGIN_URL_PROD;
};

export const PF_HOST = getPfHost();
export const PF_AUTH_HOST = getPfAuthHost();

export const LOGIN_URL = `${getLoginHost()}?returnUrl={redirectURL}`;

export const USER_BOOT = useId =>
  `${PM_API_HOST}pm/api/v2/users/boot/${useId}?details=personalDetails`;

export const MARKET_UPDATES_URL = `${STATIC_HOST}market_updates.json`;
export const CST_URL = 'https://pml-cst-gateway-prod-public.paytmmoney.com/';

export const getOnboardingStorefront = () => {
  const ONBOARDING_API_PROD =
    'https://static.paytmmoney.com/mini-app/data/mtf-sf-data.json';
  const ONBOARDING_API_STAGE =
    'https://static.paytmmoney.com/mini-app/data/stg/mtf-sf-data.json';
  if (__ENV__ === 'production') {
    return ONBOARDING_API_PROD;
  } else if (__ENV__ === 'staging') {
    return ONBOARDING_API_STAGE;
  }
  return ONBOARDING_API_STAGE;
};
export const MTF_CALCULATOR_URL = `${STATIC_HOST}mtf-calculator-config.json`;

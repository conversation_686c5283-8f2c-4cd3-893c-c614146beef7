import React, { useEffect } from 'react';
import classname from 'classnames';
import { FOOTER, TRENDING_STOCKS_HEADING } from '../../utils/enums';

import history from '../../../history';
import baseComponent from '../../../HOC/BaseComponent/BaseComponent';
import HeaderLayout from '../../../layout/HeaderLayout/HeaderLayout';
import { goBack } from '../../../services/coreUtil';
// import { getQueryParams } from '../../../utils/commonUtils';
import { useBackPress } from '../../../utils/react';
import Trending from '../../components/Trending';
import Footer from './_partials/Footer';
import Chip from '../../../components/Chip/Chip';

import styles from './styles.scss';
import { openDeepLinkPaytmMoney } from '../../../utils/bridgeUtils';
import { isPaytmMoney } from '../../../utils/commonUtils';

function TrendingStocks(props) {
  const { pushStack, clearStack } = useBackPress();

  const [activeChip, setActiveChip] = React.useState(100);

  // const params = getQueryParams();

  const handleBackPress = () => {
    goBack(history);
  };

  useEffect(() => {
    pushStack(handleBackPress);
    return () => {
      clearStack();
    };
  }, []);

  const headerProps = {
    heading: TRENDING_STOCKS_HEADING,
    disableShadow: true,
    showHelp: false,
    onClickHandler: handleBackPress,
    customClass: styles.headerCustomClass,
  };

  const handleChipPress = value => {
    setActiveChip(value);
  };

  const openDeepLink = url => {
    if (isPaytmMoney()) {
      openDeepLinkPaytmMoney(url, null, false);
    } else {
      openDeepLink(url);
    }
  };

  return (
    <HeaderLayout
      {...headerProps}
      {...props}
      footer={
        <Footer
          primaryClick={() => openDeepLink(FOOTER.PRIMARY_CTA.deeplink)}
          secondaryClick={() => openDeepLink(FOOTER.SECONDARY_CTA.deeplink)}
        />
      }
    >
      <div className={styles.div_container}>
        <Chip
          customClass={classname(styles.chip, {
            [styles.activeChip]: activeChip === 100,
          })}
          onClick={() => handleChipPress(100)}
        >
          ₹100
        </Chip>
        <Chip
          customClass={classname(styles.chip, {
            [styles.activeChip]: activeChip === 200,
          })}
          onClick={() => handleChipPress(200)}
        >
          ₹200
        </Chip>
        <Chip
          customClass={classname(styles.chip, {
            [styles.activeChip]: activeChip === 500,
          })}
          onClick={() => handleChipPress(500)}
        >
          ₹500
        </Chip>
      </div>
      <div className={styles.container}>
        <Trending {...props} activeChip={activeChip} position={0} />
      </div>
    </HeaderLayout>
  );
}

export default baseComponent(TrendingStocks);

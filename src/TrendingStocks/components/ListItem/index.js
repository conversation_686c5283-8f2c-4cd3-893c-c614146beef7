import React from 'react';

import Icon from '../../../components/Icon';
import PriceBroadcast from '../../../components/PriceBroadcast';
import styles from './index.scss';
import { isPaytmMoney } from '../../../utils/commonUtils';
import { DEEPLINKS } from '../../utils/enums';
import {
  openDeepLink,
  openDeepLinkPaytmMoney,
} from '../../../utils/bridgeUtils';

function ListItem({ id, symbol, exchange, security_id, segment }) {
  const navigateToCompanyPage = () => {
    if (isPaytmMoney()) {
      openDeepLinkPaytmMoney(DEEPLINKS.COMPANY_PAGE(id));
    } else {
      openDeepLink(DEEPLINKS.COMPANY_PAGE_PAYTM(id));
    }
  };

  return (
    <div className={styles.container} onClick={navigateToCompanyPage}>
      <Icon
        className={styles.companyLogo}
        name={id}
        companyName={symbol}
        size={24}
      />
      <div className={styles.stockNameContainer}>
        <div className={styles.stockName}>{symbol}</div>
        <div className={styles.exchange}>{exchange}</div>
      </div>
      <div className={styles.priceContainer}>
        <PriceBroadcast securityId={security_id} segment={segment} />
      </div>
    </div>
  );
}

export default ListItem;

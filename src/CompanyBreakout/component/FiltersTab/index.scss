.container {
  border-radius: 16px 16px 0px 0px;
  border-top: 1px solid map-get($colors, BorderGrey100);
  padding: 16px 16px 8px 16px;
  background-color: map-get($colors, SecondaryOffsetVariant);
  position: sticky;
  top: 69px;
  z-index: 11;
  .info {
    @include typography(text2R, map-get($colors, textGrey900), false, true);
    font-weight: 400;
    margin-bottom: 20px;
  }

  .filtersContainer {
    gap: 12px;
    display: flex;
    align-items: center;

    .filterButton {
      display: flex;
      gap: 12px;
      align-items: center;
      
      .horizontalBar {
        width: 1px;
        height: 20px;
        background-color: map-get($colors, TertiaryColor);
      }
    }

    .activeTab {
     
      background-color: map-get($colors, textGrey900) !important;

      .filterTitle {
        color: map-get($colors, PlainBackground) !important;
      }
    }

    .filter {
      padding: 8px 10px;
      width: fit-content;
      display: flex;
      align-items: center;
      gap: 4px;
      border-radius: 8px;
      background-color: map-get($colors, PlainBackground);
      .filterTitle {
        @include typography(text2R, map-get($colors, textGrey900), false, true);
        font-weight: 500;
      }
    }
  }
}
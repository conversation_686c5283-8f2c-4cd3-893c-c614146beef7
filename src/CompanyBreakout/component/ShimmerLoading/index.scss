@import 'src/commonStyles/commoncss';

.container {
  padding: 0 16px 16px 16px;

  gap: 12px;
  display: flex;
  justify-content: space-between;

  border-bottom: 1px solid map-get($colors, OffsetSecondary);

  background: map-get($colors, SecondaryOffsetVariant);

  .stockNameContainer {
    flex: 1;

    .stockName {
      width: 100px;
      height: 20px;
    }

    .exchange {
      width: 40px;
      height: 10px;
      margin-top: 4px;
    }
  }

  .companyLogo {
    width: 40px;
    height: 40px;

    border-radius: 6px;
    border: 1px solid map-get($colors, OffsetSecondary);
  }

  .priceContainer {
    display: flex;
    flex-direction: column;
    text-align: right;
    gap: 4px;

    .price {
      width: 60px;
      height: 20px;
    }
  }
}

.containerLight {
  background: map-get($colors, SecondaryOffsetVariant);
}
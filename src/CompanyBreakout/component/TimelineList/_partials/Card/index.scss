
.card {
  padding: 12px 0;

}

.readMore {
  color: map-get($colors, TextLinkBlue);
  font-weight: 400;
}

.titleContainer {
  gap: 6px;
  display: flex;
  align-items: center;


  .icon {
    width: 16px;
    height: 16px;
  }
  
  .title {
    @include typography(heading2B2, map-get($colors, PrimaryColor), false, true);
    line-height: 20px;
    flex: 1;
  }

  .infoIcon {
    width: 20px;
    max-width: 20px;
    height: 20px;
    max-height: 20px;

  }
}

.description {
  @include typography(text1R1, map-get($colors, TextGrey600), false, true);
  font-weight: 400;
  margin-top: 4px;

  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: pre-wrap;
}
import classNames from 'classnames';
import React from 'react';
import { INFO, LOSS, PROFIT } from '../../../../utils/Constants';
import styles from './index.scss';

function Card({ title, description, onInfoClick, className, bTrend }) {
  if (!title) {
    return null;
  }
  return (
    <div
      className={classNames(styles.card, className)}
      onClick={e => {
        e.stopPropagation();
        onInfoClick();
      }}
    >
      <div className={styles.titleContainer}>
        <img
          className={styles.icon}
          src={bTrend === 'BEARISH' ? LOSS : PROFIT}
          alt=""
        />
        <div className={styles.title}>{title}</div>
        <img className={styles.infoIcon} src={INFO} alt="" />
      </div>
      {description ? (
        <div className={styles.description}>
          {description
            .split('\n')
            .slice(0, 2)
            .join('\n')}
        </div>
      ) : null}
    </div>
  );
}

export default Card;

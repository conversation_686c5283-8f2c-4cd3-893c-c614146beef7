
.cardWrapper {
  display: flex;
  flex-direction: column;
  background-color: map-get($colors, PlainBackground);
  padding: 0 16px;
  border-radius: 12px;
  width: 100%;
  margin: 12px 0px 12px 8px;
}

.buttonContainer {
  padding: 4px 16px 16px 16px;

  .button {
    gap: 4px;
    display: flex;
    align-items: center;
    justify-content: center;

    padding: 15px 12px;
    border-radius: 48px;
    background-color: map-get($colors, NeutralStrong);
    @include typography(heading1B1, map-get($colors, TextSilver900), false, true);

    .feedbackIcon {
      width: 32px;
      height: 32px;
      margin-right: 4px;
    }
  }
}


.cardContainer {
  margin-left: 5px;
  display: flex;
  border-left: 1px dashed map-get($colors, LightGrey300);
}

.noBorder {
  border-left: 0;
}


.card {
  border-bottom: 1px solid map-get($colors, BorderGrey100);
}
import classNames from 'classnames';
import React, { useEffect, useRef, useState } from 'react';
import Icon, { ICONS_NAME } from '../../../components/Icon';
import styles from './BreakoutHeader.scss';

const Header = ({ title, subTitle, handleBackPress, onScroll = () => {} }) => {
  const headerRef = useRef();
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const distanceY = window.scrollY || document.documentElement.scrollTop;
      if (distanceY > 50) {
        setIsScrolled(true);
        onScroll(true);
      } else {
        setIsScrolled(false);
        onScroll(false);
      }
    };
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div
      ref={headerRef}
      className={classNames(styles.header, {
        [styles.scrolled]: isScrolled,
      })}
    >
      <div className={styles.headerContent}>
        <Icon
          name={ICONS_NAME.BACK}
          size={4}
          className={styles.backButton}
          iconStyles={styles.customBackIconWrapper}
          onClick={() => {
            handleBackPress();
          }}
        />
        <div className={styles.title}>{title}</div>
      </div>
      <div className={styles.expandedHeader}>
        <div className={styles.title}>{title}</div>
        <div className={styles.subtitle}>{subTitle}</div>
      </div>
    </div>
  );
};

export default Header;

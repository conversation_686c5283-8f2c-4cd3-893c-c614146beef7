@import 'src/commonStyles/commoncss';

.header {
  position: relative;
  
  transition: all 0.1s ease-in-out;
  z-index: 10;
  transition: height 0.3s ease-in-out;

  .headerContent {
    gap: 12px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease-in-out;
    // background-color: map-get($colors, PlainBackground);

    padding: 16px 16px 16px 16px;

    top: 0;
    left: 0;
    position: fixed;
    width: 100%;

    .title {
      opacity: 0;
      transition: all 0.1s ease-in-out;
      transform: translateY(20px);
      max-width: 80%;
      @include typography(title3B1, map-get($colors, textGrey900), false, true);
        line-height: 24px;
      
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: pre-wrap;
    }
  }

  .expandedHeader {
    padding: 76px 16px 16px 16px;
    .title {
      opacity: 1;
      transition: all 0.1s ease-in-out;
      @include typography(title1B2, map-get($colors, textGrey900), false, true);
      line-height: 32px;
    }
  }

  .headerContentInline {
    gap: 12px;
  }

  .backButton {
    display: flex;
    justify-content: start;
    margin-right: 0px !important;
    cursor: pointer;
  }

  .customBackIconWrapper {
    padding: 0;
    background-color: map-get($colors, PlainBackground);
    padding: 10px;
    border-radius: 50%;
    border: 1px solid transparent;
  }



  .inlineTitle {
    @include typography(title3B1, map-get($colors, primaryTextColor), false, true);
  }

  .subtitle {
    @include typography(subheading1B2, map-get($colors, secondaryTextColor), false, true);
  }
}

 .scrolled {
    .headerContent {
      background-color: map-get($colors, PlainBackground);
      .title {
        transform: translateY(0px);
        opacity: 1 !important;
      }
    }
    .customBackIconWrapper {
      border: 1px solid map-get($colors, BorderGrey100);
    }
  }
.tabsContainer {
  width: 100%;
}



.tabContentWrapper {
  top: 209px;
}

.contentWrapper {
  height: 100%;
  width: 100vw;
  max-width: 100vw;
  overflow-x: hidden;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  background-color: map-get($colors, SecondaryOffsetVariant);
}

.tabContent {
  min-height: 100vh;
  display: flex;
  transition: transform 0.3s ease;
}

.emptyTabContent {
  height: 100vh;
}

.content {
  box-sizing: border-box;
  min-width: 100%;
}

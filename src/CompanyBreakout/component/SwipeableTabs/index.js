import React, { useRef } from 'react';
import Tab from './_partials/Tab';
import styles from './index.scss';
import classNames from 'classnames';

const SwipeableTabs = ({
  activeTab = 0,
  subTab,
  setActiveTab = () => {},
  tabs = [],
  content,
  isEmpty = false,
  className,
  isScrolled,
}) => {
  const tabsRef = useRef(null);
  const startX = useRef(0);

  const handleTouchStart = e => {
    startX.current = e.touches[0].clientX;
  };

  const handleTouchEnd = e => {
    const endX = e.changedTouches[0].clientX;
    const diffX = startX.current - endX;

    if (Math.abs(diffX) > window.innerWidth / 4) {
      if (diffX > 0) {
        // Swipe left
        setActiveTab(prevIndex => Math.min(prevIndex + 1, tabs.length - 1));
      } else {
        // Swipe right
        setActiveTab(prevIndex => Math.max(prevIndex - 1, 0));
      }
    }
  };

  const handleTabClick = tab => {
    setActiveTab(tab);
  };

  return (
    <div className={styles.tabsContainer}>
      <div className={styles.tabContentWrapper}>
        {tabs.length ? (
          <Tab
            tabs={tabs}
            activeTab={activeTab}
            tabOnClick={handleTabClick}
            isScrolled={isScrolled}
          />
        ) : null}
        {subTab}
        <div className={classNames(styles.contentWrapper, className)}>
          <div
            className={classNames(styles.tabContent, {
              [styles.emptyTabContent]: isEmpty,
            })}
            ref={tabsRef}
            onTouchStart={handleTouchStart}
            onTouchEnd={handleTouchEnd}
            style={{
              transform: `translateX(-${activeTab * 100}%)`,
            }}
          >
            {content.map(item => (
              <div key={item.id} className={styles.content}>
                {item.children}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SwipeableTabs;

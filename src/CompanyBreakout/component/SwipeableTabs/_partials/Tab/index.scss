.tabContainer {
  z-index: 3;
  display: flex;
  z-index: 1;
  height: fit-content;
  padding: 0 16px;
  position: sticky;
  top: 62px;
  transition: .3s all ease-in-out;

  .tab {
    cursor: pointer;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-right: 16px;
    padding: 8px 16px 0px 0;


    @include typography(heading2B3, map-get($colors, tertiaryText), false, true);
    font-weight: 400;
    line-height: 20px;
    letter-spacing: -0.08px;


    &.activeTabClass {
      @include typography(heading2B3, map-get($colors, textGrey900),false, true);
      font-weight: 500;

      .tabContent {
        border-bottom: 2px solid map-get($colors, BorderPrimary);
      }
    }

    .tabContent {
      gap: 6px;
      display: flex;
      align-items: center;
      padding: 8px 0px 6px 0px;
      // padding: 0px 12px;
    }
    .icon {
      width: 20px;
      height: 20px;
    }
  }
}

.isScrolled {
  background-color: map-get($colors, PlainBackground);
}

.container {
  height: 60%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;

  padding: 24px;
  border-radius: 12px;
  background-color: map-get($colors, PlainBackground);
}

.logo {
  width: 200px;
  height: 114px;
}

.title {
  margin-top: 16px;
  @include typography(heading1B1, map-get($colors, textGrey900), false, true);
}

.subTitle {
  margin-top: 4px;
  @include typography(heading2B3, map-get($colors, secondaryTextSIPColor), false, true);
  font-weight: 400;
}
.container {
  gap: 4px;
  display: flex;
  align-items: center;

  padding-bottom: 16px;
  border-bottom: 1px solid map-get($colors, BorderGrey100);

  .companyIcon {
    width: 32px;
    height: 32px;
    background-color: map-get($colors, PureWhite);
  }

  .titleContainer {
    flex: 1;
    .title {
      @include typography(heading2B2, map-get($colors, NeutralStrong), false, true);
    }

    .exchange {
      margin-top: 2px;
      @include typography(text11B, map-get($colors, GreySecondary), false, true);
      font-weight: 400;
    }
  }
}
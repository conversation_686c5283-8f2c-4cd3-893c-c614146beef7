import React from 'react';
import styles from './index.scss';
import Header from './_partials/Header';
import Body from './_partials/Body';

function Card({
  name,
  exchange,
  pmlId,
  onInfoClick,
  title,
  description,
  ...item
}) {
  return (
    <div className={styles.container}>
      <Header
        name={name}
        exchange={exchange}
        id={pmlId}
        securityId={item.securityId}
      />
      <Body
        {...item}
        onInfoClick={onInfoClick}
        title={title}
        description={description}
      />
    </div>
  );
}

export default Card;

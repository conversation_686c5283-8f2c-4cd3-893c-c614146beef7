import React from 'react';
import purify from 'dompurify';
import Drawer, { ALIGNMENTS } from '../../../components/Drawer';
import CLOSE from '../../../assets/icons/Breakout/Close.png';
import CLOSE_DARK from '../../../assets/icons/Breakout/CloseDark.png';
import styles from './index.scss';
import { isDarkMode } from '../../../utils/commonUtils';

function DrawerContent({ description, isOpen, title, onClose, darkModeState }) {
  return (
    <Drawer
      isOpen={isOpen}
      title={title}
      showCross={false}
      className={styles.drawer}
      customHeadingColor={styles.drawerTitle}
      align={ALIGNMENTS.LEFT}
      onClose={onClose}
    >
      <img
        className={styles.closeIcon}
        onClick={onClose}
        src={darkModeState ? CLOSE_DARK : CLOSE}
        alt=""
      />
      <div
        className={styles.description}
        dangerouslySetInnerHTML={{
          __html: purify.sanitize(description, { sanitize: true }),
        }}
      />
    </Drawer>
  );
}

export default DrawerContent;

.description {
    padding: 16px;
    @include typography(heading2B3, map-get($colors, textGrey900), false, true);

    h3 {
      @include typography(heading1B1, map-get($colors, textGrey900), false, true);
    }

    strong {
      
      @include typography(text1R1B2, map-get($colors, textGrey900), false, true);
    }
  }

.drawerTitle {
  font-size: 24px !important;
  color: map-get($colors, textGrey900) !important;
  font-weight: 700 !important;
}

.closeIcon {
  position: absolute;
  top: 33px;
  right: 19px;
}

.drawer {
  h2 {
    max-width: 90% !important;
  }
}
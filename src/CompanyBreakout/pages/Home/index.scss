.container {
  background: linear-gradient(180deg, #1A344D 0%,rgba(16, 16, 16, 0.5),  #101010 100%);
  height: 100vh;
}

.noData {
  padding-top: 16px;
  height: 96vh;
}

.containerLight {
  background: linear-gradient(180deg, var(--Gradient-GradientPrimary, #C2DAF3) 0%, var(--Colors-Plain, #FFF) 100%);
}

.card {
  margin-top: 12px;
}

.list {
  padding: 0 16px 10px 16px;
}

.contentWrapper {
  border-radius: 0;
}


.drawer {
  

  .learnMore {
    margin-top: 8px;
    color: map-get($colors, TextLinkBlue);
    font-weight: 500;
  }
}


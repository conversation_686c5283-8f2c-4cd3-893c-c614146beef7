.container {
background: linear-gradient(180deg, #1A344D 0%,rgba(16, 16, 16, 0.5),  #101010 100%);
 height: 100vh;
}

.containerLight {
  background: linear-gradient(180deg, var(--Gradient-GradientPrimary, #C2DAF3) 0%, var(--Colors-Plain, #FFF) 100%);
}

.list {
  padding: 16px 16px 10px 16px;
}

.noData {
  padding-top: 16px;
  height: 96vh;
}

.drawer {
  .description {
    padding: 16px;
    @include typography(heading2B3, map-get($colors, textGrey900), false, true);
  }

  .learnMore {
    margin-top: 8px;
    color: map-get($colors, TextLinkBlue);
    font-weight: 500;
  }
}

.drawerTitle {
  font-size: 24px !important;
  color: map-get($colors, textGrey900) !important;
  font-weight: 700 !important;
}
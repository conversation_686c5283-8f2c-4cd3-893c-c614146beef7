import { EQ_HOST } from '../../config/urlConfig';

export const COMPANY_BREAKOUT_ROUTES = {
  HOME: '/home',
  COMPANY_BREAKOUT: '/view',
};

const getStaticHost = () => {
  if (__ENV__ === 'production') {
    return 'https://static.paytmmoney.com/data/v1/production/';
  }

  return 'https://static.paytmmoney.com/data/v1/staging/';
};

export const COMPANY_BREAKOUT_APIS = {
  GET_ALL_BREAKOUTS: query => `${EQ_HOST}data/v1/breakouts/latest${query}`,
  GET_BREAKOUT_DETAILS: query => `${EQ_HOST}data/v1/breakouts/details${query}`,
  GET_BREAKOUT_JSON: `${getStaticHost()}breakout-stocks-content.json`,
};

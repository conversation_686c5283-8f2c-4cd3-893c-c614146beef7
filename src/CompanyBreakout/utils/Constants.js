import { isDarkMode } from '../../utils/commonUtils';
import FEEDBACK from '../../assets/icons/Breakout/feedback.png';

export const LEARN_MORE = 'Learn More';
export const SHARE_YOUR_FEEDBACK = 'Share your feedback';
export const BREAKOUTS = 'Breakouts';

let PROFIT;
let LOSS;
let INFO;
let TIMELINEBAR;
let FILTERS;

if (isDarkMode()) {
  PROFIT = require('../../assets/icons/Breakout/ProfitDark.png');
  LOSS = require('../../assets/icons/Breakout/LossDark.png');
  INFO = require('../../assets/icons/Breakout/InfoDark.png');
  TIMELINEBAR = require('../../assets/icons/Breakout/TimeLineBarLight.svg');
  FILTERS = require('../../assets/icons/Breakout/filtersDark.svg');
} else {
  PROFIT = require('../../assets/icons/Breakout/ProfitLight.png');
  LOSS = require('../../assets/icons/Breakout/LossLight.png');
  INFO = require('../../assets/icons/Breakout/InfoLight.png');
  TIMELINEBAR = require('../../assets/icons/Breakout/TimeLineBarLight.svg');
  FILTERS = require('../../assets/icons/Breakout/filtersLight.svg');
}

export { PROFIT, LOSS, INFO, TIMELINEBAR, FEEDBACK, FILTERS };

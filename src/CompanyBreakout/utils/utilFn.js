import { sendAnalyticsEvent } from '../../services/coreUtil';

export const formatTimestamp = timestamp => {
  if (!timestamp) return '';
  const [datePart, timePart] = timestamp.split(' ');

  const [day, month, year] = datePart.split('-');

  const [hours, minutes] = timePart.split(':');

  const date = new Date(year, month - 1, day, hours, minutes);

  const formattedDate = new Intl.DateTimeFormat('en-GB', {
    day: 'numeric',
    month: 'short',
  }).format(date);

  const formattedTime = new Intl.DateTimeFormat('en-US', {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  }).format(date);

  return `${formattedDate}, ${formattedTime}`;
};

export const sendBreakoutAnalyticsEvent = (screenName, eventData) => {
  const data = {
    ...eventData,
  };

  sendAnalyticsEvent(screenName, data);
};

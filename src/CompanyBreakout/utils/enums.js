export const BREAKOUT_TABS = [
  {
    id: 'all',
    name: 'All',
    key: 'bTrends',
    value: 'ALL',
  },
  {
    id: 'bullish',
    name: 'Bullish',
    key: 'bTrends',
    value: 'BULLISH',
  },
  {
    id: 'bearish',
    name: 'Bear<PERSON>',
    key: 'bTrends',
    value: 'BEARISH',
  },
];

export const HOME_BREAKOUT_TABS = [
  {
    id: 'intraday',
    name: 'Intraday',
    key: 'bTypes',
    value: 'INTRADAY',
  },
  {
    id: 'medium',
    name: 'Medium',
    key: 'bTypes',
    value: 'MEDIUM',
  },
  {
    id: 'long',
    name: 'Long',
    key: 'bTypes',
    value: 'LONG',
  },
  {
    id: 'short',
    name: 'Short',
    key: 'bTypes',
    value: 'SHORT',
  },
];

export const FILTER_INFO =
  'Medium-term breakouts span weeks to a few months, capturing sustained trend movements.';

export const ALL_BREAKOUTS = 'All Breakouts';
export const ALL_BREAKOUTS_SUBTITLE = 'Spot key stock moves for trading.';

export const FILTER_LIST = [
  { name: 'All', key: 'bTrends', value: '' },
  { name: 'Bullish', key: 'bTrends', value: 'BULLISH' },
  { name: 'Bearish', key: 'bTrends', value: 'BEARISH' },
];

export const NO_DATA = {
  TITLE: 'No breakouts found',
  DESCRIPTION:
    "Looks like there are no breakouts here. Why don't you check out other categories",
};

export const COMPANY_BREAKOUT_EVENTS = {
  ALL_BREAKOUT_SCREEN_NAME: '/allbreakouts',
  ALL_BREAKOUT_VERTICAL_NAME: 'allbreakouts',
  CLICKED_EVENT_ACTION: 'allbreakouts_clicked',
  CATEGORY: 'stocks',
  ALL_BREAKOUT_INFO_ICON_CLICK: 'allbreakouts_info_clicked',
  ALL_BREAKOUT_FILTER_CLICK: 'allbreakouts_filter_clicked',
  ALL_BREAKOUT_LANDING_PAGE: 'allbreakouts_screenopen',
  COMPANY_PAGE_SCREEN_NAME: '/company_page_overview',
  COMPANY_PAGE_VERTICAL_NAME: 'companypage_2.0',
  COMPANY_PAGE_CATEGORY: 'companypage',
  COMPANY_PAGE_VIEW_ALL_LANDING: 'companypage_breakout_viewall_clicked',
};

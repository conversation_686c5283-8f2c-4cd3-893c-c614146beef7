import Play from '../../assets/icons/media/play.png';
import Pause from '../../assets/icons/media/pause.png';
import Mute from '../../assets/icons/media/mute.png';
import Volume from '../../assets/icons/media/volume.png';
import Close from '../../assets/icons/close.png';
import CloseDark from '../../assets/icons/closeDark.png';
import PlayCircular from '../../assets/icons/play.png';
import PlayCircularInverted from '../../assets/icons/playCircularInverted.png';
import Share from '../../assets/icons/share.png';
import backIcon from '../../assets/icons/backIcon.png';
import backIconDarkMode from '../../assets/icons/backIcon_darkMode.png';
import checkedImg from '../../assets/images/checkbox-checked.png';
import unCheckedImg from '../../assets/images/checkbox-unchecked.png';
import Forward from '../../assets/icons/shareIcon.png';
import BlueTick from '../../assets/icons/blue_tick.svg';
import SkyBlueTick from '../../assets/icons/sky_blue_tick.svg';
import CheckboxChecked from '../../assets/icons/checkbox_checked.png';
import CheckboxCheckedDarkBlue from '../../assets/icons/checkbox-checked-blue-dark.png';
import CheckboxUnchecked from '../../assets/icons/checkbox_unchecked.png';
import CheckboxUncheckedDark from '../../assets/icons/checkbox-unchecked.png';
import InfoIconGrey from '../../assets/icons/info_icon_grey.svg';
import arrowBottomDark from '../../assets/icons/arrowBottomDark.png';
import arrowTopDark from '../../assets/icons/arrowTopDark.png';
import arrowBottomLight from '../../assets/icons/arrowBottomLight.png';
import arrowTopLight from '../../assets/icons/arrowTopLight.png';
import ArrowBack from '../../assets/icons/arrow-back.svg';
import ArrowBackDarkMode from '../../assets/icons/arrow-back-darkmode.svg';
import CloseCircle from '../../assets/icons/closed-circle.svg';
import CloseCircleDarkMode from '../../assets/icons/closed-circle-darkmode.svg';
import Search from '../../assets/icons/search-icon.svg';
import SearchDarkMode from '../../assets/icons/search-icon-darkmode.svg';
import InfoIcon from '../../assets/icons/info-icon.svg';
import InfoIconDarkMode from '../../assets/icons/info-icon-grey.svg';
import NoHoldingsFallback from '../../assets/icons/no-holdings.svg';
import NoHoldingsFallbackDarkMode from '../../assets/icons/no-holdings-darkmode.svg';
import DematHoldingErrorPage from '../../assets/icons/demat-holding-error-page.svg';
import DematHoldingErrorPageDarkMode from '../../assets/icons/demat-holding-error-page-darkmode.svg';

const THEME = {
  DARK: 'DARK',
  LIGHT: 'LIGHT',
};

const ICONS_NAME = {
  PLAY: 'PLAY',
  PAUSE: 'PAUSE',
  MUTE: 'MUTE',
  VOLUME: 'VOLUME',
  CLOSE: 'CLOSE',
  PLAY_CIRCULAR: 'PLAY_CIRCULAR',
  PLAY_CIRCULAR_INVERTED: 'PLAY_CIRCULAR_INVERTED',
  SHARE: 'SHARE',
  BACK: 'BACK',
  CHECKED: 'CHECKED',
  UNCHECKED: 'UNCHECKED',
  FORWARD: 'FORWARD',
  BLUE_TICK: 'BLUE_TICK',
  CHECKBOX_CHECKED: 'CHECKBOX_CHECKED',
  CHECKBOX_CHECKED_BLUE_DARK: 'CHECKBOX_CHECKED_BLUE_DARK',
  CHECKBOX_UNCHECKED: 'CHECKBOX_UNCHECKED',
  INFO_ICON_GREY: 'INFO_ICON_GREY',
  SKY_BLUE_TICK: 'SKY_BLUE_TICK',
  ARROW_UP: 'ARROW_UP',
  ARROW_DOWN: 'ARROW_DOWN',
  ARROW_BACK: 'ARROW_BACK',
  CLOSE_CIRCLE: 'CLOSE_CIRCLE',
  SEARCH: 'SEARCH',
  INFO_ICON: 'INFO_ICON',
  NO_HOLDINGS: 'NO_HOLDINGS',
  DEMAT_HOLDING_FALLBACK: 'DEMAT_HOLDING_FALLBACK',
};

const ICONS = {
  [THEME.LIGHT]: {
    [ICONS_NAME.PLAY]: Play,
    [ICONS_NAME.PAUSE]: Pause,
    [ICONS_NAME.MUTE]: Mute,
    [ICONS_NAME.VOLUME]: Volume,
    [ICONS_NAME.CLOSE]: Close,
    [ICONS_NAME.PLAY_CIRCULAR]: PlayCircular,
    [ICONS_NAME.PLAY_CIRCULAR_INVERTED]: PlayCircularInverted,
    [ICONS_NAME.SHARE]: Share,
    [ICONS_NAME.BACK]: backIcon,
    [ICONS_NAME.CHECKED]: checkedImg,
    [ICONS_NAME.UNCHECKED]: unCheckedImg,
    [ICONS_NAME.FORWARD]: Forward,
    [ICONS_NAME.BLUE_TICK]: BlueTick,
    [ICONS_NAME.CHECKBOX_CHECKED]: CheckboxChecked,
    [ICONS_NAME.CHECKBOX_CHECKED_BLUE_DARK]: CheckboxCheckedDarkBlue,
    [ICONS_NAME.CHECKBOX_UNCHECKED]: CheckboxUnchecked,
    [ICONS_NAME.INFO_ICON_GREY]: InfoIconGrey,
    [ICONS_NAME.SKY_BLUE_TICK]: SkyBlueTick,
    [ICONS_NAME.ARROW_UP]: arrowTopDark,
    [ICONS_NAME.ARROW_DOWN]: arrowBottomDark,
    [ICONS_NAME.ARROW_BACK]: ArrowBack,
    [ICONS_NAME.CLOSE_CIRCLE]: CloseCircle,
    [ICONS_NAME.SEARCH]: Search,
    [ICONS_NAME.INFO_ICON]: InfoIcon,
    [ICONS_NAME.NO_HOLDINGS]: NoHoldingsFallback,
    [ICONS_NAME.DEMAT_HOLDING_FALLBACK]: DematHoldingErrorPage,
  },
  [THEME.DARK]: {
    [ICONS_NAME.PLAY]: Play,
    [ICONS_NAME.PAUSE]: Pause,
    [ICONS_NAME.MUTE]: Mute,
    [ICONS_NAME.VOLUME]: Volume,
    [ICONS_NAME.CLOSE]: CloseDark,
    [ICONS_NAME.PLAY_CIRCULAR]: PlayCircular,
    [ICONS_NAME.PLAY_CIRCULAR_INVERTED]: PlayCircularInverted,
    [ICONS_NAME.SHARE]: Share,
    [ICONS_NAME.BACK]: backIconDarkMode,
    [ICONS_NAME.CHECKED]: checkedImg,
    [ICONS_NAME.UNCHECKED]: unCheckedImg,
    [ICONS_NAME.FORWARD]: Forward,
    [ICONS_NAME.BLUE_TICK]: BlueTick,
    [ICONS_NAME.CHECKBOX_CHECKED]: CheckboxChecked,
    [ICONS_NAME.CHECKBOX_CHECKED_BLUE_DARK]: CheckboxCheckedDarkBlue,
    [ICONS_NAME.CHECKBOX_UNCHECKED]: CheckboxUncheckedDark,
    [ICONS_NAME.INFO_ICON_GREY]: InfoIconGrey,
    [ICONS_NAME.SKY_BLUE_TICK]: SkyBlueTick,
    [ICONS_NAME.ARROW_UP]: arrowTopLight,
    [ICONS_NAME.ARROW_DOWN]: arrowBottomLight,
    [ICONS_NAME.ARROW_BACK]: ArrowBackDarkMode,
    [ICONS_NAME.CLOSE_CIRCLE]: CloseCircleDarkMode,
    [ICONS_NAME.SEARCH]: SearchDarkMode,
    [ICONS_NAME.INFO_ICON]: InfoIconDarkMode,
    [ICONS_NAME.NO_HOLDINGS]: NoHoldingsFallbackDarkMode,
    [ICONS_NAME.DEMAT_HOLDING_FALLBACK]: DematHoldingErrorPageDarkMode,
  },
};

const STATICS = {
  SIZE_MULTIPLIER: 5,
};

const LOGO_SIZE = {
  MEDIUM: 'MEDIUM',
  LARGE: 'LARGE',
  AVERAGE: 'AVERAGE',
};

export { ICONS_NAME, STATICS, THEME, LOGO_SIZE, ICONS as default };

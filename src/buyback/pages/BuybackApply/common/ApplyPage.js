import React, { useState, useEffect, useRef } from 'react';

import HeaderLayout from '../../../../layout/HeaderLayout/HeaderLayout';
import QuantityInput from '../../../../components/QuantityInput';
import Button from '../../../../components/Button/Button';
import ImportantInfo from '../../../../components/ImportantInfo/ImportantInfo';
import Icon from '../../../../components/Icon';
import Tooltip, { DIRECTIONS } from '../../../components/Tooltip';
import CancelDrawer from '../../../components/CancelDrawer/CancelDrawer';

import history from '../../../../history';
import {
  openPaytmMoneyPaymentAndGenericWebActivity,
  isH5,
} from '../../../../utils/bridgeUtils';
import { CDSL_URL } from '../../../../config/urlConfig';
import { useBackPress } from '../../../../utils/react';
import {
  processQuarryPrams,
  openWindowWithPost,
} from '../../../../utils/commonUtils';

import { STATIC_DATA, TOOLTIP } from './enums';

import {
  applyBuyback,
  confirmBuyback,
  getUpdatedStatus,
} from '../../../../actions/buybackActions';
import {
  setRootError,
  setLoaderView,
} from '../../../../actions/genericActions';

import InfoIcon from '../../../../assets/icons/Info_Icon.png';
import SuccessIcon from '../../../../assets/icons/success.png';

import styles from './ApplyPage.scss';

const ApplyPage = ({
  state,
  parentProps,
  goToBack,
  navigateToOrderDetails,
}) => {
  /** This reqId we are using to check if this page is redirected from CDSL. In normal flow it will be `null`  */
  const query = history.location?.search || null;
  const reqId = query ? processQuarryPrams(query).ReqId : null;

  const {
    pmlId,
    buybackId,
    stockName,
    offerPrice,
    eligibleQty,
    recordDateHolding,
    freeHolding,
    utilizedQty,
  } = state;

  const [quantity, setQuantity] = useState(eligibleQty);
  const [isTradingBalanceError, setIsTradingBalanceError] = useState(false);
  const [isMaxQuantityError, setIsMaxQuantityError] = useState(false);
  const [isAuthorisationSuccess, setIsAuthorisationSuccess] = useState(false);
  const [placeOrderData, setPlaceOrderData] = useState({});
  const [isCancelDrawerOpen, setIsCancelDrawerOpen] = useState(false);
  const cdslRef = useRef(null);
  const { pushStack, clearStack, popStack } = useBackPress();

  const closeCancelDrawer = () => {
    setIsCancelDrawerOpen(false);
    pushStack(() => {
      setIsCancelDrawerOpen(true);
    });
  };

  const handleBack = () => {
    popStack();
  };

  const checkCDSLStatus = id => {
    getUpdatedStatus(id).then(res => {
      if (res?.data?.isin_list?.[0]?.status) {
        setIsAuthorisationSuccess(true);
        window.scroll(0, 0);
        pushStack(() => setIsCancelDrawerOpen(true));
      } else {
        setRootError({ message: res?.meta?.displayMessage });
      }
    });
  };

  useEffect(() => {
    /** In web, if page is redirected from CDSL, then we will show empty page with loader and will set reqId to localstorage */
    if (!isH5() && reqId) {
      setLoaderView(true);
      localStorage.setItem('reqId', reqId);
    }

    pushStack(goToBack);

    const getStatus = () => {
      const cdslData = localStorage.getItem('reqId');
      if (cdslData) {
        cdslRef.current.close();
        localStorage.removeItem('reqId');
        checkCDSLStatus(cdslData);
      }
    };

    if (!isH5()) {
      window.addEventListener('storage', getStatus);
    }
    return () => {
      clearStack();
    };
  }, []);

  const handleQuantityInc = () => {
    if (quantity < eligibleQty) {
      setQuantity(+quantity + 1);
      if (isMaxQuantityError) {
        setIsMaxQuantityError(false);
      }
    }
  };

  const handleQuantityDec = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
      if (isMaxQuantityError) {
        setIsMaxQuantityError(false);
      }
    }
  };

  const quantityChangeHandler = e => {
    setQuantity(e.target.value);
    if (isMaxQuantityError) {
      setIsMaxQuantityError(false);
    }
  };

  const renderAuthorisationSuccess = () => (
    <div className={styles.authorisationSuccessContainer}>
      <div className={styles.successText}>
        <img alt="" src={SuccessIcon} className={styles.successIcon} />
        {STATIC_DATA.AUTH_SUCCESS_HEADER}
      </div>
      <div className={styles.info}>{STATIC_DATA.AUTH_SUCCESS_TEXT}</div>
    </div>
  );

  const renderQuantityDetails = () => {
    const renderTooltipContent = () => (
      <div>
        <div className={styles.tooltipRow}>
          <div className={styles.label}>{TOOLTIP.HOLDING_ON_RECORD_DATE}</div>
          <div className={styles.value}>{recordDateHolding}</div>
        </div>
        <div className={styles.tooltipRow}>
          <div className={styles.label}>{TOOLTIP.AVAILABLE_HOLDINGS}</div>
          <div className={styles.value}>{freeHolding}</div>
        </div>
        <div className={styles.tooltipRow}>
          <div className={styles.label}>{TOOLTIP.ALREADY_APPLIED_HOLDINGS}</div>
          <div className={styles.value}>{utilizedQty}</div>
        </div>
        <div className={styles.tooltipRow}>
          <div className={styles.label}>{TOOLTIP.ELIGIBLE_HOLDINGS}</div>
          <div className={styles.value}>{eligibleQty}</div>
        </div>
      </div>
    );

    return (
      <div className={styles.quantityDetailsContainer}>
        <div className={styles.eligibleQuantity}>
          <div>
            {STATIC_DATA.ELIGIBLE_QTY}: {eligibleQty}
          </div>
          <div className={styles.tooltipContainer}>
            <Tooltip
              placement={DIRECTIONS.BOTTOM}
              className={styles.tooltip}
              content={renderTooltipContent()}
            >
              <img alt="" src={InfoIcon} className={styles.infoIcon} />
            </Tooltip>
          </div>
        </div>
        <QuantityInput
          label={STATIC_DATA.QUANTITY}
          quantity={quantity}
          handleQuantityDec={handleQuantityDec}
          handleQuantityInc={handleQuantityInc}
          pricePerShare={+offerPrice}
          quantityChangeHandler={quantityChangeHandler}
          isMaxQuantityError={isMaxQuantityError}
        />
      </div>
    );
  };

  const renderImportantInfo = () => (
    <ImportantInfo
      title={STATIC_DATA.IMPORTANT_INFO_TITLE}
      info={
        isAuthorisationSuccess
          ? STATIC_DATA.IMPORTANT_INFO_CONFIRMATION
          : STATIC_DATA.IMPORTANT_INFO
      }
      className={styles.importantInfoContainer}
      isAccordion
      isAccordionOpen
    />
  );

  const renderTradingBalanceError = () => (
    <div
      className={styles.tradingBalanceContainer}
      id="trading-balance-container"
    >
      {STATIC_DATA.TRADING_BALANCE_ERROR}
    </div>
  );

  const renderPageContent = () => (
    <>
      {isAuthorisationSuccess && renderAuthorisationSuccess()}
      <div className={styles.mainContainer}>
        {renderQuantityDetails()}
        {renderImportantInfo()}
        {isTradingBalanceError && renderTradingBalanceError()}
      </div>
    </>
  );

  const displaySubHeader = () => (
    <div className={styles.subHeaderContainer}>
      <Icon name={pmlId} companyName={stockName} />
      <div className={styles.title}>{stockName}</div>
    </div>
  );

  const showTradingBalanceError = () => {
    setIsTradingBalanceError(true);
    const tradingBalanceContainer = document.getElementById(
      'trading-balance-container',
    );
    tradingBalanceContainer.scrollIntoView();
  };

  const placeOrder = async () => {
    const { buyback_id, collection_id } = placeOrderData;
    const params = {
      buyback_id,
      collection_id,
    };
    const response = await confirmBuyback(params, parentProps.axiosSource);

    if (response?.data && Object.keys(response.data).length > 0) {
      navigateToOrderDetails(response.data, pmlId, offerPrice);
    }
  };

  const makeCDSLAuthCall = data => {
    const { param, redirection_url: webViewUrl } = data;
    if (isH5()) {
      const postData = {
        webViewUrl,
        webViewType: 'CDSL',
        webViewTitle: 'Authorization',
        redirectionUrl: CDSL_URL.VERIFY_PIN,
        param,
      };
      openPaytmMoneyPaymentAndGenericWebActivity(postData, result => {
        if (result && result?.data) {
          checkCDSLStatus(param.ReqId);
        }
      });
    } else {
      cdslRef.current = openWindowWithPost({
        url: webViewUrl,
        params: param,
        target: '_blank',
      });
    }
  };

  const applyClickHandler = async () => {
    const params = {
      buyback_id: buybackId,
      qty: quantity,
      product_id: 'C',
    };
    const data = await applyBuyback(params, parentProps.axiosSource);
    if (data.tradingBalanceError) {
      showTradingBalanceError();
    } else if (data.maxQtyError) {
      window.scroll(0, 0);
      setIsMaxQuantityError(true);
    }

    if (data?.data && Object.keys(data.data).length > 0) {
      makeCDSLAuthCall(data.data);
      setPlaceOrderData({
        buyback_id: data.data.buyback_id,
        collection_id: data.data.collection_id,
      });
    }
  };

  const displayFooter = () => (
    <div className={styles.buttonFooter}>
      <Button
        buttonText={
          isAuthorisationSuccess ? STATIC_DATA.CONFIRM : STATIC_DATA.PROCEED
        }
        isPrimary
        isDisabled={
          !Number.isFinite(+quantity) ||
          +quantity <= 0 ||
          isTradingBalanceError ||
          isMaxQuantityError
        }
        onClickHandler={isAuthorisationSuccess ? placeOrder : applyClickHandler}
      />
    </div>
  );

  const headerProps = {
    heading: STATIC_DATA.TITLE,
    disableShadow: true,
    customClass: styles.headerCustomClass,
    subHeader: displaySubHeader(),
    onClickHandler: handleBack,
  };

  /** Show empty page if it is redirected from CDSL */
  if (reqId) {
    return null;
  }

  return (
    <HeaderLayout
      {...headerProps}
      {...parentProps}
      helperIconClick={() => {}}
      footer={displayFooter()}
    >
      {renderPageContent()}

      <CancelDrawer
        isDrawerOpen={isCancelDrawerOpen}
        onCancelOrder={goToBack}
        closeDrawer={closeCancelDrawer}
      />
    </HeaderLayout>
  );
};

export default ApplyPage;

import axios from 'axios';
import { COMPANY_BREAKOUT_APIS } from '../CompanyBreakout/config/urlConfig';
import { getGenericAppHeaders, makeApiGetCall } from '../utils/apiUtil';
import { AxiosErrorHandler } from '../utils/errorUtils';
import { setLoaderView } from './genericActions';

export const getLatestBreakouts = async queryParams => {
  const url = COMPANY_BREAKOUT_APIS.GET_ALL_BREAKOUTS(queryParams);

  const headers = getGenericAppHeaders();
  try {
    setLoaderView(true);
    const response = await makeApiGetCall({
      url,
      headers,
    });
    setLoaderView(false);
    return response.data;
  } catch (error) {
    if (error?.response?.status) {
      if (!axios.isCancel(error)) {
        AxiosErrorHandler(error, false, false, true);
        return {};
      }
      return { error };
    }
  }
};

export const getBreakoutDetails = async queryParams => {
  const url = COMPANY_BREAKOUT_APIS.GET_BREAKOUT_DETAILS(queryParams);
  const headers = getGenericAppHeaders();

  try {
    setLoaderView(true);
    const response = await makeApiGetCall({
      url,
      headers,
    });
    setLoaderView(false);
    return response.data;
  } catch (error) {
    if (error?.response?.status) {
      if (!axios.isCancel(error)) {
        AxiosErrorHandler(error, false, false, true);
        return {};
      }
      return { error };
    }
  }
};

export const getBreakoutJSON = async () => {
  const url = COMPANY_BREAKOUT_APIS.GET_BREAKOUT_JSON;
  const headers = getGenericAppHeaders();
  const response = await makeApiGetCall({ url, headers });

  return response.data;
};

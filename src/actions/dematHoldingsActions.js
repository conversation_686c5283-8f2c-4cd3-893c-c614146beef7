import axios from 'axios';
import { getGenericAppHeaders, makeApiGetCall } from '../utils/apiUtil';
import { AxiosErrorHandler } from '../utils/errorUtils';
import { DEMAT_HOLDINGS_APIS } from '../DematHoldings/config/urlConfig';

export const fetchDematHoldingsData = async (
  axiosSource,
  pageNo = 0,
  pageSize = 20,
) => {
  const url = DEMAT_HOLDINGS_APIS.GET_DEMAT_HOLDINGS;
  const headers = getGenericAppHeaders();

  try {
    const response = await makeApiGetCall({
      url,
      headers,
      axiosSource,
      queryParams: {
        page_no: pageNo,
        page_size: pageSize,
      },
    });
    return response?.data;
  } catch (error) {
    if (!axios.isCancel(error)) {
      AxiosErrorHandler(error, false, false, true);
      return {};
    }
  }
};

{"name": "web", "version": "0.0.0", "private": true, "engines": {"node": ">=12.13.0", "npm": ">=7.6.1"}, "browserslist": [">1%", "last 4 versions", "Firefox ESR", "not ie < 9"], "dependencies": {"@babel/polyfill": "^7.0.0-beta.42", "@chainlit/react-client": "^0.2.4", "axios": "^0.19.0", "chart.js": "^4.3.0", "chart.js-plugin-labels-dv": "^4.0.0", "chartjs-plugin-zoom": "^2.0.1", "classnames": "^2.2.5", "cross-env": "^7.0.2", "dompurify": "^3.2.4", "express": "^4.16.3", "history": "4.7.2", "isomorphic-style-loader": "^5.1.0", "lodash": "^4.17.10", "moment": "^2.30.1", "prop-types": "^15.6.1", "query-string": "^6.0.0", "react": "^16.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^16.2.0", "react-lottie": "^1.2.3", "react-slick": "^0.29.0", "recoil": "^0.7.7", "rxjs": "^7.5.6", "slick-carousel": "^1.8.1", "universal-router": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.10.1", "@babel/plugin-proposal-optional-chaining": "^7.10.1", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-jsx": "^7.14.5", "@babel/plugin-transform-react-jsx": "^7.14.5", "@babel/plugin-transform-runtime": "^7.12.1", "@babel/preset-env": "^7.1.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.0.0", "@hot-loader/react-dom": "^16.13.0", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^12.0.0", "@testing-library/react-hooks": "^7.0.1", "autoprefixer": "^10.3.4", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^9.0.0", "babel-jest": "^23.4.2", "babel-loader": "^8.2.2", "babel-plugin-module-resolver": "^4.1.0", "chalk": "^4.0.0", "compression-webpack-plugin": "^8.0.1", "css-loader": "^6.2.0", "css-minimizer-webpack-plugin": "^3.0.2", "eslint": "^4.19.0", "eslint-config-airbnb": "^16.1.0", "eslint-config-prettier": "^2.9.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-css-modules": "^2.7.5", "eslint-plugin-flowtype": "^2.46.1", "eslint-plugin-import": "^2.9.0", "eslint-plugin-jsx-a11y": "^6.0.3", "eslint-plugin-prettier": "^2.6.0", "eslint-plugin-react": "^7.7.0", "express-static-gzip": "^2.1.0", "html-webpack-plugin": "^5.3.2", "husky": "^4.3.0", "identity-obj-proxy": "^3.0.0", "jest": "^22.4.3", "jest-dom": "^4.0.0", "lint-staged": "^7.3.0", "mini-css-extract-plugin": "^2.1.0", "mutation-observer": "^1.0.3", "node-sass": "^6.0.1", "pixrem": "^4.0.1", "pleeease-filters": "^4.0.0", "postcss-calc": "^6.0.1", "postcss-color-function": "^4.0.1", "postcss-custom-media": "^6.0.0", "postcss-custom-properties": "^7.0.0", "postcss-custom-selectors": "^4.0.1", "postcss-flexbugs-fixes": "^3.3.0", "postcss-import": "^11.1.0", "postcss-media-minmax": "^3.0.0", "postcss-nested": "^3.0.0", "postcss-nesting": "^4.2.1", "postcss-selector-matches": "^3.0.1", "postcss-selector-not": "^3.0.1", "prettier": "^1.11.1", "react-deep-force-update": "^2.1.1", "react-hot-loader": "^4.12.20", "replace-in-file-webpack-plugin": "^1.0.6", "rimraf": "^3.0.2", "sass-loader": "^12.1.0", "sass-resources-loader": "^2.2.3", "string-replace-loader": "^3.1.0", "style-loader": "^3.2.1", "stylelint": "^9.1.3", "stylelint-config-standard": "^18.2.0", "stylelint-order": "^0.8.1", "webpack": "^5.47.1", "webpack-assets-manifest": "^5.0.6", "webpack-bundle-analyzer": "^4.4.2", "webpack-cli": "^4.7.2", "webpack-dev-server": "^3.11.2", "webpack-merge": "^5.8.0", "workbox-webpack-plugin": "^6.1.5"}, "scripts": {"start": "cross-env NODE_ENV=staging webpack serve --env env=dev", "start-desktop": "cross-env NODE_ENV=staging webpack serve --env env=dev desktop", "dev:bundleanalyzer": "cross-env NODE_ENV=staging npm run dev -- --env addons=bundleanalyzer", "build-desktop": "cross-env NODE_ENV=staging webpack --mode production --env env=prod desktop", "build-mWeb": "cross-env NODE_ENV=staging webpack --mode production --env env=prod mWeb", "build-mobile": "cross-env NODE_ENV=staging webpack --mode production --env env=prod", "build": "npm run build-desktop && npm run build-mobile && npm run build-mWeb", "build-prod-dev": "cross-env NODE_ENV=staging webpack --mode production --env env=prod release noconsole", "build-prod-desktop": "cross-env NODE_ENV=production webpack --mode production --env env=prod release desktop", "build-prod-mWeb": "cross-env NODE_ENV=production webpack --mode production --env env=prod release mWeb", "build-prod-mobile": "cross-env NODE_ENV=production webpack --mode production --env env=prod release", "build-prod": "npm run build-prod-desktop && npm run build-prod-mobile && npm run build-prod-mWeb", "build-beta-desktop": "cross-env NODE_ENV=production webpack --mode production --env env=prod release desktop", "build-beta-mWeb": "cross-env NODE_ENV=production webpack --mode production --env env=prod release mWeb", "build-beta-mobile": "cross-env NODE_ENV=production webpack --mode production --env env=prod release", "build-beta": "npm run build-beta-desktop && npm run build-beta-mobile && npm run build-beta-mWeb", "build:bundleanalyzer": "npm run build-prod-dev -- --env addons=bundleanalyzer", "coverage": "jest --coverage", "serve": "node server.js", "serve-desktop": "node server-desktop.js", "lint": "eslint --ext .jsx --ext .js src/ --ignore-pattern node_modules/", "lint:fix": "eslint --ext .jsx --ext .js src/ --ignore-pattern node_modules/ --fix", "scss-lint": "scss-lint", "test": "jest --watch"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run lint"}}, "lint-staged": {"*.js": ["npm run lint:fix", "git add"]}}
#!/usr/bin/env node
//# vi: ft=javascript

"use strict";

var util = require('util');
var recluster = require('recluster');
var path = require('path');
var fs = require('fs');
var args = require('optimist').argv;
var http = require('http');
var flagReload = true;

function usage() {
    console.log('Usage : ' + process.argv[1] + ' <filename> ');
    console.log('example : cluster app.js');
}

function killAll(signal) {
    util.log('Received ' + signal + ' signal, signalling all worker processes...');
    process.kill(0, signal);
}


function heartbeat(cluster) {
    var port = args.p | 0;

    function reloadCluster(msg) {
        if (flagReload) {
            flagReload = false;
            util.log('reloading cluster instances ' + msg);
            cluster.reload();
            setTimeout(function () {
                flagReload = true;
            }, 10000);
        } else {
            util.log('multiple call reload ');
        }
    }

    if (port) {
        process.env.PORT = port;
        util.log('will monitor port ' + port + ' for heartbeat');
        setTimeout(function () {
            setInterval(function () {
                var request = http.get('http://localhost:' + port, function (res) {
                    request.setTimeout(0); // disable timeout on response.
                    if ([200, 302].indexOf(res.statusCode) == -1) {
                        reloadCluster('[heartbeat] : FAIL with code ' + res.statusCode);
                    } else {
                        util.log(' [heartbeat]:  OK [' + res.statusCode + ']');
                    }
                })
                    .on('error', function (err) {
                        reloadCluster(' [heartbeat]:  FAIL with ' + err.message);
                    });

                request.setTimeout(10000, function () {
                    // QZ: This is agressive reload on first failure. Later, we may change it 
                    // to reload on n consecutive failures
                    reloadCluster(' [heartbeat]: FAIL with timeout ');
                });

            }, 10000);
        }, 20000);
    }
}

function startApp(filename, workers) {

    var opts = { timeout: 30, respawn: 60 };
    if (process.env.NONSERVER) {
        opts.readyWhen = 'started';
    }

    if (workers) {
        opts.workers = workers;
    }
    var cluster = recluster(filename, opts);
    var sighupSent = false;
    var restartFile = process.env.RESTARTFILE || './public/system/restart';

    heartbeat(cluster); // this comes first as it may set the port
    cluster.run();

    process.on('SIGHUP', function () {
        if (!sighupSent) {
            sighupSent = true;
            killAll('SIGHUP');
            setTimeout(function () {
                sighupSent = false;
            }, 30000);
        }
    });

    process.on('SIGUSR2', function () {
        util.log('Restart signal received, reloading instances');
        cluster.reload();
    });

    process.on('SIGTERM', function () {
        util.log('TERM signal received, shutting down instances');
        cluster.terminate();
    });

    /**
      * Monitor the specified file for restart. If that file
      * is modified, shut down the current process instance.
      */
    fs.watchFile(restartFile, function (curr, prev) {
        util.log('Restart signal received, reloading instances');
        cluster.reload();
    });
}

(function main() {

    var argv = process.argv.slice(2);
    var filename = argv[0];
    var workers = args.n || null;
    if (argv.length == 0) {
        return usage();
    }

    console.log('starting ' + filename);

    fs.stat(filename, function (err, st) {

        if (/\.js$/.test(filename) == false) {
            filename += '.js';
        }

        if (filename[0] != '/') {
            filename = process.cwd() + '/' + filename;
        }

        startApp(filename, workers);
    });

}());

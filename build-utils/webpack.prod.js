const commonPaths = require('./common-paths');

const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CompressionPlugin = require('compression-webpack-plugin');
const { InjectManifest } = require('workbox-webpack-plugin');
const webpack = require('webpack');

const fs = require('fs');
const WebpackAssetsManifest = require('webpack-assets-manifest');

const APP_VERSION = require('./version');

const isDebug = !process.argv.includes('release');
const isDesktop = process.argv.includes('desktop');
const isMWeb = process.argv.includes('mWeb');
const buildPath = isDesktop ? 'desktop/' : isMWeb ? 'mweb/' : '';

const config = {
  name: 'client',
  target: 'web',

  mode: isDebug ? 'development' : 'production',
  entry: {
    app: [`${commonPaths.appEntry}/index.js`],
  },

  output: {
    filename: `${APP_VERSION}/${buildPath}static/js/[name].[chunkhash:8].js`,
    chunkFilename: `${APP_VERSION}/${buildPath}static/js/[name].[chunkhash:8].js`,
  },

  plugins: [
    new MiniCssExtractPlugin({
      filename: `${APP_VERSION}/${buildPath}static/css/[name].[chunkhash:8].css`,
      chunkFilename: `${APP_VERSION}/${buildPath}static/css/[id].[chunkhash:8].css`,
      ignoreOrder: true,
    }),
    new CompressionPlugin({
      filename: '[path][base].gz[query]',
      algorithm: 'gzip',
      test: /\.(js|css)$/,
    }),
    new webpack.DefinePlugin({
      __MAINVERSION__: true,
    }),
    new WebpackAssetsManifest({
      output: `${commonPaths.outputPath}/${APP_VERSION}/${buildPath}asset-manifest.json`,
      publicPath: true,
      writeToDisk: true,
      customize: entry => {
        // You can prevent adding items to the manifest by returning false.
        if (entry.key.toLowerCase().endsWith('.map')) return false;
        return entry;
      },
      done: (manifest, stats) => {
        // Write chunk-manifest.json.json
        const chunkFileName = `${commonPaths.outputPath}/${APP_VERSION}/${buildPath}chunk-manifest.json`;
        try {
          const fileFilter = file => !file.endsWith('.map');
          const addPath = file => manifest.getPublicPath(file);
          const chunkFiles = stats.compilation.chunkGroups.reduce((acc, c) => {
            acc[c.name] = [
              ...(acc[c.name] || []),
              ...c.chunks.reduce(
                (files, cc) => [
                  ...files,
                  ...cc.files.filter(fileFilter).map(addPath),
                ],
                [],
              ),
            ];
            return acc;
          }, Object.create(null));
          fs.writeFileSync(chunkFileName, JSON.stringify(chunkFiles, null, 2));
        } catch (err) {
          console.error(`ERROR: Cannot write ${chunkFileName}: `, err);
          if (!isDebug) process.exit(1);
        }
      },
    }),
    new InjectManifest({
      swDest: `${commonPaths.outputPath}/sw-new.js`,
      swSrc: './public/serviceworker.js',
      exclude: [/asset-manifest\.json$/, /\.gz$/, /src\/assets\//],
      chunks: [
        /* 'app', 'BuybackHome' */
      ],
    }),
    // new ReplaceInFileWebpackPlugin([
    //   {
    //     dir: `${commonPaths.outputPath}`,
    //     files: ['sw-new.js'],
    //     rules: [
    //       {
    //         search: '__MAINVERSION__',
    //         replace: `new${new Date().getTime()}`,
    //       },
    //     ],
    //   },
    // ]),
  ],
};

module.exports = config;

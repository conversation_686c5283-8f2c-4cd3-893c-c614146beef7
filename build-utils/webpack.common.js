const commonPaths = require('./common-paths');

const webpack = require('webpack');
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const { ModuleFederationPlugin } = require('webpack').container;
const { dependencies } = require('../package.json');

const isDebug = !process.argv.includes('release');
const removeConsoleLogs = process.argv.includes('noconsole');

const isDesktop = process.argv.includes('desktop');
const isMWeb = process.argv.includes('mWeb');
const indexPath = isDesktop ? 'desktop-' : isMWeb ? 'mweb-' : '';

const config = {
  entry: {},
  output: {
    path: commonPaths.outputPath,
    publicPath: 'auto',
    assetModuleFilename: isDebug
      ? 'images/[path][name].[contenthash:8][ext]'
      : 'images/[path][contenthash:8][ext]',
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        loader: 'string-replace-loader',
        options: {
          multiple: [
            {
              search: '__BUILD_PATH__',
              replace: isDesktop ? 'desktop' : 'mobile',
            },
            {
              search: /__IS_SERVICE_WORKER__/gi,
              replace: isDesktop || isMWeb ? 'false' : 'true',
            },
          ],
        },
      },
      {
        test: /\.(js)$/,
        exclude: /node_modules/,
        use: ['babel-loader'],
      },
      {
        test: /\.(png|jpe?g|gif|svg)$/i,
        type: 'asset/resource',
      },
      {
        test: /\.(sa|sc|c)ss$/,
        exclude: /node_modules\/(?!(slick-carousel)\/).*/,
        use: [
          MiniCssExtractPlugin.loader,
          {
            loader: 'css-loader',
            options: {
              modules: {
                mode: 'local',
                localIdentName: isDebug
                  ? '[name]-[local]-[contenthash:base64:5]'
                  : '[contenthash:base64:5]',
              },
              sourceMap: isDebug,
              importLoaders: 1,
            },
          },
          'sass-loader',
          {
            loader: 'sass-resources-loader',
            options: {
              resources: commonPaths.commonCssStyles,
            },
          },
        ],
      },
    ],
  },
  optimization: {
    minimize: !isDebug,
    minimizer: isDebug
      ? []
      : [
          new TerserPlugin({
            terserOptions: {
              sourceMap: true,
              compress: {
                inline: false,
                drop_console: !!removeConsoleLogs,
              },
            },
          }),
          new CssMinimizerPlugin({
            minimizerOptions: {
              preset: [
                'default',
                {
                  discardComments: { removeAll: true },
                },
              ],
            },
          }),
        ],
    runtimeChunk: false,
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        commons: {
          test: /[\\/]node_modules[\\/]/,
          priority: -10,
          reuseExistingChunk: true,
        },
        default: {
          minChunks: 2,
          priority: -20,
          reuseExistingChunk: true,
        },
      },
    },
    sideEffects: false,
  },
  plugins: [
    new HtmlWebpackPlugin({
      filename: `${indexPath}index.html`,
      template: `public/index.html`,
      favicon: `public/favicon.ico`,
      publicPath:
        (isDesktop || isMWeb) && process.env.NODE_ENV === 'staging'
          ? '/corporate-actions/'
          : '/',
    }),
    new webpack.DefinePlugin({
      __ENV__: `'${process.env.NODE_ENV}'`,
      __DEV__: isDebug,
    }),
    new webpack.ProvidePlugin({
      process: 'process/browser',
    }),
    new ModuleFederationPlugin({
      name: 'CorporateActions',
      filename: `${indexPath}moduleEntry.js`,
      exposes: {
        './CorporateActions': path.resolve(__dirname, '../src/initApp'),
        './MTFOnboarding': path.resolve(
          __dirname,
          '../src/mtf/pages/MTFOnboarding/MTFOnboardingModuleEntry/index.js',
        ),
        './MTFPledge': path.resolve(
          __dirname,
          '../src/mtf/pages/MTFPledge/MTFPledgeModuleEntry/index.js',
        ),
        './MTFScrips': path.resolve(
          __dirname,
          '../src/mtf/pages/MTFScrips/MTFScripsModuleEntry/index.js',
        ),
      },
      shared: {
        // ...dependencies,
        react: {
          singleton: true,
          requiredVersion: dependencies.react,
        },
        'react-dom': {
          singleton: true,
          requiredVersion: dependencies['react-dom'],
        },
      },
    }),
  ],
};

module.exports = config;
